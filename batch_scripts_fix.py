"""
This is a standalone script to fix the issue with batch generation of custom scripts.
Run this script to apply the fix to your main.py file.
"""

import os
import re
import shutil
import sys

def apply_fix():
    # Path to the main.py file
    main_py_path = os.path.join('src', 'main.py')
    
    # Check if the file exists
    if not os.path.exists(main_py_path):
        print(f"Error: Could not find {main_py_path}")
        return False
    
    # Create a backup of the original file
    backup_path = main_py_path + '.bak'
    shutil.copy2(main_py_path, backup_path)
    print(f"Created backup at {backup_path}")
    
    # Read the content of the file
    with open(main_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define the patterns to search for and their replacements
    patterns = [
        # First occurrence (success branch)
        (
            r'# Handle custom titles from batch file if enabled\s+custom_title = None\s+ai_custom_title = None\s+\s+if self\.batch_titles and self\.batch_current <= len\(self\.batch_titles\):',
            """# Handle custom scripts or titles from batch for subsequent videos
                    custom_title = None
                    ai_custom_title = None
                    
                    # Check if we're using custom scripts from folder
                    if self.batch_scripts and self.batch_current <= len(self.batch_scripts):
                        # Get the script and title for the current batch index
                        batch_script = self.batch_scripts[self.batch_current - 1]
                        
                        # Use the script content and title from the file
                        custom_script = batch_script['script']
                        custom_title = batch_script['title']
                        
                        # Force custom script mode
                        custom_script_enabled = True
                        self.use_custom_script_var.set(True)
                        self.toggle_custom_script()
                        
                        # Set the script in the editor for visibility
                        self.script_editor.delete(1.0, tk.END)
                        self.script_editor.insert(tk.END, custom_script)
                        
                        # Set the title in the entry for visibility
                        self.custom_title_entry.delete(0, tk.END)
                        self.custom_title_entry.insert(0, custom_title)
                        
                        self.update_status(f"Using script from file: {batch_script['file']}")
                    
                    # Check if we're using custom titles from file
                    elif self.batch_titles and self.batch_current <= len(self.batch_titles):"""
        ),
        # Second occurrence (error branch)
        (
            r'# Handle custom titles from batch file if enabled\s+custom_title = None\s+ai_custom_title = None\s+\s+if self\.batch_titles and self\.batch_current <= len\(self\.batch_titles\):',
            """# Handle custom scripts or titles from batch for subsequent videos
                    custom_title = None
                    ai_custom_title = None
                    
                    # Check if we're using custom scripts from folder
                    if self.batch_scripts and self.batch_current <= len(self.batch_scripts):
                        # Get the script and title for the current batch index
                        batch_script = self.batch_scripts[self.batch_current - 1]
                        
                        # Use the script content and title from the file
                        custom_script = batch_script['script']
                        custom_title = batch_script['title']
                        
                        # Force custom script mode
                        custom_script_enabled = True
                        self.use_custom_script_var.set(True)
                        self.toggle_custom_script()
                        
                        # Set the script in the editor for visibility
                        self.script_editor.delete(1.0, tk.END)
                        self.script_editor.insert(tk.END, custom_script)
                        
                        # Set the title in the entry for visibility
                        self.custom_title_entry.delete(0, tk.END)
                        self.custom_title_entry.insert(0, custom_title)
                        
                        self.update_status(f"Using script from file: {batch_script['file']}")
                    
                    # Check if we're using custom titles from file
                    elif self.batch_titles and self.batch_current <= len(self.batch_titles):"""
        )
    ]
    
    # Apply the replacements
    modified_content = content
    for i, (pattern, replacement) in enumerate(patterns):
        # Find all occurrences of the pattern
        matches = list(re.finditer(pattern, modified_content, re.DOTALL))
        
        if len(matches) >= 2:
            # Replace only the first and second occurrences
            if i == 0 and len(matches) >= 1:
                # Replace the first occurrence
                start, end = matches[0].span()
                modified_content = modified_content[:start] + replacement + modified_content[end:]
            elif i == 1 and len(matches) >= 2:
                # Replace the second occurrence
                # Need to find matches again because the content has changed
                matches = list(re.finditer(pattern, modified_content, re.DOTALL))
                if len(matches) >= 2:
                    start, end = matches[1].span()
                    modified_content = modified_content[:start] + replacement + modified_content[end:]
        else:
            print(f"Warning: Could not find pattern {i+1} in the file")
    
    # Write the modified content back to the file
    with open(main_py_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"Successfully applied the fix to {main_py_path}")
    print("The issue with batch generation of custom scripts should now be fixed.")
    print("You can now use the 'Use Custom Scripts from Folder' feature to generate videos from multiple script files.")
    
    return True

if __name__ == "__main__":
    print("Applying fix for batch generation of custom scripts...")
    if apply_fix():
        print("Fix applied successfully!")
    else:
        print("Failed to apply the fix. Please check the error messages above.")
