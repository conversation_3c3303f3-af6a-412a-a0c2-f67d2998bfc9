import os
from pydub import AudioSegment
from moviepy.editor import AudioFileClip
from moviepy.audio.AudioClip import AudioClip

def adjust_background_music_volume(narration_path, bg_music_path, target_diff=-15.0, output_dir=None):
    """
    Adjusts the volume of the background music so that it is target_diff dB quieter than the narration.

    Args:
        narration_path (str): Path to the narration audio file (MP3).
        bg_music_path (str): Path to the background music file (MP3).
        target_diff (float): Desired difference in dBFS (default: -15.0 dB, meaning background is 15 dB quieter than narration).
        output_dir (str, optional): Directory to save the adjusted background music file. If not provided, uses the directory of bg_music_path.

    Returns:
        str: Path to the adjusted background music audio file.
    """
    try:
        narration_audio = AudioSegment.from_file(narration_path, format="mp3")
        bg_music = AudioSegment.from_file(bg_music_path, format="mp3")

        narration_dbfs = narration_audio.dBFS
        bg_music_dbfs = bg_music.dBFS
        current_diff = bg_music_dbfs - narration_dbfs
        gain_adjustment = target_diff - current_diff
        adjusted_bg_music = bg_music.apply_gain(gain_adjustment)

        # Use output_dir if provided; otherwise, use the directory of bg_music_path
        if output_dir is None:
            output_dir = os.path.dirname(bg_music_path)

        os.makedirs(output_dir, exist_ok=True)
        temp_path = os.path.join(output_dir, "adjusted_bg_music.mp3")
        adjusted_bg_music.export(temp_path, format="mp3")
        return temp_path
    except Exception as e:
        print(f"Error adjusting background music volume: {str(e)}")
        # If there's an error, return the original music path
        return bg_music_path

def mix_audio_with_background(narration_path, bg_music_path, output_path, volume_ratio=0.2):
    """
    Mixes narration audio with background music at the specified volume ratio.

    Args:
        narration_path (str): Path to the narration audio file.
        bg_music_path (str): Path to the background music file.
        output_path (str): Path to save the mixed audio file.
        volume_ratio (float): Volume ratio for background music (0.0 to 1.0).
                             0.0 means no background music, 1.0 means equal volume.

    Returns:
        str: Path to the mixed audio file.
    """
    try:
        # Load audio files
        narration = AudioSegment.from_file(narration_path)
        bg_music = AudioSegment.from_file(bg_music_path)

        # Adjust background music volume based on ratio
        bg_music = bg_music - (1.0 - volume_ratio) * 20  # Convert ratio to dB reduction

        # Loop background music if it's shorter than narration
        if len(bg_music) < len(narration):
            loops_needed = int(len(narration) / len(bg_music)) + 1
            bg_music = bg_music * loops_needed

        # Trim background music to match narration length
        bg_music = bg_music[:len(narration)]

        # Mix narration and background music
        mixed_audio = narration.overlay(bg_music)

        # Export mixed audio
        mixed_audio.export(output_path, format="mp3")
        return output_path
    except Exception as e:
        print(f"Error mixing audio with background music: {str(e)}")
        # If there's an error, return the original narration path
        return narration_path


def add_audio_buffer(audio_clip, buffer_duration=0.2, fade_in_duration=0.1, fade_out_duration=0.12):
    """
    Adds a silent buffer at the end of an audio clip with smooth fade-in and fade-out transitions.
    This prevents audio glitches and ensures clean transitions between scenes.

    Args:
        audio_clip (AudioFileClip): The audio clip to add a buffer to.
        buffer_duration (float): Duration of the silent buffer in seconds (default: 0.2).
        fade_in_duration (float): Duration of the fade-in effect in seconds (default: 0.1).
        fade_out_duration (float): Duration of the fade-out effect in seconds (default: 0.12).

    Returns:
        AudioFileClip: The audio clip with smooth transitions and a silent buffer added at the end.
    """
    try:
        from moviepy.audio.AudioClip import concatenate_audioclips

        # Check if the audio clip is very short
        if audio_clip.duration < 0.5:
            # For very short clips, use minimal fades to avoid affecting content
            fade_in_duration = min(0.05, audio_clip.duration / 10)
            fade_out_duration = min(0.08, audio_clip.duration / 8)
            print(f"Very short audio clip detected ({audio_clip.duration:.2f}s), using minimal fades")
        else:
            # Ensure fade durations are not too long for the clip
            max_fade_duration = min(audio_clip.duration / 4, 0.3)  # Max 0.3s or 1/4 of clip duration
            fade_in_duration = min(fade_in_duration, max_fade_duration)
            fade_out_duration = min(fade_out_duration, max_fade_duration)

        print(f"Applying audio fade-in: {fade_in_duration:.2f}s, fade-out: {fade_out_duration:.2f}s")

        # Apply fade-in and fade-out to the audio clip for smooth transitions
        try:
            smoothed_audio = audio_clip.audio_fadein(fade_in_duration).audio_fadeout(fade_out_duration)
        except Exception as fade_error:
            print(f"Warning: Error applying audio fades: {str(fade_error)}")
            # If fades fail, return the original clip
            smoothed_audio = audio_clip

        # Create a silent clip with the same parameters as the input clip
        silent_clip = audio_clip.volumex(0).subclip(0, min(buffer_duration, audio_clip.duration))

        # Concatenate the smoothed audio clip with the silent buffer
        audio_with_buffer = concatenate_audioclips([smoothed_audio, silent_clip])

        return audio_with_buffer
    except Exception as e:
        print(f"Error adding audio buffer with smooth transitions: {str(e)}")
        # If there's an error, try to at least apply the fades without the buffer
        try:
            return audio_clip.audio_fadein(fade_in_duration).audio_fadeout(fade_out_duration)
        except Exception as fade_error:
            print(f"Error applying audio fades: {str(fade_error)}")
            # If all else fails, return the original audio clip
            return audio_clip
