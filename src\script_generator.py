import os
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import datetime
import ai_client
from utils import call_openai_api, STORY_TYPES

class ScriptGeneratorThread(threading.Thread):
    """Thread for script generation to avoid UI freezing"""
    def __init__(self, concept, script_type, callback, update_progress, llm_provider="Groq", word_count=150):
        threading.Thread.__init__(self)
        self.concept = concept
        self.script_type = script_type
        self.callback = callback
        self.update_progress = update_progress
        self.llm_provider = llm_provider
        self.word_count = word_count
        self.result = None

        # Add pause and stop flags
        self.paused = False
        self.pause_lock = threading.Lock()
        self.stop_requested = False

    def pause(self):
        """Pause the thread execution"""
        with self.pause_lock:
            self.paused = True
            self.callback("Script generation paused")

    def resume(self):
        """Resume the thread execution"""
        with self.pause_lock:
            self.paused = False
            self.callback("Script generation resumed")

    def stop(self):
        """Request the thread to stop execution"""
        self.stop_requested = True
        with self.pause_lock:
            self.paused = False  # Ensure we're not paused so we can process the stop request
        self.callback("Script generation stopping...")

    def _check_pause(self):
        """Check if thread is paused and wait if it is"""
        while True:
            with self.pause_lock:
                if not self.paused:
                    break
            # Check if stop was requested while paused
            if self.stop_requested:
                break
            # Sleep briefly to avoid busy waiting
            import time
            time.sleep(0.1)

    def run(self):
        # Initialize result to None at the start
        self.result = None

        try:
            # Update progress
            self.callback("Initializing script generation...")
            self.update_progress(10)

            # Check if paused or stopped
            self._check_pause()
            if self.stop_requested:
                self.result = {
                    "success": False,
                    "message": "Script generation stopped by user",
                    "script": None
                }
                return

            # Set the LLM provider
            self.callback(f"Using {self.llm_provider} as AI provider...")

            try:
                # Switch to the selected provider directly using ai_client
                provider_key = self.llm_provider.lower()
                try:
                    success = ai_client.set_provider(provider_key)
                    if success:
                        self.callback(f"Switched to {self.llm_provider} AI provider")
                    else:
                        self.callback(f"Error switching AI provider. Using Groq as fallback.")
                        ai_client.set_provider("groq")
                except Exception as e:
                    self.callback(f"Error switching AI provider: {str(e)}. Using Groq as fallback.")
                    try:
                        ai_client.set_provider("groq")
                    except:
                        pass  # If even Groq fails, we'll catch it in the next step

                # Get the current AI client
                client = ai_client.get_current_client()
                if client is None:
                    self.result = {
                        "success": False,
                        "message": "No AI client available. Please check your API keys.",
                        "script": None
                    }
                    self.callback("Error: No AI client available. Please check your API keys.")
                    self.update_progress(100)
                    return

                # Print debug info
                import logging
                logging.info(f"Using AI provider: {self.llm_provider}")
                logging.info(f"Current provider: {ai_client.get_current_provider()}")
                logging.info(f"Client type: {type(client)}")
            except Exception as e:
                self.result = {
                    "success": False,
                    "message": f"Error setting up AI provider: {str(e)}",
                    "script": None
                }
                self.callback(f"Error setting up AI provider: {str(e)}")
                self.update_progress(100)
                import traceback
                traceback.print_exc()
                return

            current_provider = ai_client.get_current_provider()

            self.callback(f"Generating script using {current_provider}...")
            self.update_progress(30)

            # Create the prompt for script generation
            prompt = self._create_script_prompt()

            # Check if paused or stopped
            self._check_pause()
            if self.stop_requested:
                self.result = {
                    "success": False,
                    "message": "Script generation stopped by user",
                    "script": None
                }
                return

            # Generate the script
            try:
                messages = [
                    {
                        "role": "system",
                        "content": """You are an expert script writer for viral short-form videos.
                        You create engaging, hooky scripts that capture attention from the first second and maintain interest throughout.
                        Your scripts are optimized for TikTok, Instagram Reels, and YouTube Shorts.
                        You write in simple, modern English that a 7th grader can understand.
                        Your scripts always have a strong hook at the beginning and maintain engagement until the end.
                        You never use hashtags or emojis in your scripts.

                        IMPORTANT: NEVER start scripts with phrases like "welcome to my channel", "hey guys", "what's up everyone", or similar channel introductions.
                        ALWAYS start directly with a powerful hook or the main content. Jump straight into the most interesting part of your message."""
                    },
                    {"role": "user", "content": prompt}
                ]

                self.callback("Crafting your script...")
                self.update_progress(50)

                # Use the AI client to generate the script
                try:
                    import logging
                    logging.info("Attempting to generate script with AI client")

                    # Use a simpler model for better compatibility
                    model_to_use = "gpt-3.5-turbo"
                    if self.llm_provider.lower() == "groq":
                        model_to_use = "llama3-8b-8192"  # Direct Groq model name

                    logging.info(f"Using model: {model_to_use}")

                    response = ai_client.chat_completion(
                        messages=messages,
                        model=model_to_use,
                        temperature=0.7,
                        max_tokens=1000
                    )

                    logging.info(f"Response received: {type(response)}")

                    # Check if response is valid
                    if response is None:
                        raise ValueError("No response received from AI provider")

                    # Check if response has the expected structure
                    if not hasattr(response, 'choices'):
                        logging.error(f"Response missing 'choices' attribute. Response type: {type(response)}")
                        logging.error(f"Response attributes: {dir(response)}")
                        raise ValueError("Invalid response format from AI provider (no choices attribute)")

                    if not response.choices:
                        logging.error("Response has empty choices list")
                        raise ValueError("Invalid response format from AI provider (empty choices)")

                    # Check if the first choice has a message attribute
                    if not hasattr(response.choices[0], 'message'):
                        logging.error(f"First choice missing 'message' attribute. Choice type: {type(response.choices[0])}")
                        logging.error(f"Choice attributes: {dir(response.choices[0])}")
                        raise ValueError("Invalid response format from AI provider (no message in first choice)")

                    # Check if the message has a content attribute
                    if not hasattr(response.choices[0].message, 'content'):
                        logging.error(f"Message missing 'content' attribute. Message type: {type(response.choices[0].message)}")
                        logging.error(f"Message attributes: {dir(response.choices[0].message)}")
                        raise ValueError("Invalid response format from AI provider (no content in message)")

                    # Get the content from the response
                    script_content = response.choices[0].message.content

                    # Validate the content
                    if script_content is None:
                        raise ValueError("Script content is None")

                    if not script_content or len(script_content.strip()) < 10:
                        raise ValueError(f"Generated script is too short or empty: '{script_content}'")

                    logging.info(f"Successfully generated script of length {len(script_content)}")

                except Exception as e:
                    logging.error(f"Error during chat completion: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    raise ValueError(f"Failed to generate script: {str(e)}")

                # Set the result BEFORE updating progress to 100
                # This ensures the result is available when check_script_results is called
                self.result = {
                    "success": True,
                    "message": "Script generated successfully!",
                    "script": script_content
                }

                # Log that we've set the result
                import logging
                logging.info(f"Setting script result: success=True, script length={len(script_content)}")

                # Now update progress and callback
                self.callback("Script generated successfully!")
                self.update_progress(100)

            except Exception as e:
                self.callback(f"Error generating script: {str(e)}")
                self.result = {
                    "success": False,
                    "message": f"Error: {str(e)}",
                    "script": None
                }

        except Exception as e:
            self.callback(f"Error during script generation: {str(e)}")
            self.result = {
                "success": False,
                "message": f"Error: {str(e)}",
                "script": None
            }
            import traceback
            traceback.print_exc()

    def _create_script_prompt(self):
        """Create a prompt for script generation based on user input"""
        # Base prompt structure
        prompt = f"""
        Create a compelling script for a short-form video about: {self.concept}

        Script Type: {self.script_type}

        Requirements:
        1. Start with a powerful hook that immediately grabs attention
        2. Keep the script engaging and hooky throughout
        3. Use simple, modern English that a 7th grader can understand
        4. Include a clear call-to-action or thought-provoking ending
        5. Optimize for vertical video format
        6. Keep the script approximately {self.word_count} words ({int(self.word_count/3)}-{int(self.word_count/2)} seconds when spoken)
        7. Do NOT include any hashtags, emojis, or formatting instructions
        8. Write in a conversational, authentic tone
        9. NEVER start with "welcome to my channel", "hey guys", or similar channel introductions
        10. Jump straight into the hook or main content without any introductory phrases

        IMPORTANT FORMATTING INSTRUCTIONS:
        - Generate ONLY the actual script content that will be voiced over
        - Do NOT include any introductory phrases like "Description:" or "Opening Scene Details:"
        - Do NOT include any meta-commentary or notes about the script
        - Each sentence should be a complete narrative that flows naturally to the next
        - Format all text as complete sentences ready for voiceover recording
        - The script should maintain continuity throughout the entire story
        - Ensure no critical narrative elements are missing between scenes
        - The script should align perfectly with what will be heard in the final video

        The script should be ready to use as-is, with no additional editing needed.
        """

        # Add specific guidance based on script type
        if self.script_type.lower() == "motivational":
            prompt += """
            Additional guidance for motivational content:
            - Include a powerful, inspiring message that resonates emotionally
            - Use a mix of challenges and triumphs to create an emotional journey
            - End with an empowering call to action
            - Focus on overcoming obstacles and personal growth
            """
        elif self.script_type.lower() == "funny":
            prompt += """
            Additional guidance for funny content:
            - Use relatable humor that appeals to a broad audience
            - Include unexpected twists or punchlines
            - Keep the tone light and entertaining
            - Use timing effectively (pauses for effect)
            """
        elif self.script_type.lower() == "educational":
            prompt += """
            Additional guidance for educational content:
            - Present information in a clear, engaging way
            - Include a surprising fact or statistic early to hook viewers
            - Break down complex concepts into simple explanations
            - End with a key takeaway that viewers will remember
            """
        elif self.script_type.lower() == "storytelling":
            prompt += """
            Additional guidance for storytelling content:
            - Create a narrative arc with a clear beginning, middle, and end
            - Use vivid, descriptive language to paint a picture
            - Include an emotional component that connects with viewers
            - End with a resolution or thought-provoking question
            """

        return prompt

def generate_script(concept, script_type, callback, update_progress, llm_provider="Groq", word_count=150):
    """Generate a script based on the provided concept and type"""
    # Create and start the generator thread
    generator_thread = ScriptGeneratorThread(
        concept, script_type, callback, update_progress, llm_provider, word_count
    )
    generator_thread.start()

    # Wait a short time to ensure the thread has started
    import time
    time.sleep(0.1)

    return generator_thread
