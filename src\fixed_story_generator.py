from utils import (
    call_openai_api,
    create_empty_storyboard,
    load_config,
    STORY_TYPE_HASHTAGS
)
import re
import logging
import json
import traceback
from datetime import datetime
from typing import Dict, Any, List, Tuple
from utils import STORY_TYPE_HASHTAGS

# Story type guidelines
def get_story_type_guidelines(story_type: str) -> str:
    story_type_guidelines = {
        "love": '''
        1. Emotional connection: Create a story that explores genuine emotional connection between people.
        2. Relationship dynamics: Show the complexities, joys, and challenges of romantic relationships.
        3. Character development: Develop characters whose feelings and actions feel authentic and relatable.
        4. Emotional arc: Create a clear emotional journey with meaningful moments of connection.
        5. Universal themes: Incorporate universal themes about love that resonate with a wide audience.
        6. Authenticity: Ensure the emotions and situations feel real and relatable, not clichéd.
        7. Emotional impact: Aim for content that genuinely moves the audience emotionally.
        ''',
        "philosophy": '''
        1. Core concept: Focus on a single philosophical idea or question that's accessible to a general audience.
        2. Real-world application: Show how philosophical concepts directly apply to everyday life situations.
        3. Relatable examples: Use concrete, relatable examples that viewers can connect with personally.
        4. Simple language: Explain philosophical concepts in simple, jargon-free language.
        5. Visual storytelling: Create a narrative that visually demonstrates the philosophical principle.
        6. Practical wisdom: Emphasize practical wisdom and actionable insights rather than abstract theory.
        7. Emotional connection: Connect the philosophical idea to emotions or experiences viewers already understand.
        8. Open-ended conclusion: End with a thought-provoking question that encourages personal reflection.

        Create an engaging philosophical video that makes viewers think deeply about life, meaning, or values while keeping the content accessible, practical, and visually compelling. Focus on how philosophical ideas can improve viewers' lives or change their perspective in meaningful ways.
        ''',
        "motivational": '''
        1. Inspiring message: Craft a powerful, uplifting message that motivates viewers to take action.
        2. Personal transformation: Share a compelling story of overcoming obstacles or achieving significant growth.
        3. Actionable takeaways: Include practical steps viewers can implement in their own lives.
        4. Emotional resonance: Create content that connects emotionally and inspires genuine feelings of motivation.
        5. Universal themes: Focus on themes like perseverance, resilience, growth, and self-belief that resonate widely.
        6. Authenticity: Ensure the motivational message feels genuine and not overly clichéd.
        7. Call to action: End with a clear, inspiring call that encourages viewers to make positive changes.

        Create an inspiring motivational piece that genuinely uplifts viewers and provides them with both emotional inspiration and practical guidance for personal growth or achievement.
        ''',
        "scary": '''
        1. Atmosphere: Create a tense, eerie, or unsettling atmosphere through descriptive language.
        2. Suspense building: Gradually build tension and dread throughout the narrative.
        3. Fear triggers: Incorporate universal fear triggers or phobias that resonate with many viewers.
        4. Psychological elements: Include psychological horror elements that play on deeper fears.
        5. Unexpected twists: Include surprising developments or revelations that heighten the fear factor.
        6. Sensory details: Use vivid sensory details to make the frightening elements feel more immediate.
        7. Lingering impact: Craft a story that leaves viewers with a lasting sense of unease or fear.

        Create a genuinely frightening story that builds suspense and creates a sense of dread or fear in viewers through atmospheric storytelling and psychological tension.
        ''',
        "mystery": '''
        1. Central puzzle: Present an intriguing mystery or puzzle that needs to be solved.
        2. Clues and red herrings: Scatter meaningful clues alongside misleading information.
        3. Suspense: Build tension and curiosity that keeps viewers engaged and guessing.
        4. Character secrets: Include characters with hidden motives, backgrounds, or knowledge.
        5. Revelations: Structure the narrative to include surprising but logical revelations.
        6. Resolution: Provide a satisfying explanation that makes sense given the established clues.
        7. Lingering questions: Optionally, leave some smaller mysteries unresolved to maintain intrigue.

        Create an engaging mystery that presents viewers with an intriguing puzzle, provides subtle clues throughout, and delivers a satisfying resolution that makes sense in retrospect.
        ''',
        "bedtime": '''
        1. Gentle tone: Use soothing, calming language appropriate for bedtime listening.
        2. Imaginative elements: Include whimsical or fantastical elements that inspire pleasant dreams.
        3. Positive themes: Focus on comforting, reassuring themes that promote feelings of security.
        4. Simple structure: Use a straightforward narrative structure that's easy to follow when sleepy.
        5. Rhythmic language: Incorporate gentle rhythm and repetition that has a lulling quality.
        6. Peaceful resolution: End with a satisfying, peaceful conclusion that leaves listeners content.
        7. Sensory comfort: Include descriptions of cozy, comfortable settings and situations.

        Create a soothing bedtime story with a gentle tone, comforting themes, and a peaceful resolution that helps listeners relax and prepare for sleep.
        ''',
        "pet tips": '''
        1. Pet care advice: Provide practical, actionable tips for pet owners to improve their pet's health, behavior, or quality of life.
        2. Expert insights: Include veterinary or animal behavior knowledge that pet owners might not know.
        3. Common problems: Address frequent challenges pet owners face with solutions that work.
        4. Safety information: Highlight potential hazards or warning signs pet owners should be aware of.
        5. Species-specific guidance: Tailor advice to specific types of pets (dogs, cats, birds, etc.) when relevant.
        6. Enrichment ideas: Suggest activities, toys, or interactions that improve pet mental and physical wellbeing.
        7. Bonding techniques: Include ways to strengthen the human-animal relationship.
        8. Cost-effective solutions: Offer affordable alternatives to expensive pet products when possible.

        Create engaging, helpful content for pet owners that combines practical advice with heartwarming elements that celebrate the human-animal bond. Content can include pet care tips, training advice, health information, or heartwarming pet stories that resonate with animal lovers.
        ''',
        "islamic": '''
        1. Hadith selection: Choose an authentic hadith (saying of Prophet Muhammad) that contains valuable wisdom or guidance.
        2. Source citation: Mention the authentic collection the hadith comes from (e.g., Sahih Bukhari, Sahih Muslim).
        3. Context: Provide historical or situational context that helps understand the hadith's significance.
        4. Explanation: Offer a clear, accessible explanation of the hadith's meaning and teachings.
        5. Modern relevance: Connect the hadith's wisdom to contemporary life situations and challenges.
        6. Practical application: Suggest ways viewers can apply this teaching in their daily lives.
        7. Ethical principles: Highlight the moral or ethical principles embodied in the teaching.
        8. Respectful tone: Maintain a respectful, educational approach that honors the religious tradition.
        9. Universal values: When appropriate, emphasize universal values that can resonate with people of all backgrounds.

        Create educational, inspiring content based on authentic Islamic teachings that provides valuable wisdom, historical insights, or ethical guidance. Focus on making the content accessible and relevant to contemporary life while maintaining accuracy and respect for the tradition.
        ''',
    }

    # Default guidelines for unspecified story types
    default_guidelines = '''
    1. Main character: Introduce a compelling protagonist with clear goals or desires.
    2. Setting: Establish a vivid and appropriate setting for the story.
    3. Conflict: Present a clear challenge or obstacle that creates tension.
    4. Emotional arc: Develop an emotional journey that resonates with viewers.
    5. Pacing: Structure the narrative with a clear beginning, middle, and end.
    6. Resolution: Provide a satisfying conclusion that resolves the main conflict.
    7. Theme: Incorporate a meaningful message or theme that adds depth.
    8. Engagement: Use hooks, questions, or suspense to maintain viewer interest.
    '''

    # Return the guidelines for the specified story type, or the default if not found
    return story_type_guidelines.get(story_type.lower(), default_guidelines)

def generate_story_and_title(client, story_type: str, custom_title=None) -> Tuple[str, str, str]:
    """Generate a story and title based on the story type

    Args:
        client: The AI client to use for generation
        story_type: The type of story to generate
        custom_title: Optional custom title to use instead of generating one

    Returns:
        Tuple of (title, description, story content)
    """
    # Get guidelines for the specified story type
    type_guidelines = get_story_type_guidelines(story_type)

    # Create the prompt based on whether we have a custom title or not
    if custom_title:
        prompt = f"""Create a compelling, emotionally engaging short-form video script for {story_type} with the title: "{custom_title}".

        The content should be:
        1. Optimized for vertical video (TikTok, Instagram Reels, YouTube Shorts)
        2. 30-60 seconds in length when read aloud (approximately 80-120 words)
        3. Highly engaging with a strong hook in the first 3 seconds
        4. Conversational and authentic in tone
        5. Structured with a clear beginning, middle, and end
        6. Written in first-person perspective when appropriate
        7. Designed to keep viewers watching until the end
        8. Use only standard ASCII characters (avoid special quotes, em dashes, etc.)
        9. IMPORTANT: The script must be relevant to the provided title: "{custom_title}"
        10. NEVER start with "welcome to my channel", "hey guys", or similar channel introductions
        11. Jump straight into the hook or main content without any introductory phrases

        IMPORTANT FORMATTING INSTRUCTIONS:
        - Generate ONLY the actual script content that will be voiced over
        - Do NOT include any introductory phrases like "Description:" or "Opening Scene Details:"
        - Do NOT include any meta-commentary or notes about the script
        - Each sentence should be a complete narrative that flows naturally to the next
        - Format all text as complete sentences ready for voiceover recording
        - The script should maintain continuity throughout the entire story
        - Ensure no critical narrative elements are missing between scenes
        - The script should align perfectly with what will be heard in the final video

        {type_guidelines}

        Format your response as:

        Title: {custom_title}

        Description: [1-2 sentences describing the content with relevant hashtags]

        [The actual script content, written to be read aloud and shown as captions]"""
    else:
        prompt = f"""Create a compelling, emotionally engaging short-form video script for {story_type}.

        The content should be:
        1. Optimized for vertical video (TikTok, Instagram Reels, YouTube Shorts)
        2. 30-60 seconds in length when read aloud (approximately 80-120 words)
        3. Highly engaging with a strong hook in the first 3 seconds
        4. Conversational and authentic in tone
        5. Structured with a clear beginning, middle, and end
        6. Written in first-person perspective when appropriate
        7. Designed to keep viewers watching until the end
        8. Use only standard ASCII characters (avoid special quotes, em dashes, etc.)
        9. NEVER start with "welcome to my channel", "hey guys", or similar channel introductions
        10. Jump straight into the hook or main content without any introductory phrases

        IMPORTANT FORMATTING INSTRUCTIONS:
        - Generate ONLY the actual script content that will be voiced over
        - Do NOT include any introductory phrases like "Description:" or "Opening Scene Details:"
        - Do NOT include any meta-commentary or notes about the script
        - Each sentence should be a complete narrative that flows naturally to the next
        - Format all text as complete sentences ready for voiceover recording
        - The script should maintain continuity throughout the entire story
        - Ensure no critical narrative elements are missing between scenes
        - The script should align perfectly with what will be heard in the final video

        {type_guidelines}

        Format your response as:

        Title: [Attention-grabbing, specific title that would work well as a video title]

        Description: [1-2 sentences describing the content with relevant hashtags]

        [The actual script content, written to be read aloud and shown as captions]"""

    # Call the API
    messages = [
        {
            "role": "system",
            "content": '''You are an expert content creator specialized in viral short-form videos for TikTok, Instagram Reels and YouTube Shorts. Your content is known for:

            1. Highly engaging hooks that grab attention in the first 3 seconds
            2. Clear, concise, and conversational storytelling optimized for short attention spans
            3. Strategic pacing with unexpected twists, revelations, or emotional moments that keep viewers watching
            4. Relatable, authentic content that resonates with specific audience interests
            5. Use of proven engagement patterns like "Wait for it..." or setting up anticipation
            6. Content that's designed to be shareable, commentable, and spark discussion
            7. Strong emotional triggers - humor, surprise, inspiration, or controversy
            8. Use simple and modern English so even 7th graders can understand it
            9. Use only standard ASCII characters (no special quotes, em dashes, etc.)
            10. Avoid quotes within quotes in titles to prevent JSON parsing issues

            IMPORTANT RULES:
            - NEVER start scripts with phrases like "welcome to my channel", "hey guys", "what's up everyone", or similar channel introductions
            - ALWAYS start directly with a powerful hook or the main content
            - Jump straight into the most interesting part of your message
            - Never mention channel names, subscribers, or likes in your content

            When creating titles:
            - Make them attention-grabbing with specific, intriguing details
            - Use power words that evoke emotion or curiosity
            - Include numbers or specificity that promises valuable information
            - Consider SEO and search-friendly phrasing while maintaining appeal
            - Create a sense of urgency or must-know information
            - Use only standard ASCII characters (avoid special quotes like "" or '')
            - Avoid using quotes within quotes to prevent JSON parsing issues
            - If you need to use quotes in a title, use single quotes consistently

            When creating descriptions:
            - Include relevant trending hashtags that will maximize discovery
            - Use emoji strategically to enhance emotional impact
            - Create a clear content premise that promises value or entertainment
            - Incorporate call-to-action phrases that encourage engagement

            Most importantly, your content is optimized for the fast-paced, visually-driven format of short-form video, with clear story arcs that work well with captions and deliver on viewer expectations while maintaining their attention throughout.'''
        },
        {"role": "user", "content": prompt},
    ]

    response = call_openai_api(client, messages)
    if response:
        parts = response.split("\n\n", 2)
        if len(parts) == 3:
            # If we have a custom title, use it instead of the generated one
            if custom_title:
                title = custom_title
            else:
                title = parts[0].replace("Title: ", "").strip()

            description = parts[1].replace("Description: ", "").strip()
            content = parts[2].strip()

            # Ensure the first hashtag is from the STORY_TYPE_HASHTAGS mapping
            first_hashtag = STORY_TYPE_HASHTAGS[story_type]

            # Split the description into text and hashtags
            desc_parts = description.split('#', 1)
            if len(desc_parts) > 1:
                desc_text = desc_parts[0].strip()
                existing_hashtags = desc_parts[1].strip()
                # Remove #facelessvideos.app if it's present in existing_hashtags
                existing_hashtags = existing_hashtags.replace("facelessvideos.app", "").strip()
                # Combine hashtags, ensuring the mapped hashtag is first
                all_hashtags = f"{first_hashtag} #{existing_hashtags} #facelessvideos.app"
            else:
                desc_text = description
                all_hashtags = f"{first_hashtag} #facelessvideos.app"

            # Combine the description text and hashtags
            description = f"{desc_text} #{all_hashtags}"

            return title, description, content
        else:
            # If the response doesn't have the expected format, return default values
            return "Untitled", f"A {story_type} story. {STORY_TYPE_HASHTAGS[story_type]} #facelessvideos.app", response
    else:
        # If there's no response, return default values
        return "Untitled", f"A {story_type} story. {STORY_TYPE_HASHTAGS[story_type]} #facelessvideos.app", "No content generated."

def generate_storyboard(client, story, title, story_type):
    """Generate a storyboard for a story"""
    logging.info(f"Generating storyboard for '{title}' with story type: {story_type}")
    logging.info(f"Story length: {len(story)} characters, word count: {len(story.split())}")

    # Get guidelines for the specified story type
    type_guidelines = get_story_type_guidelines(story_type)

    # Additional guidelines for specific story types
    additional_guidelines = {
        "motivational": "- Focus on creating scenes that visually represent the transformation or growth being discussed\n- Use visual metaphors that represent obstacles, challenges, and breakthroughs\n- Include scenes that show both the 'before' and 'after' states to emphasize change\n- Consider using color progression from darker to lighter tones to represent the journey",
        "scary": "- Use visual techniques like shadows, partial visibility, and implied threats rather than explicit horror\n- Consider how lighting can create tension (harsh shadows, dim lighting, unusual color casts)\n- Use composition to create a sense of vulnerability or being watched\n- Think about how to visually represent psychological fears and anxieties",
        "mystery": "- Create visual compositions that deliberately obscure or partially reveal important elements\n- Use visual techniques like focus pulls, revealing camera movements, or strategic framing\n- Consider how to visually represent clues, red herrings, and revelations\n- Use lighting and color to create a sense of intrigue and uncertainty",
    }

    # Current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %I:%M:%S %p")

    # Create the prompt
    prompt = f"""Create a detailed storyboard for a short-form vertical video based on the following story.

    Title: {title}
    Story Type: {story_type}

    IMPORTANT: Use only standard ASCII characters in all text. Avoid special quotes, em dashes, etc.
    Do not use curly quotes ("") or special apostrophes ('') - use only straight quotes (") and regular apostrophes (').

    First, create a compelling opening scene that:
    - Hooks viewers in the first 3 seconds
    - Establishes the visual tone and style
    - Introduces the main subject or concept
    - Uses strong visual composition optimized for vertical video format
    - NEVER starts with "welcome to my channel", "hey guys", or similar channel introductions
    - Jumps straight into the hook or main content without any introductory phrases

    Then, for each subsequent scene, provide the following visual details:
    1. Scene Number
    2. Description: A highly visual description (80-100 words) focusing on cinematic elements including:
       - Specific camera angles (close-up, medium shot, wide shot, overhead, etc.)
       - Lighting quality and direction (harsh, soft, backlit, silhouette, etc.)
       - Color palette and mood
       - Key visual elements and composition
       - Environment and setting details
       - Any motion, transitions, or visual effects
       - Character positioning, expressions, and body language if applicable
       {type_guidelines}
    3. Subtitles: Use EXACT quotes from the original text that perfectly match the visual elements described.
    4. Transition: Specify the type of transition to the current scene, explaining how it enhances the visual storytelling.

    CRITICAL INSTRUCTIONS FOR COMPLETE SCRIPT COVERAGE:
    - You MUST include EVERY PART of the original script in your storyboard subtitles
    - Divide the script into logical segments, ensuring NO CONTENT IS SKIPPED, especially in the middle sections
    - Distribute the script content evenly across scenes - don't put too much in early scenes and skip later content
    - If the script is long, create more scenes to ensure complete coverage (up to 14 scenes maximum)
    - Check your work to verify that every sentence from the original script appears in your storyboard subtitles
    - The combined subtitles from all scenes should reconstruct the complete original script with no missing parts
    - Pay special attention to the middle sections of the script, which must be fully represented
    - Create a balanced storyboard where each scene contains approximately equal amounts of text

    Guidelines:
    - Create scenes specifically optimized for vertical short-form video content (TikTok, Instagram Reels, YouTube Shorts)
    - Focus on highly visual, attention-grabbing elements that work well in the first 3 seconds to hook viewers
    - Design each scene to be visually compelling even with the sound off
    - Ensure subtitles MUST contain only exact text from the original text, without any additions, omissions, or modifications
    - Include every sentence from the original text in the subtitles, maintaining the correct order across all scenes
    - Each subtitle must be unique; do not repeat content in multiple scenes
    - For partial sentences at scene boundaries, include the fragment and continue it in the next scene's subtitles
    - EVERY SCENE MUST HAVE NON-EMPTY SUBTITLES. If you run out of story text, do not create additional scenes
    - Ensure that the scenes flow logically and capture the essence of the story or information
    - Make subtitles PLAIN TEXT only - do not use asterisks (*) for emphasis, or any other formatting symbols
    - Do not include vertical bars or column separators (|) in subtitles
    - Do not repeat the same subtitles across multiple scenes
    - Do not include directorial comments like "Scene:" or "Description:" in the subtitles
    - Use only standard ASCII characters in all text (no special quotes, em dashes, etc.)
    - Avoid quotes within quotes in titles and descriptions to prevent JSON parsing issues
    {additional_guidelines.get(story_type.lower(), "")}

    Format your response as a JSON object with the following structure:
    {{
        "project_info": {{
            "title": "{title}",
            "user": "AI Generated",
            "timestamp": "{timestamp}"
        }},
        "storyboards": [
            {{
                "scene_number": "Scene Number",
                "description": "Detailed Visual Scene Description",
                "subtitles": "Subtitles or Dialogue",
                "image": null,
                "audio": null,
                "transition_type": "Transition Type"
            }},
            ...
        ]
    }}

    Here's the story:

    {story}"""

    messages = [
        {
            "role": "system",
            "content": '''You are a highly skilled storyboard artist specializing in visualizing various types of content. You excel at:
                1. Creating vivid, engaging scene descriptions that translate different types of content into compelling visuals
                2. Developing visual metaphors and analogies to represent complex ideas or concepts
                3. Crafting scenes that show the real-world application or implications of the information
                4. Incorporating basic cinematographic techniques to enhance visual storytelling
                5. Faithfully representing the original text using exact quotes for subtitles
                6. Ensuring the visual narrative accurately captures key points and their development
                7. Balancing informative content with visually interesting and engaging scenes
                8. Maintaining logical consistency between scenes while providing a variety of visual representations
                9. Using plain text only for subtitles - no formatting characters or symbols
                10. Never repeating the same subtitles in multiple scenes
                11. Avoiding any technical markers or formatting in subtitles (like asterisks, bars, or scene labels)
                12. Using only standard ASCII characters in all text (no special quotes, em dashes, etc.)
                13. Avoiding quotes within quotes in titles and descriptions to prevent JSON parsing issues

                IMPORTANT RULES:
                - NEVER include phrases like "welcome to my channel", "hey guys", "what's up everyone", or similar channel introductions in the subtitles
                - ALWAYS start the first scene with a powerful hook or the main content
                - Never include mentions of channel names, subscribers, or likes in the subtitles
                - Jump straight into the most interesting part of the message

                CRITICAL RESPONSIBILITY: You must ensure COMPLETE coverage of the original script in your storyboard.
                - Every single part of the original script must appear in your storyboard subtitles
                - You must carefully divide the script into logical segments, ensuring NO CONTENT IS SKIPPED
                - Pay special attention to middle sections of scripts, which must be fully represented
                - The combined subtitles from all scenes should reconstruct the complete original script
                - If needed, create more scenes to ensure complete coverage (up to 14 scenes maximum)
                - Distribute content evenly across scenes - don't put too much in early scenes and skip later content
                - Create a balanced storyboard where each scene contains approximately equal amounts of text

                Your storyboards effectively bridge the gap between various types of content and visual representation,
                paying close attention to both the overall flow of information and specific details that enhance
                the communication of the content.'''
        },
        {"role": "user", "content": prompt},
    ]

    response = call_openai_api(client, messages)

    try:
        # Try to extract JSON from the response
        match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
        if match:
            json_str = match.group(1)
        else:
            json_str = response

        # Log the raw JSON string for debugging
        logging.debug(f"Raw JSON string before processing: {json_str[:100]}...")

        # Look for the start of JSON content if there's text before it
        start_idx = json_str.find('{')
        if start_idx > 0:
            json_str = json_str[start_idx:]

        # Clean up JSON string
        # Replace special quotes with regular quotes
        json_str = json_str.replace('\u201c', '\"').replace('\u201d', '\"')  # Unicode double quotes
        json_str = json_str.replace('\u2018', "'").replace('\u2019', "'")    # Unicode single quotes
        json_str = json_str.replace('"', '\"').replace('"', '\"')  # Actual double quotes
        json_str = json_str.replace(''', "'").replace(''', "'")    # Actual single quotes

        # Replace single quotes with double quotes for property names
        json_str = re.sub(r"'([^']*)'\s*:", r'"\1":', json_str)

        # Fix trailing commas in arrays and objects
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*\]', ']', json_str)

        # Fix missing quotes around property names
        json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)

        # Fix missing commas after description fields (common Groq issue)
        json_str = re.sub(r'"description":\s*"([^"]*)"\s*"subtitles"', r'"description": "\1","subtitles"', json_str)

        # Fix missing commas between any JSON properties (more general fix)
        json_str = re.sub(r'"([^"]+)":\s*"([^"]*)"\s*"([^"]+)":', r'"\1": "\2","\3":', json_str)

        # Fix missing commas after scene_number fields
        json_str = re.sub(r'"scene_number":\s*"([^"]*)"\s*"description"', r'"scene_number": "\1","description"', json_str)
        json_str = re.sub(r'"scene_number":\s*(\d+)\s*"description"', r'"scene_number": \1,"description"', json_str)

        # Fix missing commas after reading fields
        json_str = re.sub(r'"reading":\s*"([^"]*)"\s*"', r'"reading": "\1","', json_str)

        # Fix missing commas after message fields
        json_str = re.sub(r'"message":\s*"([^"]*)"\s*"', r'"message": "\1","', json_str)

        # Fix missing commas after title fields
        json_str = re.sub(r'"title":\s*"([^"]*)"\s*"user"', r'"title": "\1","user"', json_str)

        # Log the processed JSON string
        logging.debug(f"Processed JSON string: {json_str[:100]}...")

        # Try to parse the JSON
        try:
            storyboard_project = json.loads(json_str)
        except json.JSONDecodeError as json_err:
            logging.error(f"JSON parsing error: {json_err}")
            logging.error(f"Problematic JSON: {json_str}")

            # Create a basic storyboard structure as fallback
            storyboard_project = {
                "project_info": {
                    "title": title,
                    "user": "AI Generated",
                    "timestamp": timestamp
                },
                "storyboards": []
            }

            # Try to extract scenes using regex if JSON parsing failed
            scenes = re.findall(r'"scene_number"\s*:\s*"([^"]*)"\s*,\s*"description"\s*:\s*"([^"]*)"\s*,\s*"subtitles"\s*:\s*"([^"]*)"', json_str)

            for i, (scene_num, desc, subs) in enumerate(scenes):
                storyboard_project["storyboards"].append({
                    "scene_number": scene_num,
                    "description": desc,
                    "subtitles": subs,
                    "image": None,
                    "audio": None,
                    "transition_type": "Cut"
                })

            # If we couldn't extract any scenes, create a default one
            if not storyboard_project["storyboards"]:
                storyboard_project["storyboards"].append({
                    "scene_number": "Scene 1",
                    "description": "Default scene created due to JSON parsing error.",
                    "subtitles": story[:100] + "..." if len(story) > 100 else story,
                    "image": None,
                    "audio": None,
                    "transition_type": "None"
                })

        # Ensure all scenes have the required fields
        for scene in storyboard_project["storyboards"]:
            if "image" not in scene:
                scene["image"] = None
            if "audio" not in scene:
                scene["audio"] = None
            if "transition_type" not in scene:
                scene["transition_type"] = "Cut"

        # Log the number of scenes created
        scene_count = len(storyboard_project.get("storyboards", []))
        logging.info(f"Storyboard generation complete with {scene_count} scenes")

        # Calculate average subtitle length per scene
        if scene_count > 0:
            total_subtitle_length = sum(len(scene.get("subtitles", "")) for scene in storyboard_project.get("storyboards", []))
            avg_length = total_subtitle_length / scene_count
            logging.info(f"Average subtitle length per scene: {avg_length:.1f} characters")

        return storyboard_project

    except Exception as e:
        logging.error(f"Error generating storyboard: {str(e)}")
        logging.error(traceback.format_exc())

        # Return a basic storyboard structure as fallback
        return create_empty_storyboard(title, story)
