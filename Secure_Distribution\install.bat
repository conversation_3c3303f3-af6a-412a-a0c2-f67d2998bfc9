@echo off 
setlocal EnableDelayedExpansion 
title Installing Faceless Video Generator v7.0 
 
echo ======================================================= 
echo   Installing Faceless Video Generator v7.0 
echo ======================================================= 
echo. 
 
:: Check if Python 3.12.8 is installed 
echo Checking Python installation... 
 
if errorlevel 1 ( 
    echo Python 3.12.8 is not installed or not in PATH. 
    echo Downloading Python 3.12.8 installer... 
 
    :: Download Python 3.12.8 installer using PowerShell 
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.12.8/python-3.12.8-amd64.exe' -OutFile 'python-3.12.8-amd64.exe'}" 
    if not exist "python-3.12.8-amd64.exe" ( 
        echo Failed to download Python installer. 
        echo Please manually install Python 3.12.8 from https://www.python.org/downloads/ 
        echo Then run this install script again. 
        pause 
        exit /b 1 
    ) 
 
    echo Installing Python 3.12.8... 
    echo Please complete the installation when the installer opens. 
    echo IMPORTANT: Check "Add Python to PATH" during installation. 
    python-3.12.8-amd64.exe /passive InstallAllUsers=1 PrependPath=1 
    echo. 
    echo Waiting for Python installation to complete... 
    timeout /t 30 
    del python-3.12.8-amd64.exe 
 
    :: Verify Python was installed correctly 
    if errorlevel 1 ( 
        echo. 
        echo ERROR: Python 3.12.8 installation failed or PATH was not updated. 
        echo Please restart your computer and run this script again. 
        echo If the problem persists, install Python 3.12.8 manually. 
        pause 
        exit /b 1 
    ) 
) 
 
echo Python 3.12 is installed and ready to use. 
echo. 
 
:: Create and activate virtual environment 
echo Creating virtual environment... 
 
if exist "venv" rmdir /s /q "venv" 
python -m venv venv 
 
echo Activating virtual environment... 
call venv\Scripts\activate.bat 
 
echo Upgrading pip... 
python -m pip install --upgrade pip 
 
echo Installing required dependencies... 
pip install -r requirements.txt 
 
echo Installing additional dependencies... 
pip install together 
pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 imageio-ffmpeg==0.5.1 proglog==0.1.10 
 
echo Creating default configuration... 
echo { 
echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", 
echo   "whatsapp_number": "+923107520004", 
echo   "tts": { 
echo     "speech_rate": 1.0 
echo   } 
echo } 
 
echo ======================================================= 
echo Installation complete 
echo. 
echo Setting up Python path for modules... 
echo # Make sure src directory is in Python path 
echo # Make services a proper package 
echo. 
echo You can now run the application using run.bat 
echo ======================================================= 
echo. 
pause 
