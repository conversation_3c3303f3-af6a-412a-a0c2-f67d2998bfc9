import os
import re
import time
import requests
import replicate
from typing import Optional, Dict, Any, List, Callable
from utils import create_blank_image

def extract_key_elements(description: str) -> str:
    """
    Extract key visual elements from a scene description to focus the image generation.

    Args:
        description: The full scene description

    Returns:
        A condensed version focusing on key visual elements
    """
    # Extract visual elements like setting, lighting, colors, and composition
    visual_elements = []

    # Look for setting descriptions
    setting_patterns = [
        r'(in|at|on) (a|an|the) ([^,.]+)',
        r'setting: ([^,.]+)',
        r'environment: ([^,.]+)',
        r'location: ([^,.]+)',
        r'scene shows ([^,.]+)',
        r'backdrop of ([^,.]+)',
    ]

    for pattern in setting_patterns:
        matches = re.findall(pattern, description, re.IGNORECASE)
        if matches:
            for match in matches:
                if isinstance(match, tuple):
                    visual_elements.append(match[-1])  # Get the last group which contains the setting
                else:
                    visual_elements.append(match)

    # Look for lighting descriptions
    lighting_patterns = [
        r'lighting is ([^,.]+)',
        r'lit by ([^,.]+)',
        r'light(ing)? ([^,.]+)',
        r'([^,.]+) lighting',
        r'([^,.]+) light',
    ]

    for pattern in lighting_patterns:
        matches = re.findall(pattern, description, re.IGNORECASE)
        if matches:
            for match in matches:
                if isinstance(match, tuple):
                    visual_elements.append(f"lighting: {match[-1]}")
                else:
                    visual_elements.append(f"lighting: {match}")

    # Look for color descriptions
    color_patterns = [
        r'colors? of ([^,.]+)',
        r'([^,.]+) colors?',
        r'color palette ([^,.]+)',
        r'hues of ([^,.]+)',
        r'tones of ([^,.]+)',
    ]

    for pattern in color_patterns:
        matches = re.findall(pattern, description, re.IGNORECASE)
        if matches:
            for match in matches:
                if isinstance(match, tuple):
                    visual_elements.append(f"colors: {match[-1]}")
                else:
                    visual_elements.append(f"colors: {match}")

    # Look for composition descriptions
    composition_patterns = [
        r'composition ([^,.]+)',
        r'framed ([^,.]+)',
        r'angle ([^,.]+)',
        r'shot ([^,.]+)',
        r'perspective ([^,.]+)',
        r'view ([^,.]+)',
    ]

    for pattern in composition_patterns:
        matches = re.findall(pattern, description, re.IGNORECASE)
        if matches:
            for match in matches:
                if isinstance(match, tuple):
                    visual_elements.append(f"composition: {match[-1]}")
                else:
                    visual_elements.append(f"composition: {match}")

    # If we couldn't extract specific elements, use key sentences from the description
    if not visual_elements:
        # Split into sentences and take the first 2-3 that are most descriptive
        sentences = re.split(r'[.!?]', description)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        # Prioritize sentences with visual words
        visual_words = ['shows', 'displays', 'depicts', 'features', 'contains', 'with', 'scene', 'image']
        visual_sentences = [s for s in sentences if any(word in s.lower() for word in visual_words)]

        if visual_sentences:
            visual_elements = visual_sentences[:2]  # Take up to 2 visual sentences
        else:
            visual_elements = sentences[:2]  # Take first 2 sentences as fallback

    # Join the extracted elements
    condensed_description = ", ".join(visual_elements)

    # If the condensed description is too short, use the original
    if len(condensed_description) < 20:
        return description

    return condensed_description

def enhance_prompt_with_subtitles(description: str, subtitles: str) -> str:
    """
    Enhance the image prompt by incorporating relevant information from subtitles.

    Args:
        description: The scene description
        subtitles: The subtitles for the scene

    Returns:
        Enhanced description incorporating subtitle content
    """
    if not subtitles or len(subtitles) < 3:
        return description

    # Remove speaker labels if present (e.g., "John: Hello" -> "Hello")
    clean_subtitles = re.sub(r'^[^:]+:\s*', '', subtitles)

    # Check if the subtitle content is already in the description
    if clean_subtitles.lower() in description.lower():
        return description

    # Add the subtitle content to provide context
    return f"{description} The scene relates to: '{clean_subtitles}'"

def generate_image(
    storyboard: Dict[str, Any],
    characters: List[Dict[str, Any]],
    style: str,
    image_generator_func: Callable[[str], Optional[bytes]]
) -> Optional[bytes]:
    # Construct the prompt
    prompt = storyboard['description']

    # Extract key visual elements from the description
    condensed_description = extract_key_elements(prompt)

    # Enhance with subtitles if available
    if 'subtitles' in storyboard and storyboard['subtitles']:
        condensed_description = enhance_prompt_with_subtitles(condensed_description, storyboard['subtitles'])

    enhanced_prompt = f"{condensed_description} | {style}"

    # Add character descriptions
    character_descriptions = []

    for character in characters:
        name_forms = [
            character['name'].split()[0],  # First name
            character['name'],  # Full name
            f"{character['name'].split()[0]}'s",  # First name possessive
            f"{character['name']}'s",  # Full name possessive
            f"{character['name'].split()[0]}'",  # First name possessive (alternative)
            f"{character['name']}'"  # Full name possessive (alternative)
        ]

        # Check if any non-bracketed form of the name is in the prompt
        if any(
            form.lower() in prompt.lower() and
            f"{{{{{form.lower()}}}}}" not in prompt.lower()
            for form in name_forms
        ):
            desc = f"{character['name']}'s appearance: {character['ethnicity']} {character['gender']} {character['age']} {character['facial_features']} {character['body_type']} {character['hair_style']} {character['accessories']}"
            character_descriptions.append(desc)

    if character_descriptions:
        enhanced_prompt += " | " + " | ".join(character_descriptions)

    # Remove all bracketed content
    enhanced_prompt = re.sub(r'\{\{.*?\}\}', '', enhanced_prompt)

    return image_generator_func(enhanced_prompt)

def generate_and_download_images(
    storyboard_project: Dict[str, Any],
    story_dir: str,
    image_style: str,
    image_generator_func: Callable[[str], Optional[bytes]]
) -> List[str]:
    # Create directory for images if it doesn't exist
    os.makedirs(story_dir, exist_ok=True)

    image_files = []
    image_prompts = []

    # For each scene in the storyboard, generate an image using the specified model
    # and save it to disk
    for i, storyboard in enumerate(storyboard_project['storyboards']):
        scene_number = storyboard['scene_number']

        # Get description for the image - try different fields that might exist
        if 'image_prompt' in storyboard:
            description = storyboard['image_prompt']
        elif 'scene_description' in storyboard:
            description = storyboard['scene_description']
        elif 'description' in storyboard:
            description = storyboard['description']
        elif 'subtitles' in storyboard:
            # If nothing else is available, use the subtitles text
            description = storyboard['subtitles']
        else:
            # Fallback to a generic description if nothing is available
            description = f"Scene {scene_number}"

        # Extract key visual elements from the description
        condensed_description = extract_key_elements(description)

        # Enhance with subtitles if available and not already used as description
        if 'subtitles' in storyboard and storyboard['subtitles'] and description != storyboard['subtitles']:
            condensed_description = enhance_prompt_with_subtitles(condensed_description, storyboard['subtitles'])

        image_filename = os.path.join(story_dir, f"scene_{scene_number}.png")

        # Create image prompt based on description and style
        if image_style.lower() in ["photorealistic", "cinematic"]:
            image_prompt = f"Highly detailed, photorealistic cinematic 8K image, masterful composition, dramatic lighting, shallow depth of field, high-quality production, showing {condensed_description}. trending on artstation, award-winning photography"
        elif image_style.lower() == "anime":
            image_prompt = f"Japanese anime style illustration, vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic, showing {condensed_description}. trending on pixiv"
        elif image_style.lower() == "comic-book":
            image_prompt = f"Professional comic book illustration, dynamic poses, bold outlines, vibrant colors, Marvel/DC style, detailed shading and crosshatching, rich textures, action-packed composition showing {condensed_description}. published comic book quality"
        elif image_style.lower() == "pixel-art" or image_style.lower() == "pixar-art":
            image_prompt = f"Detailed pixel art, 16-bit style, limited color palette, crisp edges, nostalgic retro gaming aesthetic, charming design style showing {condensed_description}. reminiscent of classic SNES games"
        elif image_style.lower() == "digital-art":
            image_prompt = f"Professional digital art illustration, high resolution, vivid colors, detailed textures, fantasy art style, impressive light effects, cohesive composition showing {condensed_description}. trending on ArtStation, most beautiful artwork"
        elif image_style.lower() == "oil-painting":
            image_prompt = f"Fine art oil painting, rich textures, visible brushstrokes, depth through glazing, classical composition, warm color palette, museum-quality, reminiscent of old masters showing {condensed_description}. artistic masterpiece"
        elif image_style.lower() == "watercolor":
            image_prompt = f"Delicate watercolor painting, soft color washes, flowing transitions, subtle bleeding edges, translucent layers, loose brushwork, organic feel, on textured paper showing {condensed_description}. professional watercolor artwork"
        elif image_style.lower() == "dark-aesthetic":
            image_prompt = f"Dark moody atmosphere, deep shadows and high contrast, low-key lighting, limited dark color palette, gothic elements, mysterious atmosphere, dramatic cinematic style showing {condensed_description}. film noir inspired"
        elif image_style.lower() == "neon-cyberpunk":
            image_prompt = f"Futuristic cyberpunk scene, bright neon lights in magenta and cyan, rainy reflective streets, advanced technology, dystopian urban setting, Blade Runner inspired, holographic displays, high-contrast lighting showing {condensed_description}. digital art masterpiece"
        elif image_style.lower() == "minimalist":
            image_prompt = f"Minimalist design, simple geometric shapes, limited color palette, negative space, clean composition, essential elements only, modern aesthetic showing {condensed_description}. elegant simplicity, award-winning design"
        elif image_style.lower() == "film-noir":
            image_prompt = f"1940s film noir aesthetic, black and white with high contrast, dramatic shadows, low-key lighting, moody atmosphere, Venetian blinds shadows, cinematic composition showing {condensed_description}. classic Hollywood noir style"
        elif image_style.lower() == "retro-80s":
            image_prompt = f"1980s retro style, synthwave aesthetic, neon grid, sunset colors, VHS quality, chrome and gradient effects, Memphis design elements showing {condensed_description}. nostalgic 80s vibe, retro futurism"
        elif image_style.lower() == "vaporwave":
            image_prompt = f"Vaporwave aesthetic, glitchy digital art, Roman busts, checkerboard patterns, pink and cyan color scheme, retro computing elements, surreal composition, nostalgic 90s internet culture showing {condensed_description}. digital art masterpiece"
        elif image_style.lower() == "cottagecore":
            image_prompt = f"Idyllic cottagecore aesthetic, rustic countryside setting, warm natural lighting, wildflowers, vintage elements, cozy atmosphere, soft colors, pastoral romanticism showing {condensed_description}. fairytale quality"
        elif image_style.lower() == "hyperrealistic":
            image_prompt = f"Ultra-detailed hyperrealistic 8K image, indistinguishable from photography, extreme textures and details, perfect lighting, flawless rendering showing {condensed_description}. award-winning photography, incredible realism"
        elif image_style.lower() == "flat-design":
            image_prompt = f"Modern flat design illustration, clean vector style, solid colors without gradients, simplified shapes, minimalistic approach, 2D aesthetic showing {condensed_description}. professional graphic design"
        elif image_style.lower() == "3d-cartoon":
            image_prompt = f"3D animated cartoon style, Pixar/Dreamworks quality, exaggerated proportions, smooth textures, vibrant colors, expressive characters, playful design showing {condensed_description}. professional 3D animation"
        elif image_style.lower() == "pastel-dreamscape":
            image_prompt = f"Dreamy pastel fantasy landscape, soft color palette, ethereal atmosphere, magical elements, hazy lighting, whimsical design, floating objects showing {condensed_description}. surreal and enchanting artwork"
        elif image_style.lower() == "fantasy-vibrant":
            image_prompt = f"Vibrant fantasy illustration, rich saturated colors, magical atmosphere, elaborate details, mythical elements, dramatic lighting, epic composition showing {condensed_description}. high-quality digital artwork"
        elif image_style.lower() == "nostalgic-filter":
            image_prompt = f"Nostalgic photograph with vintage filter effect, warm amber glow, slight grain texture, light leaks, faded colors, 70s/80s photography style showing {condensed_description}. authentic retro photography"
        elif image_style.lower() == "vhs-aesthetic":
            image_prompt = f"VHS style image, tracking lines, chromatic aberration, interlaced scan lines, low resolution, bleeding colors, warped edges, 80s/90s camcorder look showing {condensed_description}. authentic analog video aesthetic"
        elif image_style.lower() == "y2k":
            image_prompt = f"Y2K aesthetic, early 2000s design, glossy metallic elements, bright neon colors, bubble shapes, futuristic yet nostalgic, digital artifacts, millennial aesthetic showing {condensed_description}. authentic early digital era vibes"
        elif image_style.lower() == "stick-animation-style":
            image_prompt = f"Stick Animation Style illustration of {condensed_description}. Simple stick figure characters with basic line-drawn bodies and circular heads, minimalist design, clear outlines, basic shapes, clean white background, black line art, simplified representation of scenes and actions. NO TEXT, no words, no writing, no captions, no labels."
        else:
            image_prompt = f"High-quality detailed image of {condensed_description}"

        # Add enhancing terms to improve quality and explicitly request no text
        image_prompt += ", highly detailed, professional lighting, 8K resolution, NO TEXT, no words, no writing, no captions, no labels"

        # Save the prompt for debugging
        image_prompts.append(image_prompt)
        print(f"Image prompt for scene {scene_number}: {image_prompt}")

        # Try to generate image with the specified generator
        try:
            print(f"Generating image for scene {scene_number}")
            image_bytes = image_generator_func(image_prompt)

            if image_bytes:
                with open(image_filename, "wb") as f:
                    f.write(image_bytes)
                print(f"Image saved to {image_filename}")
                image_files.append(image_filename)
            else:
                # If primary generator fails, try the replicate_flux_api as fallback
                from api import replicate_flux_api
                if image_generator_func.__name__ != "replicate_flux_api":
                    print(f"Primary generator failed, trying replicate_flux_api as fallback")
                    image_bytes = replicate_flux_api(image_prompt)
                    if image_bytes:
                        with open(image_filename, "wb") as f:
                            f.write(image_bytes)
                        print(f"Image saved to {image_filename} (using fallback)")
                        image_files.append(image_filename)
                    else:
                        print(f"Fallback generator also failed, creating blank image")
                        create_blank_image(image_filename)
                        image_files.append(image_filename)
                else:
                    print(f"Generator failed, creating blank image")
                    create_blank_image(image_filename)
                    image_files.append(image_filename)
        except Exception as e:
            print(f"Error generating image: {e}")
            create_blank_image(image_filename)
            image_files.append(image_filename)

    return image_files
