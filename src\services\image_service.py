"""
Service to generate images using Replicate, Together AI, FAL AI, or Runware.ai.
Based on the RapidClips implementation.
"""
import os
import base64
import requests
import replicate
from together import Together
import fal_client
from utils import load_config

class ReplicateImageService:
    """
    Service to interact with the Replicate API for image generation using the Flux model.
    """

    def __init__(self, api_token=None):
        """
        Initialize the service with the Replicate API token.

        Args:
            api_token (str): Replicate API token.
        """
        self.api_token = api_token or os.getenv("REPLICATE_API_KEY", "")
        os.environ["REPLICATE_API_TOKEN"] = self.api_token

    def generate_image(self, prompt, width=720, height=1280, steps=4):
        """
        Generate an image based on the given prompt using the model specified in config.

        Args:
            prompt (str): The prompt describing the image to generate.
            width (int): The width of the generated image.
            height (int): The height of the generated image.
            steps (int): Number of inference steps.

        Returns:
            bytes: The generated image data.
        """
        # Load configuration
        config = load_config()
        replicate_config = config["replicate_flux_api"]

        # Add a negative prompt to prevent text in the generated images
        negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

        # Determine if this is portrait or landscape
        is_portrait = height > width

        # Set aspect ratio based on orientation from config
        orientation = "portrait" if is_portrait else "landscape"
        aspect_ratio = replicate_config["aspect_ratio"].get(orientation, "9:16")

        print(f"Replicate service using aspect ratio: {aspect_ratio}")

        input_data = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "aspect_ratio": aspect_ratio,  # Use aspect_ratio parameter directly
            "num_inference_steps": steps,
            "guidance_scale": 7.5
        }
        model_id = replicate_config["model"]

        try:
            print(f"Calling Replicate API with model: {model_id}")
            print(f"Input data: {input_data}")

            output = replicate.run(
                model_id,
                input=input_data
            )

            print(f"Replicate API response type: {type(output)}")

            if isinstance(output, list) and len(output) > 0:
                image_url = output[0]
                print(f"Image URL received: {image_url[:50]}...")
                response = requests.get(image_url)
                response.raise_for_status()
                return response.content
            else:
                print(f"Unexpected response from Replicate API: {output}")
                return None
        except Exception as e:
            print(f"Error in Replicate API call: {str(e)}")
            # Try again with different parameters if there's an issue with dimensions
            try:
                print("Retrying with alternative parameters...")

                # Try with a different aspect ratio format
                # Sometimes the model works better with one format vs another
                alt_aspect_ratio = "0.5625" if aspect_ratio == "9:16" else "1.7778"

                print(f"Trying alternative aspect ratio format: {alt_aspect_ratio}")

                # Alternative input data
                alt_input = {
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "aspect_ratio": alt_aspect_ratio,  # Use numeric aspect ratio
                    "num_inference_steps": steps,
                    "guidance_scale": 7.5
                }

                output = replicate.run(
                    model_id,
                    input=alt_input
                )

                if isinstance(output, list) and len(output) > 0:
                    image_url = output[0]
                    print(f"Retry successful, image URL: {image_url[:50]}...")
                    response = requests.get(image_url)
                    response.raise_for_status()
                    return response.content
            except Exception as retry_error:
                print(f"Retry also failed: {str(retry_error)}")

            return None


class TogetherImageService:
    """
    Service to interact with Together AI API for image generation.
    """

    def __init__(self, api_token=None):
        """
        Initialize the Together AI service with an API token.

        Args:
            api_token (str): Together AI API token.
        """
        self.api_token = api_token or os.getenv("TOGETHER_API_KEY", "")
        self.client = Together(api_key=self.api_token)

    def generate_image(self, prompt, width=720, height=1280, steps=4, model=None):
        """
        Generate an image based on the provided prompt using Together AI's models.

        Args:
            prompt (str): The prompt to guide image generation.
            width (int): Width of the generated image (between 64 and 1792, must be multiple of 16).
            height (int): Height of the generated image (between 64 and 1792, must be multiple of 16).
            steps (int): Number of steps for image generation.
            model (str, optional): The model to use for image generation. If None, uses the model from config.

        Returns:
            bytes: The image data in binary format.
        """
        # Load configuration if model is not specified
        if model is None:
            config = load_config()
            together_config = config["together_flux_api"]
            model = together_config["model"]
        try:
            # Ensure width and height are within the allowed range (64-1792)
            width = max(64, min(width, 1792))
            height = max(64, min(height, 1792))

            # Ensure width and height are multiples of 16
            width = (width // 16) * 16
            height = (height // 16) * 16

            # Add a negative prompt to prevent text in the generated images
            negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

            # Generate image using Together AI
            response = self.client.images.generate(
                prompt=prompt,
                negative_prompt=negative_prompt,
                model=model,
                width=width,
                height=height,
                steps=steps,
                n=1,
                response_format="b64_json",
            )

            # Get base64 encoded image
            image_b64 = response.data[0].b64_json

            # Decode the base64 image
            image_data = base64.b64decode(image_b64)
            return image_data
        except Exception as e:
            raise Exception(f"Error generating image with Together AI: {str(e)}")


class FalAIImageService:
    """
    Service to interact with the FAL AI API for image generation.
    """

    def __init__(self, api_token=None):
        """
        Initialize the FAL AI service with an API token.

        Args:
            api_token (str): FAL AI API token.
        """
        # FAL client expects the key in FAL_KEY environment variable
        self.api_token = api_token or os.getenv("FAL_KEY", "")

        # If FAL_KEY is not set, try FAL_API_KEY as fallback for backward compatibility
        if not self.api_token:
            self.api_token = os.getenv("FAL_API_KEY", "")
            if self.api_token:
                print("WARNING: Using FAL_API_KEY is deprecated. Please use FAL_KEY instead.")

        # Set the API key for FAL client
        if self.api_token:
            os.environ['FAL_KEY'] = self.api_token
        else:
            print("ERROR: No FAL AI API key found. Please set the FAL_KEY environment variable.")

    def generate_image(self, prompt, width=1080, height=1920, steps=4):
        """
        Generate an image based on the given prompt using FAL AI.

        Args:
            prompt (str): The prompt describing the image to generate.
            width (int): The width of the generated image.
            height (int): The height of the generated image.
            steps (int): Number of inference steps.

        Returns:
            bytes: The generated image data.
        """
        try:
            # Add a negative prompt to prevent text in the generated images
            negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

            # Calculate aspect ratio
            aspect_ratio = width / height

            # Determine the appropriate image_size parameter based on aspect ratio
            # FAL AI expects specific string values or a properly formatted object
            if abs(aspect_ratio - 9/16) < 0.1:  # Portrait (9:16)
                image_size = "portrait_16_9"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 16/9) < 0.1:  # Landscape (16:9)
                image_size = "landscape_16_9"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 4/3) < 0.1:  # 4:3 aspect ratio
                if width > height:
                    image_size = "landscape_4_3"
                else:
                    image_size = "portrait_4_3"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            elif abs(aspect_ratio - 1) < 0.1:  # Square
                image_size = "square_hd"
                print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
            else:
                # For custom aspect ratios, use the width/height object format
                # Ensure dimensions are within FAL AI's limits
                max_dimension = 1024  # FAL AI typically has limits on dimensions

                # Scale dimensions to fit within limits while maintaining aspect ratio
                if width > height:
                    if width > max_dimension:
                        scale = max_dimension / width
                        width = max_dimension
                        height = int(height * scale)
                else:
                    if height > max_dimension:
                        scale = max_dimension / height
                        height = max_dimension
                        width = int(width * scale)

                # Use custom dimensions
                image_size = {
                    "width": width,
                    "height": height
                }
                print(f"Using custom dimensions for FAL AI: {width}x{height}")

            try:
                # Load configuration
                config = load_config()
                fal_config = config["fal_flux_api"]

                # Submit request to FAL AI
                print(f"Submitting request to FAL AI with prompt: {prompt[:50]}...")
                handler = fal_client.submit(
                    fal_config["model"],
                    arguments={
                        "prompt": prompt,
                        "negative_prompt": negative_prompt,
                        "image_size": image_size,
                        "num_images": fal_config["num_images"],
                        "num_inference_steps": steps,
                        "enable_safety_checker": fal_config["enable_safety_checker"],
                    },
                )

                print(f"Request submitted, waiting for result...")
                # Get the result
                result = handler.get()

                if result and isinstance(result, dict) and "images" in result:
                    images = result["images"]
                    if isinstance(images, list) and images:
                        image_url = images[0].get("url")
                        if image_url:
                            print(f"Image URL received: {image_url[:50]}...")
                            response = requests.get(image_url)
                            response.raise_for_status()
                            return response.content

                print("No valid image data found in FAL AI response")
                return None

            except Exception as e:
                print(f"Error in FAL AI request: {str(e)}")
                if "422" in str(e) or "400" in str(e):
                    print("Error 422/400: This usually means the parameters are invalid.")
                    print("Trying with predefined image size...")

                    # Try again with a predefined image size based on orientation
                    try:
                        # Use a predefined image size based on orientation
                        if width > height:
                            fallback_image_size = "landscape_16_9"
                        else:
                            fallback_image_size = "portrait_16_9"

                        print(f"Retrying with predefined image size: {fallback_image_size}")

                        handler = fal_client.submit(
                            fal_config["model"],
                            arguments={
                                "prompt": prompt,
                                "negative_prompt": negative_prompt,
                                "image_size": fallback_image_size,
                                "num_images": fal_config["num_images"],
                                "num_inference_steps": steps,
                                "enable_safety_checker": fal_config["enable_safety_checker"],
                            },
                        )

                        result = handler.get()
                        if result and isinstance(result, dict) and "images" in result:
                            images = result["images"]
                            if isinstance(images, list) and images:
                                image_url = images[0].get("url")
                                if image_url:
                                    print(f"Retry successful with predefined size: {fallback_image_size}")
                                    response = requests.get(image_url)
                                    response.raise_for_status()
                                    return response.content
                    except Exception as retry_error:
                        print(f"Retry with predefined size failed: {str(retry_error)}")

                        # Last resort: try without specifying image_size at all
                        try:
                            print("Trying without image_size parameter...")
                            handler = fal_client.submit(
                                fal_config["model"],
                                arguments={
                                    "prompt": prompt,
                                    "negative_prompt": negative_prompt,
                                    "num_images": fal_config["num_images"],
                                    "num_inference_steps": steps,
                                    "enable_safety_checker": fal_config["enable_safety_checker"],
                                },
                            )

                            result = handler.get()
                            if result and isinstance(result, dict) and "images" in result:
                                images = result["images"]
                                if isinstance(images, list) and images:
                                    image_url = images[0].get("url")
                                    if image_url:
                                        print("Fallback without image_size succeeded, but aspect ratio may be incorrect")
                                        response = requests.get(image_url)
                                        response.raise_for_status()
                                        return response.content
                        except Exception as last_error:
                            print(f"Final fallback also failed: {str(last_error)}")

                # Re-raise the exception
                raise
        except Exception as e:
            print(f"Error in FAL AI image generation: {e}")
            # Re-raise the exception to be consistent with other services
            raise Exception(f"Error generating image with FAL AI: {str(e)}")


class RunwareImageService:
    """
    Service to interact with the Runware.ai API for image generation.
    Uses the official Runware SDK when available, with fallback to direct API calls.
    """

    def __init__(self, api_token=None, model_type="flux_dev"):
        """
        Initialize the Runware.ai service with an API token.

        Args:
            api_token (str): Runware.ai API token.
            model_type (str): The model type to use (flux_dev or flex_schenele).
        """
        self.api_token = api_token or os.getenv("RUNWARE_API_KEY", "")
        self.model_type = model_type

        # Load configuration
        config = load_config()
        self.runware_config = config["runware_flux_api"]

        # Get model ID from config
        self.model_id = self.runware_config["models"][self.model_type]["model_id"]

        # Always use direct API calls
        self.use_sdk = False
        print("Using direct API calls for Runware.ai")

    def generate_image(self, prompt, width=768, height=1344, steps=None, model_type=None):
        """
        Generate an image based on the given prompt using Runware.ai.

        Args:
            prompt (str): The prompt describing the image to generate.
            width (int): The width of the generated image.
            height (int): The height of the generated image.
            steps (int, optional): Number of inference steps. If None, uses the value from config.
            model_type (str, optional): The model type to use. If None, uses the model from initialization.

        Returns:
            bytes: The generated image data.
        """
        try:
            print(f"RunwareImageService.generate_image called with model_type={model_type}, current model_type={self.model_type}")

            # Use provided model_type if specified, otherwise use the one from initialization
            if model_type:
                self.model_type = model_type
                self.model_id = self.runware_config["models"][self.model_type]["model_id"]
                print(f"Updated model_type to {self.model_type}, model_id to {self.model_id}")

            # ALWAYS use dimensions from Runware config, overriding any provided dimensions
            # Get orientation based on aspect ratio
            orientation = "portrait"  # Default to portrait
            if width is not None and height is not None and width > height:
                orientation = "landscape"

            # Get dimensions from config
            dimensions = self.runware_config["dimensions"].get(orientation, {})
            # Store original dimensions for logging
            original_width, original_height = width, height
            # Always override with dimensions from config
            width = dimensions.get("width", 640)
            height = dimensions.get("height", 1152)

            # Log if we're overriding provided dimensions
            if original_width is not None and original_height is not None and (original_width != width or original_height != height):
                print(f"Overriding provided dimensions ({original_width}x{original_height}) with Runware.ai config dimensions: {width}x{height}")
            else:
                print(f"Using dimensions from Runware.ai config: {width}x{height}")

            # Use model-specific steps if not provided
            if steps is None:
                if self.model_type == "flux_dev":
                    steps = 28  # Use 28 steps for Flux Dev model
                    print(f"Using 28 steps for Flux Dev model")
                elif self.model_type == "flex_schenele":
                    steps = 4  # Use 4 steps for Flex Schenele model
                    print(f"Using 4 steps for Flex Schenele model")
                else:
                    steps = self.runware_config.get("steps", 20)  # Default from config

            # Get negative prompt from config
            negative_prompt = self.runware_config.get("negative_prompt",
                "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp")

            print(f"Generating image with Runware.ai using model_id={self.model_id}, model_type={self.model_type}")
            print(f"Dimensions: {width}x{height}, Steps: {steps}")

            # Always use direct API calls
            return self._generate_with_direct_api(prompt, width, height, steps, negative_prompt)

        except Exception as e:
            print(f"Error in Runware.ai image generation: {e}")
            # Re-raise the exception to be consistent with other services
            raise Exception(f"Error generating image with Runware.ai: {str(e)}")



    def _generate_with_direct_api(self, prompt, width, height, steps, negative_prompt):
        """
        Generate an image using direct API calls to Runware.ai.
        """
        try:
            # API endpoint
            api_endpoint = "https://api.runware.ai/v1/inference"

            # Generate a random UUID for the task
            import uuid
            task_uuid = str(uuid.uuid4())

            # Prepare the request payload according to Runware.ai API format
            payload = [{
                "taskType": "imageInference",
                "taskUUID": task_uuid,
                "positivePrompt": prompt,
                "negativePrompt": negative_prompt,
                "model": self.model_id,
                "width": width,
                "height": height,
                "steps": steps,
                "CFGScale": self.runware_config.get("guidance_scale", 7.5),
                "numberResults": self.runware_config.get("num_images", 1),
                "outputType": "base64Data",
                "outputFormat": self.runware_config.get("output_format", "PNG")
            }]

            print(f"Calling Runware.ai API with model: {self.model_id} using direct API")
            print(f"Payload: {payload}")
            print(f"API endpoint: {api_endpoint}")
            print(f"API token: {self.api_token[:5]}...{self.api_token[-5:] if len(self.api_token) > 10 else ''}")

            # Set up headers with API key
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_token}"
            }

            # Make the API request
            response = requests.post(
                api_endpoint,
                headers=headers,
                json=payload
            )

            # Check if the request was successful
            response.raise_for_status()

            # Parse the response
            result = response.json()
            print(f"Runware.ai API response: {result}")

            # The response format is different from what we expected
            # According to the documentation, it should be in the format:
            # { "data": [ { "taskType": "imageInference", "taskUUID": "...", "imageBase64Data": "..." } ] }

            if "data" in result and len(result["data"]) > 0:
                # Get the task data
                task_data = result["data"][0]

                # Check if we have the image data
                if "imageBase64Data" in task_data:
                    # Get the base64 encoded image
                    image_b64 = task_data["imageBase64Data"]

                    if image_b64:
                        # Decode the base64 image
                        image_data = base64.b64decode(image_b64)
                        return image_data
                    else:
                        print("No base64 image data found in response")
                else:
                    print(f"No imageBase64Data found in response: {task_data}")
            else:
                print(f"Unexpected response format: {result}")

            return None
        except Exception as e:
            print(f"Error in direct Runware.ai API call: {e}")
            return None


def get_image_service(service_name="replicate", api_token=None, model_type=None):
    """
    Factory function to get the appropriate image service.

    Args:
        service_name (str): The name of the service to use ('replicate', 'together', 'fal', or 'runware').
        api_token (str): Optional API token to use.
        model_type (str): Optional model type for services that support multiple models (like Runware.ai).

    Returns:
        An instance of the requested image service.
    """
    print(f"get_image_service called with service_name={service_name}, model_type={model_type}")

    if service_name.lower() == "together":
        print(f"Creating TogetherImageService")
        return TogetherImageService(api_token)
    elif service_name.lower() == "fal":
        print(f"Creating FalAIImageService")
        return FalAIImageService(api_token)
    elif service_name.lower() == "runware":
        # Always ensure we have a model_type for Runware.ai
        if model_type is None:
            model_type = "flux_dev"
            print(f"No model_type specified for Runware.ai, defaulting to {model_type}")
        print(f"Creating RunwareImageService with model_type={model_type}")
        return RunwareImageService(api_token, model_type)
    else:  # Default to replicate
        print(f"Creating ReplicateImageService (default fallback)")
        return ReplicateImageService(api_token)
