@echo off
echo ===================================================
echo Azanx AutoShorts Secure Build Script
echo ===================================================
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in PATH. Please install Python 3.8 or higher.
    exit /b 1
)

REM Check if Nuitka is installed
python -c "import nuitka" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Nuitka...
    pip install nuitka
    if %ERRORLEVEL% neq 0 (
        echo Failed to install Nuitka. Please check your internet connection and try again.
        exit /b 1
    )
)

echo Starting secure build process...
echo This may take several minutes. Please be patient.
echo.

REM Run the Nuitka build script
python nuitka_build.py
if %ERRORLEVEL% neq 0 (
    echo Build failed. Please check the error messages above.
    exit /b 1
)

echo.
echo Build completed successfully!
echo The secure distribution is available in the Secure_Distribution folder.
echo.

pause
