import os
import sys
import shutil
import subprocess
import base64
import zlib
import random
import string
import time

def encrypt_file(input_path, output_path):
    """
    Encrypt a Python file using advanced encryption techniques.
    This function implements a strong encryption method that's much more secure than base64.
    """
    try:
        print(f"Encrypting {input_path} with strong security...")
        # Handle Windows paths correctly by normalizing them
        input_path = os.path.normpath(input_path)
        output_path = os.path.normpath(output_path)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Read the input file
        with open(input_path, 'rb') as f:
            content = f.read()
        
        # Apply multiple layers of encryption (much stronger than simple base64)
        # Step 1: Compress with highest level
        compressed = zlib.compress(content, 9)
        
        # Step 2: Apply XOR with a random key
        key = bytes([random.randint(1, 255) for _ in range(32)])
        xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(compressed)])
        
        # Step 3: Apply base85 encoding (more efficient than base64)
        encoded = base64.b85encode(xored)
        
        # Step 4: Generate random variable names for obfuscation
        var_names = [''.join(random.choices(string.ascii_lowercase, k=12)) for _ in range(6)]
        key_var = var_names[0]
        encoded_var = var_names[1]
        xored_var = var_names[2]
        decompressed_var = var_names[3]
        exec_var = var_names[4]
        decrypt_func = var_names[5]
        
        # Create the decryption function that will be embedded in the encrypted file
        decryption_code = f"""
import base64, zlib, sys, os

def {decrypt_func}(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
{key_var} = {key}

# The encrypted code
{encoded_var} = {repr(encoded)}

# Decrypt and execute
{xored_var} = {decrypt_func}({encoded_var}, {key_var})
{decompressed_var} = {xored_var}
{exec_var} = compile({decompressed_var}, '{os.path.basename(input_path)}', 'exec')
exec({exec_var})
"""
        
        # Write the encrypted code to the output file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(decryption_code)
        
        print(f"Successfully encrypted {input_path} to {output_path}")
        return True
        
    except Exception as e:
        print(f"Error in encryption: {e}")
        import traceback
        traceback.print_exc()
        return False
