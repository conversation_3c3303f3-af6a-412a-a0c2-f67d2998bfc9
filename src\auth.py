import os
import json
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser

# Import the credentials manager using a relative import
try:
    from credentials_manager import CredentialsManager
except ImportError:
    # If that fails, try with the src prefix (for when running from outside src)
    try:
        from src.credentials_manager import CredentialsManager
    except ImportError:
        # If both fail, create a simple fallback version
        class CredentialsManager:
            def __init__(self):
                pass

            def save_credentials(self, email):
                return False

            def get_credentials(self):
                return None

            def clear_credentials(self):
                return True

            def is_logged_in(self):
                return False

            def get_email(self):
                return None

class LoginDialog:
    def __init__(self, parent):
        self.parent = parent
        self.result = False

        # Initialize credentials manager
        self.credentials_manager = CredentialsManager()

        # Hardcoded configuration
        self.whatsapp_number = "+************"
        self.spreadsheet_id = "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10"  # Your actual spreadsheet ID

        # Properly format the private key to fix padding issues
        private_key = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

        # Hardcoded service account credentials with properly formatted private key
        self.credentials = {
            "type": "service_account",
            "project_id": "azanx-autoshorts",
            "private_key_id": "4290e2725f3599fa5cad66b89365a25d5e1b73ad",
            "private_key": private_key,
            "client_email": "<EMAIL>",
            "client_id": "109901492966103025795",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/azanxautoshorts%40azanx-autoshorts.iam.gserviceaccount.com",
            "universe_domain": "googleapis.com"
        }

        # Create the dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Login - Faceless Video Generator")
        self.dialog.geometry("800x500")  # Wider dialog for two columns
        self.dialog.resizable(False, False)

        # Make it modal
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Force focus and bring to front
        self.dialog.focus_force()
        self.dialog.lift()

        # Set background color
        self.dialog.configure(bg='white')

        # Center the dialog
        self.center_dialog()

        # Create the form
        self.create_widgets()

        # Initialize Google Sheets client
        self.init_google_sheets()

        # Focus on email entry
        self.email_entry.focus_set()

        # Bind Enter key to login button
        self.dialog.bind('<Return>', lambda e: self.verify_login())

    def center_dialog(self):
        """Center the dialog on the screen"""
        self.dialog.update_idletasks()

        # Get screen dimensions
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        # Calculate position
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.dialog.geometry(f"+{x}+{y}")

        # Ensure dialog stays on top
        self.dialog.attributes('-topmost', True)
        self.dialog.update()

    def create_widgets(self):
        # Create main frame with two columns
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(expand=True, fill='both')

        # Left column - App Info (Blue background)
        info_frame = tk.Frame(main_frame, bg='#1a73e8', width=400)
        info_frame.pack(side='left', fill='both', expand=True)
        info_frame.pack_propagate(False)

        # App title and description
        title_label = tk.Label(
            info_frame,
            text="Faceless Video Generator",
            font=('Helvetica', 24, 'bold'),
            fg='white',
            bg='#1a73e8',
            wraplength=350
        )
        title_label.pack(pady=(100, 20))

        description_text = """Create professional Ai Generated Videos. Perfect for content creators, educators, and businesses looking to produce engaging video content without showing their face."""

        description_label = tk.Label(
            info_frame,
            text=description_text,
            font=('Helvetica', 12),
            fg='white',
            bg='#1a73e8',
            wraplength=350,
            justify='center'
        )
        description_label.pack(pady=20, padx=25)

        # Right column - Login Form
        login_frame = tk.Frame(main_frame, bg='white', width=400)
        login_frame.pack(side='right', fill='both', expand=True)
        login_frame.pack_propagate(False)

        # Welcome text
        welcome_label = tk.Label(
            login_frame,
            text="Welcome Back!",
            font=('Helvetica', 20, 'bold'),
            bg='white'
        )
        welcome_label.pack(pady=(100, 10))

        # Email entry with label
        email_label = tk.Label(
            login_frame,
            text="Enter your email address",
            font=('Helvetica', 10),
            bg='white'
        )
        email_label.pack(pady=(20, 5))

        self.email_entry = ttk.Entry(login_frame, width=40)
        self.email_entry.pack()

        # Login button
        login_button = ttk.Button(
            login_frame,
            text="Login",
            command=self.verify_login,
            style='Accent.TButton',
            width=30
        )
        login_button.pack(pady=(15, 30))

        # Separator with "or" text
        separator_frame = tk.Frame(login_frame, bg='white')
        separator_frame.pack(fill='x', padx=50)

        left_sep = ttk.Separator(separator_frame, orient='horizontal')
        left_sep.pack(side='left', fill='x', expand=True, pady=20)

        or_label = tk.Label(
            separator_frame,
            text=" or ",
            bg='white',
            fg='gray'
        )
        or_label.pack(side='left', padx=10)

        right_sep = ttk.Separator(separator_frame, orient='horizontal')
        right_sep.pack(side='left', fill='x', expand=True, pady=20)

        # Purchase button
        purchase_button = ttk.Button(
            login_frame,
            text="Purchase License",
            command=self.show_purchase_info,
            width=30
        )
        purchase_button.pack(pady=10)

    def init_google_sheets(self):
        """Initialize Google Sheets client with hardcoded credentials"""
        try:
            # Use hardcoded credentials
            scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds = ServiceAccountCredentials.from_json_keyfile_dict(self.credentials, scope)
            client = gspread.authorize(creds)

            try:
                # Use hardcoded spreadsheet ID
                self.sheet = client.open_by_key(self.spreadsheet_id).sheet1
                print("Successfully connected to Google Sheets")
            except gspread.exceptions.APIError as e:
                print(f"Google Sheets API Error: {str(e)}")
                self.sheet = None
            except Exception as e:
                print(f"Error accessing spreadsheet: {str(e)}")
                self.sheet = None

        except Exception as e:
            print(f"Error initializing Google Sheets: {str(e)}")
            self.sheet = None

    def verify_login(self):
        """Verify the user's email against the Google Sheet"""
        email = self.email_entry.get().strip()

        if not email:
            messagebox.showerror("Login Error", "Please enter your email address.", parent=self.dialog)
            return

        # If Google Sheets client is not initialized
        if self.sheet is None:
            # Try to reconnect once
            self.init_google_sheets()

            # If still can't connect, show an error
            if self.sheet is None:
                messagebox.showerror(
                    "Error Connecting to Server",
                    "Unable to connect to authentication server. Please check your internet connection and try again.",
                    parent=self.dialog
                )
                return

        try:
            # Get all emails from the spreadsheet
            all_emails = self.sheet.col_values(1)[1:]  # Skip header row

            if email.lower() in [e.lower() for e in all_emails]:
                # Valid email found
                self.result = True

                # Save credentials for future logins
                self.credentials_manager.save_credentials(email)

                self.dialog.destroy()
            else:
                # Email not found in the spreadsheet
                messagebox.showerror(
                    "Access Denied",
                    "Your email is not registered. Please contact support to purchase a license.",
                    parent=self.dialog
                )
        except Exception as e:
            print(f"Error verifying email: {str(e)}")
            messagebox.showerror(
                "Error Verifying Account",
                "There was an error verifying your account. Please try again later.",
                parent=self.dialog
            )

    def open_buy_link(self):
        """Open WhatsApp chat link to buy the tool"""
        # Replace with your WhatsApp number
        whatsapp_number = "+************"  # e.g., "**********"
        whatsapp_message = "Hi! I'm interested in buying Azanx Autoshorts Video Generator."

        # Create WhatsApp link
        whatsapp_link = f"https://wa.me/{whatsapp_number}?text={whatsapp_message}"
        webbrowser.open(whatsapp_link)

    def show_purchase_info(self):
        """Show purchase information in a message box"""
        message = """To purchase a license for Azanx Autoshorts Video Generator:

1. Send a WhatsApp message to: {}
2. Include your:
   • Name
   • Email address
   • Preferred payment method

We'll respond with payment details and activate your account within 24 hours.""".format(self.whatsapp_number)

        messagebox.showinfo(
            "Purchase Information",
            message,
            parent=self.dialog
        )

        # Open WhatsApp chat
        whatsapp_link = f"https://wa.me/{self.whatsapp_number}?text=Hi! I'm interested in buying Faceless Video Generator."
        webbrowser.open(whatsapp_link)

def show_login_dialog(root):
    """Show the login dialog and return True if login successful"""
    # Check if user is already logged in
    credentials_manager = CredentialsManager()
    if credentials_manager.is_logged_in():
        # User is already logged in, no need to show dialog
        return True

    # User is not logged in, show the login dialog
    dialog = LoginDialog(root)
    root.wait_window(dialog.dialog)
    return dialog.result

def logout_user():
    """Log out the current user by clearing saved credentials"""
    credentials_manager = CredentialsManager()
    return credentials_manager.clear_credentials()

def get_current_user_email():
    """Get the email of the currently logged in user"""
    credentials_manager = CredentialsManager()
    return credentials_manager.get_email()