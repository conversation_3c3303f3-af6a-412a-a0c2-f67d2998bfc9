"""
Service to generate image prompts using OpenAI or Groq.
Based on the RapidClips implementation.
"""
import ai_client

class PromptService:
    """
    Service to generate image prompts using the current AI provider (OpenAI or Groq).
    """

    def __init__(self):
        """
        Initialize the prompt service.
        """
        pass

    def generate_image_prompt(
        self, full_subtitles: str, previous_prompts: list, group_text: str, image_style: str = "cinematic"
    ) -> str:
        """
        Generate a prompt for image generation based on subtitle context using Repic Clip integration.
        The AI analyzes the story content to create prompts that prioritize all types of images
        while using the selected image style.

        Args:
            full_subtitles (str): The entire subtitle text for context.
            previous_prompts (list): List of previously generated image prompts.
            group_text (str): The specific subtitle segment to create an image prompt for.
            image_style (str): The desired visual style for the image (e.g., "cinematic", "anime").

        Returns:
            str: The generated image prompt in English.
        """
        # Get style description based on the selected image style
        style_description = self._get_style_description(image_style)

        # Enhanced prompt for Repic Clip integration
        prompt = (
            "You are a creative prompt generator for Repic Clip, a sophisticated text-to-image system. "
            "Your task is to deeply analyze the story content and create image prompts that prioritize "
            "all types of visual imagery (landscapes, objects, abstract concepts, symbolic representations, etc.) "
            "while maintaining a consistent artistic style throughout all generated prompts. "
            f"IMPORTANT: You MUST create prompts in the {image_style} visual style. "
            f"Style description: {style_description} "
            f"Your prompt MUST begin with '{image_style} style:' and include the style description in your prompt. "
            "Analyze the story content for key visual elements, themes, emotions, and settings. "
            "If the context implies a historical or thematic setting, incorporate accurate period elements. "
            "Avoid mentioning or including any text or lettering within the image itself. "
            "Do NOT generate or describe textual elements. "
            "Focus on creating rich, detailed visual prompts that capture the essence of the scene described.\n\n"
            "IMPORTANT: Do NOT include any meta-text like 'Here is a prompt:' or 'Here is a detailed prompt in the X style:'. "
            "Just provide the actual prompt that starts with the style name.\n\n"
            "Start with a hook and avoid phrases like 'welcome to my channel' or channel name type text.\n\n"
            f"1. Full story context (analyze for themes, settings, and visual elements):\n{full_subtitles}\n\n"
        )

        if previous_prompts:
            prompt += (
                "2. Previously generated prompts (do not repeat these ideas; "
                "maintain overall style coherence while introducing new creative angles):\n"
                f"{previous_prompts}\n\n"
            )

        prompt += (
            f"3. Current segment to visualize (analyze deeply for visual potential):\n{group_text}\n\n"
            "Generate a single, detailed, and artistically styled prompt in English that: "
            "1. Captures the essence of the current segment "
            "2. Maintains consistency with the overall story "
            "3. Prioritizes visual elements that will create compelling imagery "
            "4. Uses the specified image style consistently "
            "5. Avoids any text elements or references to text "
            "6. Incorporates relevant historical, cultural, or contextual details if applicable "
            "7. Creates a visually rich scene that enhances the storytelling "
            "8. Avoids sensitive, violent, or disturbing content "
            "9. Focuses on positive, uplifting imagery "
            "\n\nIMPORTANT: Present only one concise, final image prompt now. Do NOT include any meta-text like 'Here is a prompt:' - just provide the actual prompt starting with the style name."
        )

        # Use the ai_client to ensure proper model mapping between OpenAI and Groq
        completion = ai_client.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            model="gpt-4o",  # This will be mapped to the appropriate model for the current provider
            temperature=0.7,  # Slightly higher temperature for more creative prompts
        )

        # Get the raw response from the LLM
        raw_response = completion.choices[0].message.content

        # Clean the response to remove any meta-text like "Here is a detailed prompt in the X style:"
        # This pattern matches common prefixes that the LLM might add
        import re
        cleaned_response = re.sub(
            r'^(Here is|Here\'s|I\'ve created|I have created|This is|Generated|Creating|Here is a detailed prompt in the .* style:).*?:',
            '',
            raw_response,
            flags=re.IGNORECASE | re.DOTALL
        ).strip()

        # If the cleaning removed too much or nothing, use the original response
        if not cleaned_response or len(cleaned_response) < 20:
            cleaned_response = raw_response

        # Add content filtering for sex-related topics only
        sensitive_topics = [
            "explicit", "nude", "naked", "sexual", "porn", "sex", "erotic", "adult",
            "xxx", "nsfw", "18+", "mature"
        ]

        # Check if the prompt contains sensitive content
        contains_sensitive = any(topic in cleaned_response.lower() for topic in sensitive_topics)

        if contains_sensitive:
            print("⚠️ Adult/NSFW content detected in prompt, replacing with safe alternative")
            # Replace with a safe alternative that maintains the style
            style_description = self._get_style_description(image_style)

            # Create style-specific safe alternatives
            if "stickanimation" in image_style.lower() or "stick-animation" in image_style.lower():
                cleaned_response = f"{image_style} style: A cheerful scene with stick figure characters in a park setting. Simple stick figure characters with basic line-drawn bodies and circular heads, minimalist design, clear outlines, basic shapes, clean white background, black line art. NO TEXT, no words, no writing, no captions, no labels."
            elif "anime" in image_style.lower() or "ghibli" in image_style.lower():
                cleaned_response = f"{image_style} style: A peaceful nature scene with trees and mountains in the background. Vibrant colors, clean linework, detailed natural environment, dreamy atmospheric lighting. NO TEXT, no words, no writing, no captions, no labels."
            else:
                cleaned_response = f"{image_style} style: A peaceful landscape scene with natural elements. {style_description}"

        print(f"Original LLM response: {raw_response[:100]}...")
        print(f"Cleaned response: {cleaned_response[:100]}...")

        return cleaned_response

    def _get_style_description(self, image_style: str) -> str:
        """
        Get a detailed description of the selected image style.

        Args:
            image_style (str): The image style name

        Returns:
            str: A detailed description of the style
        """
        # Convert to lowercase and normalize for case-insensitive matching
        style_lower = image_style.lower()
        # Remove spaces and hyphens for more flexible matching
        style_normalized = style_lower.replace(" ", "").replace("-", "")

        # Dictionary of style descriptions with normalized keys
        style_descriptions = {
            "photorealistic": "Hyperrealistic, professional photography, perfect lighting, ultra-detailed, photographic quality. NO TEXT, no words, no writing, no captions, no labels.",
            "cinematic": "Professional lighting, masterful composition, dramatic atmosphere, high-quality production, movie still quality. NO TEXT, no words, no writing, no captions, no labels.",
            "anime": "Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic. NO TEXT, no words, no writing, no captions, no labels.",
            "comicbook": "Professional comic book illustration, dynamic poses, bold outlines, vibrant colors, Marvel/DC style. NO TEXT, no words, no writing, no captions, no labels.",
            "pixarart": "3D animated style, Pixar quality, exaggerated proportions, smooth textures, vibrant colors. NO TEXT, no words, no writing, no captions, no labels.",
            "digitalart": "Digital illustration, vibrant colors, detailed textures, professional digital painting techniques. NO TEXT, no words, no writing, no captions, no labels.",
            "oilpainting": "Traditional oil painting style, rich textures, visible brushstrokes, classical composition, fine art quality. NO TEXT, no words, no writing, no captions, no labels.",
            "watercolor": "Soft watercolor painting, gentle color blending, translucent layers, artistic washes, traditional watercolor technique. NO TEXT, no words, no writing, no captions, no labels.",
            "pixelart": "Detailed pixel art, limited color palette, crisp edges, nostalgic retro gaming aesthetic. NO TEXT, no words, no writing, no captions, no labels.",
            "darkaesthetic": "Moody atmosphere, dark color palette, dramatic shadows, gothic elements, mysterious ambiance. NO TEXT, no words, no writing, no captions, no labels.",
            "neoncyberpunk": "Futuristic cityscape, neon lights, cyberpunk aesthetic, high tech low life, vibrant contrasting colors. NO TEXT, no words, no writing, no captions, no labels.",
            "minimalist": "Clean simple lines, limited color palette, negative space, elegant simplicity, modern design. NO TEXT, no words, no writing, no captions, no labels.",
            "filmnoir": "High contrast black and white, dramatic shadows, moody lighting, cinematic composition, 1940s aesthetic. NO TEXT, no words, no writing, no captions, no labels.",
            "retro80s": "Vibrant neon colors, retro 80s aesthetic, synthwave style, geometric patterns, nostalgic vibe. NO TEXT, no words, no writing, no captions, no labels.",
            "vaporwave": "Pastel colors, retro computing aesthetics, glitch elements, 90s nostalgia, surreal compositions. NO TEXT, no words, no writing, no captions, no labels.",
            "cottagecore": "Rustic countryside aesthetic, natural elements, soft warm lighting, pastoral scenes, cozy atmosphere. NO TEXT, no words, no writing, no captions, no labels.",
            "hyperrealistic": "Extreme photorealism, incredible detail beyond photography, perfect lighting, flawless textures. NO TEXT, no words, no writing, no captions, no labels.",
            "flatdesign": "Modern flat design, clean vector style, solid colors without gradients, simplified shapes. NO TEXT, no words, no writing, no captions, no labels.",
            "3dcartoon": "3D animated cartoon style, exaggerated proportions, smooth textures, vibrant colors, playful design. NO TEXT, no words, no writing, no captions, no labels.",
            "pasteldreamscape": "Soft pastel colors, dreamy atmosphere, ethereal lighting, surreal elements, fantasy-like quality. NO TEXT, no words, no writing, no captions, no labels.",
            "fantasyvibrant": "Vibrant fantasy world, magical elements, rich colors, detailed fantasy environments, otherworldly quality. NO TEXT, no words, no writing, no captions, no labels.",
            "nostalgicfilter": "Vintage film look, subtle grain, slightly faded colors, warm nostalgic tone, retro photography style. NO TEXT, no words, no writing, no captions, no labels.",
            "vhsaesthetic": "VHS tape quality, scan lines, slight distortion, 80s/90s video look, analog video artifacts. NO TEXT, no words, no writing, no captions, no labels.",
            "y2k": "Y2K era aesthetic, iridescent colors, bubblegum pop style, early 2000s digital design, glossy futuristic look. NO TEXT, no words, no writing, no captions, no labels.",
            "godanimevine": "Bold, exaggerated anime style, dramatic expressions, vibrant saturated colors, extreme contrast, dynamic action poses, bold outlines, exaggerated features. NO TEXT, no words, no writing, no captions, no labels.",
            "ghibli": "Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility. NO TEXT, no words, no writing, no captions, no labels.",
            # Add Islamic style
            "islamic": "Rich geometric patterns, arabesque designs, intricate calligraphy-inspired elements, architectural motifs, jewel-toned colors, ornate details, cultural symbols, historical accuracy, elegant composition. NO human figures or faces. NO TEXT, no words, no writing, no captions, no labels.",
            # Add Stick Animation Style
            "stickanimationstyle": "Simple stick figure characters with basic line-drawn bodies and circular heads, minimalist design, clear outlines, basic shapes, clean white background, black line art, simplified representation of scenes and actions. NO TEXT, no words, no writing, no captions, no labels."
        }

        # Print debug information
        print(f"Original style: {image_style}")
        print(f"Normalized style: {style_normalized}")

        # Special handling for specific styles
        if "anime" in style_lower:
            print("Anime style detected, using anime description")
            return "Vibrant colors, clean linework, studio Ghibli inspired, detailed character design, high quality anime aesthetic. NO TEXT, no words, no writing, no captions, no labels."
        elif "ghibli" in style_lower:
            print("Ghibli style detected, using ghibli description")
            return "Soft watercolor-like textures, gentle color palette, whimsical characters, detailed natural environments, dreamy atmospheric lighting, hand-drawn animation quality, Miyazaki-inspired artistic sensibility. NO TEXT, no words, no writing, no captions, no labels."
        elif "islamic" in style_lower:
            print("Islamic style detected, using Islamic description")
            return "Rich geometric patterns, arabesque designs, intricate calligraphy-inspired elements, architectural motifs, jewel-toned colors, ornate details, cultural symbols, historical accuracy, elegant composition. NO human figures or faces. NO TEXT, no words, no writing, no captions, no labels."
        elif "stickanimation" in style_lower or "stick-animation" in style_lower:
            print("Stick Animation Style detected, using stick animation description")
            return "Simple stick figure characters with basic line-drawn bodies and circular heads, minimalist design, clear outlines, basic shapes, clean white background, black line art, simplified representation of scenes and actions. NO TEXT, no words, no writing, no captions, no labels."
        elif "photorealistic" in style_lower:
            print("Photorealistic style detected, using photorealistic description")
            return "Hyperrealistic, professional photography, perfect lighting, ultra-detailed, photographic quality. A majestic scene with intricate details and subtle shading. Rich, deep colors with subtle highlights, evoking a sense of realism and depth. NO TEXT, no words, no writing, no captions, no labels."

        # Return the description for the specified style, or a default if not found
        description = style_descriptions.get(style_normalized)
        if description:
            print(f"Found style description for: {style_normalized}")
            return description
        else:
            print(f"No exact match found for style: {style_normalized}, using default description")
            return "High-quality detailed image with professional lighting and composition."
