
import base64, zlib, sys, os

def isuehoulxczp(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
hjjceoqhqkpe = b'\xc9\x14\x88\x1b\xf4\xccu\xc9\x1f\x96\xcae\xc9sL\xba\t\x05B\xfdD\xfa\x14\xa8\x9c\xc7\x86p%P\x90\xcb'

# The encrypted code
xeusdjuvamub = b'vCd^ijlXUVifL8)t9fFr2?Bz*@@Xt|&T*dlv&q=sf{<&o(Bei>c9yBDEJ_4>Q+wh7SOL*q!MaO)ZHo1*qQ4|Dug=qMkU=G#bkJ8(qs_oe4Ja<)E_E*IMIwL;Ry@SWUv43=hssX?`;cRi95bl5A)lucHe4jCh}s}QFm=+O1(_i&^zsQhm0uGDaDMlJKZIsoQ5Hi-?(>J-&c}-aQJ#;Mb|(`-Ff}tqw<|CJt6Uz7Da|FDre)svPt_AYr?%5GEUBjx><8~9G)mx`Qs(g!#W%Fs#LruOg|RKHC~Yj)V}zE4Bf|r)W#4A2Eoi?|ype4XhDy#b+$uGxyX%7z+%}}ophL5X_jn2^T!t~win}7$-8Tf?{(k%ecncOf!bOi2TV?_1?POT}HHiU98w6(PlX94H7PL{Fxok^acE^j#(&qbWbfrY2NvysR$?b_ca8T9Vue?VLHP_%z$1oH_tYf1{IZogQbVAI&EB6HQdt&G=ipH$5YRRhJByP3+4l?+^XH>iU!qmd44F5{&0n)8~6v6(x+uF1^DH3N0oo0woPoM{6?M5x60yKe~s!vA>B%&ZrM4CNjQrl_Zef`!11;<p|0$KHohW6UY?y5p=sZ7lk9d02^iq&q4LdAw}C%q46DQdl#8|G0RKQxtvM#Ap$oQsV#J#PFerxW^W4#{M$1WCt~w(aIXIr)9}#Ma%))-S5z!LAZkQFHhnm`1wR!ULh%J~d7$?@xvbSW4!}7{c~iqxj(<l2GT`1JDaD!t{m6U2e(me87js*)##I3qPuO0(3=7d@~L@--3gz*8l$sUIMW|9iPdw<;jU+nyfhS7OSr=8V4&u54+r?BGsee#uLNI_@&7;(X5bMf0%p<V9Sn3*-Q)Eju;E5BqUji0HG?fCH7~CVt#6CXcKMW4%1-E+AASy9rbWA9Isp;f~F6J<|OWrojrI=t<ETmiKQqQHM~>lFH%K}7W*rxi8m{4D^#qPB=-0WPz5jWM0dlb(#~C)y(CEJ^fsfNvwhTk$sa|a2w*o-AmM+H3vf_=P#y+CzfLfnx^WQqk`=bEk+GKl8Xe%0#07%n7YH7a!+SL1WRE^W>TYRw|2#QYP+Nal!+zY6)+YU)0l*5IKo!2BV+nS5#ldEH7Z(hv<ImZW>}?!lUd3%T1B1v|c0Q&8!DU~h{rCS&+rK%3>4Ace2{0#!IohMoT=<KE2?xw0Up>e5{iD@fYflE(SJg^3;0KTSBBxZ%agwje<B|Vh<y_kTDHsWW7~i-?6&<T5o}XwgY!}Q+c&Dc#6Qy`m0I3A&2T3{d#11;#ugh}L1c^`Y^}urTGt!UA5OP%0lyBFV0x6F?U+f)V$Upaj{-iFM&jGihc$)`P0NgFL9Jl$^cADTZ%3rlC*&PT8byV>@v@#x{l$Cm5sAewTdSmr+$#eC)DDg@pF8h-)Yn)7ch0McbPcZ;74nuuzMXmdtgUzsj4^6Hk2NnKX{7B7Y?;@YH<(z$!?-%+~VyTfqYH1$-l|4IF9BV!a{5ZTb|5SNBA)U(P?U;(wE%B#g8Q>R99WO+)yUwZ{5*|c=BW&RMygmr;Md|GzNe1$#1a2M{TPTbW-eB5N<#jw(Ai+R{Derh=v$2xaqv!M_J`nz3mej~xVG;pGg3nNnOLioz@;~A?>>F|`-ATP4avh0r|6^Zjr?7~YD-D(kJr+Rw+pfciNN_W@V+Bwpse#nkhl!OdxOO1g0VW{Kg__+{^s?`?S9<*-dT!TKqtOhz%%xG@Ca_EP2Zl43lGJcseSu8oXpV}<OhjAHr#)1^F1#eoIepS|t}=YtDMQ_)SQqlCL1|1v5oudDNbyING>Yo)`(j#;jJ^W^tk7vB7p0D5&neyK!F~KzMg()|hDgS3?n??X*+Od_<(fnzkf8z>@<OkKD5~L2UOG4L(UDSBy<T-w{`5cr?2Oc3I5QomF4b*w0?0{C)bL@R^b5S&x5Bs+{<5gT<KZ3b_q3AGfN9^8bX3R8uek0e8WU~)^u%}-m>;Y3>|dUM@Ik^BQdyPOi7mNZtsr?USMD*j4_SNaO&)M%Q+v4D{bjInq<FkiVMbQA`cXX;dmJR%^#a=X*BwaG`U!gE6HcJ{pRQfwJ*cAKgb>wsCmIEOBr4EbA?}^8ZpKQ&IWW5=-12GfE#m$Kamq&d<%RrlAs3C&Vls?`W&N*&9lF4$Cr*CYOV}47bF|O|5?B8A?Pgi|+%w2lyR0gUbi+e#g3A}-8N!GDjYnn7&%?ZZPGY<MI2+)g!prVqZ%cC5$T~TG*_iImR*OibxY8(88sAQ3xL)!<l8<b`YAO$G1%wK&$v`oDX7RgklLc4{2%w$^0)+4d_pxLVh<im^Z&U7F$*4xp6y-JJD`OUjP%{0Y0slmfex-t+->HuuY7qmFh75{LtL_(j$bZF{Hq#SJH@^!7)}rs<&z9HS<=QO)Yu}S09FSX@DGQl)TDh6C9YTSzs1<sRPT(nQe6q+2=%WYKLDJnl*evI#FKwEPgGgrq4Xf@AE?&z_rH7}B=?56@1dv(Mas8<tc17oVHV>mqv_aFOywH(V&Wl?L{hKaRj05qN>myX-nP)J&8-m5m&DU_PB>t1izE>Mk&*9@Z`u0aLt?;%;Ze}p3Njbr$v7GqQtI;e`NA-yYCot}1^+px<_%}Ixrc4Dpm=^1<&1NvI1^~1L{|Dec?$N)EA4xZZp&?%0MJsFog)cijMxr9bZ>M?q{CB|rImI@<5-&0G`EOj;Je-U(Z9aei8(*|sm8U4PUb0(?ba(cSfuN>_ma~(vI?(;?A-yF9P6i;z(-!U|l3vlc-LD(+J?LyQy#^ld-$s>}MYyh2w$X7p3d3s-wF~Vy1jP7#CccCt=InJ1*CU;~vqYGy#{kA?%2u&ZIl**}kaLNg0oqiSdc#liqn$AHmwy3zo^9u5UBV7k86FgWhZUD|G^=#0`iB?xP{B4U$H|J7_>7=oZE=B(zoa0ES-zACGq>w5>uV$?hq2lo?Six0Ns5*mttd2PS^N8<{m&QWZ7yj&ziEh~k^GmpV$agVB7Yop<Z}I06@+N-QfHg@kNOh-4HyeypHI=HYz^@@d<Ho=&l?SqWYH?%%9k$fA<qwEfAj%Yp<l=)i?2qtM4=#)rCknf26T-Iqd~3k!FwXw^B2l->l<2e=4)^3(C&Kqq2K5LT-B%NGFx7WX2PxM8;b_c)9@u|MaVu*l}2j^Rr?A4ZQClQVkRWL##!S>l`D@n8%l$|cu)Pfq`h~ljEBtLz-gJzV#v<*&}U38VOK@-NUYGgSaHZiZ28&aoF_yFS)W#HUn~+7WD;7$MlO`VyD-Uijy6xMvALK6bS|9LnY7NO588rx7GOZ}ykMYsLz9n+r5)p$2r92&>Ck5+_VC%gtpr?&!2;d8M9fyqMF)0cNX3yX>H!pa8yZ7VYQBjlJ7{uV1a=!sNCu)_6J<IV&l&MaRmCENs}6Yc$>kr7s4f`YfZZ*~FY0}mJsXj@7nYw(!w5zG4SS_c6D(|eiddG+JgmBEseja*W71tNzta-p;@ql(X6)BNGh7Mu(XA6Ka<#-(rk%U2G;b0iO1aBiZ9t5F8d<4JMUX13wKbTG70qWI!i)~LNnpsmF$Msma$)&^6Bc<e48_TOO3FYb5-b)27Mu!1HN8+YY?R$Zo22gdJcr$Ij&jEojdZ@>AFKLr=4mwDsg{t8{?wI%6Yzyn@8^%-ax}oN85R{Yhp6=JC!5?sg_vLarGS`d22@7aZ~=7;Ud|=j7OhL}aS|G0fcF_8$lVXh(bD`$tG@`!505W3Y*m~9BoDB~*cRG8$BJy=rCea};Vk$tZY&^j5+&y8ZdGtTA*tl$V3($>8K&1NyDTqop+Bf)VE9)Qu;z|y$x2@`$=wM1$x_=}9SYLeN$_t%lX#veB-^g76y`L4nFa2Jw{NM*ja&(RnOt+xl*;N**_n;d?2ll>B<rF%`VkLu$8iFtpSntX)kWXTNmS8{ONhjh|7ZSuz#%eU?XSUjc@G+>3wf!6{E@)DbyQ!%Q;!CJvy7FYdJaM$YNG`reOJkbmiuZR+S8GlDrcPH5DloRZFP8o**2CE6ys<d2%ikQ=8Qp?TP>`v2!;F6VR+r&eMS(~EI4h>3BC;igg?Ptx6%3S81LeODI}=l>)C}LYi(Mnn6%|j2hdDonn)1+BL6)nj~HEywo803G3e>*>E=VukyVOAVxc<Vf_=w?0{|^25DNq$VVCLGnSlNdsx%}W$-HLdmG);$uBms?|Kp%#qL;zALjHiF$tL?OJY;o3@mIsYR_&bAS~uA~STRkeaXA<8VNh%cp);q*!d_VEA$}$380S0oyGp<sF6W}be-oIX-aWp(*8JRn{z>nbm2cnLb2F#l*|v(Lk_}VyM_2os0bLM(gsh?IP+H0BcwhY>QDOAEqh``zV|D&JP4Y$#$T`!r)i$dq7&H&d+netV%&%Qh$}kF#r#^ek@MMJmS<O(}zkmRb<My9!<KwGj^ONz;wqlbxu?&_oJReB2twt&28THQ1^%Hc{|H_i{4MKKTK@aCHIdvV!=vt!r5#TVaeSyj}8sDKD4NKa(4tVj156oxHvB|Z_gv9RsaQUP4mNlr~6yD;kzvaM#F=^;>jZF$&2%5yD;rCx?2NLro*lpOu4ncPluB;{%|ALRYX38c<-ba_r(OE`na9m}PxQh$!!AJO`gCtfUL(24XeKTUwm-)R$UF8QhY}ubD5&<yZT~;ws?*FVLZo5kge-0g(m=Mjew6-jZgd#J%9aMMpX2<l8yhp)&4YxXTt7k>4L*NyOuJ?!kj~(r!eG*6W<U2_5B?Cp>)E<Ept*J*#h(`Bjf*2)o0$jyAfsxUq=?N@Xk{biE`x;fL9cuwBB)L*5_I^t45|7Et7#!17TvzNO)SYY4kL@OC04D#fiJ2`RLN8G*81@{XW9{De<Ee|t^aDi5%dE^W18Y+vm}(~7)x?!^Q}zB!x+&@b&V=FyoulTppHx*RHTDo-Vt5>O_!3HClN%;gKvPWPoZAEr)gOjxrjeu>GK%?ZCsKnZjq_NVgj+{i$|gu0#X)w#zULrd@3^SIP+5)5Tl7K<mh@f)u+mnulAuEk=o5ZbYpcIxF=PW=c74D+b_#U>V&kLlYAZYonRU{yh5S^K?U%Ob&wyIxG@N|^xbrSVEfkq(H`4gURA8hLIME4<dNAv}^KwL|b)<z0rX5k{bb#Gq5jB=xDgQvtS|i)UTxya&DY2DRqoISLP>xvJ2DE&l4h1MCsUe@T%A5T%CYVR;%V-O<5(D|r2*&XsB=oXaUIbu{$Ka}b_V4<R2vKdqr{yfD*Adzb1{)UgU>A^mV^oBlt-{m3x4>H+BkRALGrqo3Wy~nMe90FcUoQdsf)qAew)XWGo;8{PJ+ASbGP+kLWFSJAFi}QaUk{X>4Q&lzy&!{KuY8+QX~9Lkt8$YZ1tRkNfh^r=+g#xHB~EXkQh}~Je<2N1+pHS&|IBEgF#rxiCa}O<6n!DAbMUjF;!h4eH5=L%qpXlihP)EKu3}X-b0rFg)0i18eQ68K4^T8Ek2T|zPWI(VcoQDLEEM(hW|aYGs)8=%r6UbxR|84HA8CBS^9|xM^Z(M}6nT0-k#wYlm7Z==r)iO3xIX-E3&d>wgjHlU_4`l6aMao-gFGZHPYRDp5ZG*EPsD+Zy)7JSrK7J7`I~vh0EY!f70f=b`yOwHPTKrivR39VV#l1v>gK<tY%1t(nTZOyy!52J+HM-IIW7j`)9ed?W++%L^C}KmBmM@qv%VnHzcSSYoXW=f@%}*1wj}b1(fN`GyX<|V1lyx0Uk#ky?P?+av{g2&pR5v!SInZ=s3!yhtcmfXIK6VHqw7Mr*L6qu3g1f05O%x<cO+BX+G}tofmrnt|M1;<qwdu#R(9g3+p>)&gYG1@cTZ-BV3h^pRRFozM-uG@$rFih>}r_Xl~9}<rKxHh5dK$J1*6k32%rzzf6g#SlDNJw8U%8O<Np1L%lP^8%q5SmNtWaft#9;`aY`FA+QZJ+;UX5|ON?hmlj>}=tbaYQIeFdVC#?0rW>+9TH~x9HBMCbqmy^T#5P37vLVh~RII~}R{X*G2_7PmR40qtCYhrdGG%()Dw=p$S-_mfP@=zAXWcudF8$|!H^=Zv5V9)Uy?<+%*CC!2$QCWi9y){|6TZW)xu)8)OD>DcVB(^01Su4ZNaHZNf!yC}cCQ(EpEm}83cT?}N({e%a9;m!LMPO^DPo7Lk<{CtRj9^QzdR!MDZ=hFBLgH31h_S$t26>gH5CZgt<NesaOKhq_F34F9W7i-t;@t_?xG|wtW9iCh<8@uBcr=5%7Z6y_gC_{yJ@7m@J&WIT%Ci`y><8R5iMMxtpr<&=WXS``d#HpDiKRIW?mCd0WJIP6sj@o*X-o6QPJnB&ZI>m41Qb5b;pX>A^AN&6!+CtNg`j;t*IgAhqAqif^Rsb_91RR#K9%T@n!~9N3w76&<u?AXhH+WiC@|(#kHhEdfX1vf^dX7`O?$?$yb;kuuOD3csa8luA5p3$?zM&_)P>C+c}hL&A1(o$edRfnn!O7iym1h=AvlrSFH?5`XQ+)zc1SkCC>C&fkp3G(zwUt1oEeob09x;01d$^hl85TA)RHWkJz=4_D~<gu*JZ3>adqnMuw0E#fp=G$8!d!xkLjJn<Qbn7EAerd)wC6jhb?_21V16rI$&q|i9;Q3`oyg0BffcHWjO{_L>d<pHfL3S@ZrE&P)7ptaDB+~b-o(tZmNaL(M0^px^f+?QQD*NG?j+ht}kD4C-3J1nXzVI$I?0pXtWUoPW^o;@8ZO&6Zu$=HdQY>b(K)}*;<NK?``!H-7DMO{)5Iiq~2E;=Pz3TnE~)Y9Mm`E;RHH6QIQBjF%;H98uu0)VH-+C$B076{AKtoWVV5lc+wXsalOU<DE6TGz|SL5v$Hd=C=<HhP$wkr@Q`9?rGzo4-QPl%Bvt0cbXr5@R#j{Lm1ZS*U`*`+o0Zlq@P0@&95)avlk9q`j-a(4uW3g>&~add!M@pIP(z);shiNmfic~L&iYk66O)d5ptvJUDZ@qAr49_0PS^65<(Bt=d26H_{@*kI*@??g20Z@nVZkM+GJxgnAdfpOz&I07vEhbaZ4vB*Km{&Kt}(CCu^*H|RZYc7iB+4ok;C=NC!05tHOgJ&5pE$$6!lNK%SkBE<-eZplq`n!uR%5?w#p;(+o2sTOOz8o&DHrkB~5sqJ3s1aqnmA_k-Oe}L>Lf{97gSs?4-Si1A%1JKbsa}S#UGABRMgDWRa06!a(OYP(pl~j%bRR8_K?m1%~=50X3mmXhjb;Zg_)1<8h`7=i^UbZ9ZdwUC$^i%nfsp28`?}#HK2&38q?lgJ7GumM+&lawcD(UcdZDJEpUY|EPHY&du;ugVVgbfsB-pc}PjYqO@3Lb4%~}R}sg&DIqeKtoelI_H+Y5(o9d4u$|hNBxuD10_JF2*_<&co)iu`_GtaoWpbnX0Xu_NCOQk9=TD7L5FV(C`7^I4Bn*L%WkQx87Am?>27XO)&28E3G@;pWW4C6X%4GqO$!a&(_Tx^E4C=4I{nVjY{~CuUA--9C^f_e1*!gC%ga^7&%SJL{3U{Um{Kj68QTv$0dP^bz0wzb&n7+`R{_W3EquJNDc`r?pqdIJ<*>w+oCDYcUTWCuQPzu<LA-wn&&jO6PFGuI1k#B#;wy_@&80>zCrJ9IsFxW(F)E(CzOTSusIdeH_j$^#i+z<Q&tZiH<kCpgiNt|X}2B-3`Wd&DXTF%+~{W~M(y|{A?co8CXC!tx9c5_B+E<3Z<CVBjGhP0S}6@PZ*34N|j$5+02A-%yq8!uZR^8>m843t38k^U&5fFIqn^$F`UCR!!vZhbX78Vy}vc6Jb5zVEM!^ghuMDMK+D;9_E_q#0Vt+*G7}Q5B&dfuaRz<>K_thZJefaYBa@w?l}NIvmhIb^!iB1AuDq$^oFqEHR+}$~9*q->_mZUI8bMI$G}%ea7DqD)>$?@xLajW$D(-sML#6+Klm-uYT>4oGVgDhP*e9%nH#OaCWrXCN7TL4A}O3LS4vxu2TIsrLb(HV!ES@%JX|%>WP`a4dCdGq-$FZxp8(NP5=Ev_)=!^0Cty6d4y4$)_f>8qNb8%M!obVNAi?-Kv-Q{InFe#?@UWpNm%GRxI=30hX2Ji{v0I$MUqMZ(15;QiZrbR+2Al!u)urSn-*d0gTdRiXga=7I5&vV_FkXyq?*EKQ$^E9E{ia|Cpqbu(y8umhKazuMp?~q=h7hGy6bB5hvKp>%omC@vcx4=9`4x<-$ce|S#zqff?U^0K|0#wQN{gHgXpkVjn7BXkvLVymrDxV>(!~(fH^-Ek=r19xbh9MPN?rmN}-n=WeiMU@%_R9Fuf%QWXhq#UC^QboPms{L#YSAUhdO|EvjVsT;H}%E`fQEmnSxd^56oS^QhtZGvH<CC!mX<1M09JF`DOjMk>Ivk1OgLSwwqPDNKDC)66>{dSjU$2ksBgwP~5+DXb<faoR>y-7tjW;|*qq*{f(zdhu6Xn`h80nwiv93Zx+Cpo3NISTFi=zLJ>PDUFD6`k;T|7Z(kF^At#R&*m%5<Bf3oj>xm0Yt`}n<Wk}0gKIZNu!lga<-l|={(z=%4c&+2t?M3l`T%Z>u2*K#4FDM2p#ne-SXroGk-wXCd&Z-WoLek{pAGFqZUL|gbes3ohu)dU!bG-!sTRr<wDS)UADJS|+x45-@j%t&MtOY#vXtKI0wLJSW>WWIX<vz&yEVNc1r$P@PW<ZwsM!UN?#Z{~3;08(3|<TZ-SoKUtp?2N0_s9d+&5oC^WBx*TZCJvN&qptmtK0!g+m`D=Y#$BJ&%;lM8+yj2YRs)PYQl~gHEc;cv!x&NX2^ngs`+;*0435PVU$%F$rBPe;eqf6cLe1>=%;tdfYvD!t(N%?Z04CNLLW~rv*XWuIn^Xtotv?_o2w}d*m#QpYV@BNS2354KlIJeImLzu|`TJY(UD~fJhJ*l@de&Z%I29W?x+HsSCoI$kqzn?pPEWS>L%6ypYeZvC$J`f#$6_PQcRlFw<G;rYC9I^}h<WnNH4!E^SHVcK#g(s`t|4@$W7;E#J+nz~@TcK~MWVT<}gTJHI;FRuM63JU{#~YYR`QrCW;1YVrpf(lpcR7Vl#9hKd}AVZ=;QkJ~v8P_AOHum;pyZ$D7A8JoC~D>vu;MeO|tvN!^KJVF0!xlb*UBa|w-d)Qwgg9NFGqdqHB{CpsJo<@)e$!FEw6Vj@OE%0P6xFH#^Z6L-g=JCe`l$+gRczyHA(G?pe_zdn%Q_tN-JKy;pvrG(rotS2JWaS5%6|cY@S}OGb(Scp$cz7-w85J{<6{k-P9oJpU^l%XWN7%CxeM3KfIk<(%D?VV=`g%IOLI*r_!!6|q%th931ayiVx%v5ufIi$pLLw6dob@CaD#cdkAp4E(%(({?cSap;$n|r{fA-hXV!rm*8ohw++U8c`6-I8nkfPQDBqv5@YpcFz_-1X=S*b!0r;;%UUXM6g6)LRS>i&l<(vhl6@}M49rOiV=IR9Q4Q|3z{`}~{)Ls6+ayk7W|$hQD-gtU~Z3WmgEZ<n}OgHe)5%E|tx!q73a^b~hUfPw^vb9R2sZwFlq_PMCe1!YLOCyfAvGqkAJvtwG+rECPWjE5t&s=B|I%Vw$FG_m28`1VAw9Sd5wA-sGI?k+fg4Yd@VMW8tK8Nn;FSW5sg)0ORC;qsa?QA$}`=yHAQ!1^Q&-d8n2PXm&IJp#`VH4d^c_*V$6LlM$w<@uwq&Aqqm`NEGpCbTXSgfDpdzC)uOG8Na109eW%G7O<zhg!tNb=;g&8Ul41PFpu3V<^2y6>ATb%z)6Nc8{8LS$}95DQ1e?4m0mONUf^X;%m+kGpev&-26~uI01uAbv0wT;1YL4VgmlCnNWthtLJz##DXER!=lwQq<*dv4}*$Z5#=3PvN^fLoMH@TbWDcG_lwGPqms>P=KbF)d*}&1>e6)r_JTy4hw->ywT3$fUuiHlz+w@*{^rWbx`c>a4H8mFxs>CKSw^A35aC??QY{FfrAKU!MAfZjTzp0gOW;_=kM1XOG&1w1YO9RI{PooV8RY$(`V&|y-S2iw9(z3KaoPxbOxT$-Ix3gs@e4TTissJ4`jUNCB$cDS5NpTQ^tNdF7pz&j@DGxew6;rSTCYfZ!+bt)+4FqA(5z)<pD2kuoW@hZz$}O>)Nb}Q;WTgVN4ExnbP@I<&r6QfRF2y8Oc%3_Q!>?@E$>qSvUb6Af{gan)+jp8AA<uc>KOFiVfRf-^k2Wji=NR}%j1oR#J_rFN+M^*^bn6t!hOg%@C?8Po+UsiV)c@^TDk+DqIx&3f~0p7l&>L9G0><ZMmc}sadoYZmC>#x%*ULOG&6;UeQfDSzB8m;ovQi^90Fm;;LP9J(>hF|M?TZj@XvG^&@L&gDv~Bt_R0v_Rd+x=n%yw}`+;+AjtxC<<P)=nZAHxOF!1Zzui7~~7R@SW*0$={*xk>t%B~pIej*J&LDqrf)ii4otsc0`3ClZ?-SFF8Gx&fmeWtPxKlNs9D$0+A;*e|5Iigy19&e-e0>{U@W<2VVIvP*WnypHX(rqh1heGfBorMNu;LLwGedhh`C1($}v6}z<Iz=27l7=w_b?6p|_h@S4{|`(gi$y1GXNIJ%_nZ1uGGm36S_cvLH3WwMmJ?rb<5)0j9E-RJM9!`ZJ`bEy0mPQyXD`dwpO4Uxew-WBuyuJ8KvJD<x*|bm<t=<n?RB5t0Ys4^EtLWJ(~)da1=$CS9MvGzmZBzbjLfNDTgo(24~}qHD@LW{m9T0<oS>2^iTE2;-qe0f+5VAyTY)qKOBB_4U}L4c3!cEcc)lwA5b;P$#d&HzMhReMRettZyY3A{dC@~3X8rKtO=`Plp2T+acLSPD0>;!MBn^V~C&uFg`iU=N7jBR|DxdMBBF7Z1Rwl<+A5^L656dBQ8i0@_8f0TsljX$}Q7q0Lf*&T>@XB<>baOKY(<y@wS8xFo9ht*l35J31Z49Ss+~GmwjPLxpCAwQ4h)&P7QAPzjDwhT~_}H53dhx^2?z!1r)oUQtjcj5^V0{F(i=A}$+$Hj?$`=U)dHJo8wQWIT)s7t|vG48Y2q#DXVeCVi`EUQWPq~X|59y1&UjsV8V_B3wth9*+q*mv%u-XNzQbpQzMTPoY%8&2+4NUrl(D^6oN7)oXw5u{<+g|;IoMa3uldHmv4d))Z9JU>cXtuK{8*hv&=goJ+OHumkH@t>64+Ie*=S+rU!CouG&3>BTCjGuU;Na_912{tO=PS>&|4sDm>z|F%p^pXG^UH}z-p=7_Yez=Jb_}uO{hx&c$(YDg<FquECg*?-GFHiSGGq8NzN7*q5^Z+oGS{;0p07=DAwD`Y_F!<&MeP7=sxse7{4N~dz9lW3D$^ga%ln3e%C!funDHUQH-C)g)1Ice|Earf-F1WXp(gaxE(DUMgITfGe-E&w3`vDq!2O{mXHj`kdnZpWcgfjMFOwhjP(4+zk=6-3H1ZeyMG*fW*UM(Q3k-~ip402i{*g)e10j-zfzS$3DunM#z4kv*`#pshnMSNYM+PL|y2@=l_&#90DU3zms*eQ(@8GMv__-k`hoe$2;g}6H*bB?$?H_7Bd8j-?<W9HA7vFIb;OcoXtK2wyoqAvcIGTCFHEhM?-*kk}7z@5sHp10n2|YAX8=tI8JMHiB^N=y}B2rDYg6eACw90<4U15r~TSnfs$La{G_-lBx&zDs?sUyj_0qifB?9D&J*4Z{c2yz8_iDtR+I}jd3dSBd_XFI}n+$mde9BU*<_x8Co=6wPJ%<d&8aN*wHH^4zZ76-AGOH;*(Mz8b8HpNBGUOR#%{0zW|N=qqduwpBk#3Sq$`6oHD&u?+@f27)VTt|(#;uX0BogS|Dc@`4u8)}FUgpsMHuyS6&KYzVAi&~<yrqcAU8ey*;6Nwd2zfu+cDcvrJ{RmKn{({^=%X9U%f-!reURLeLR6cR0YzyUq#b9Hmnn1%p5js25qFvLrt<dkwN`7`a28n|_;nPOi*0sKZh{MC3YBx{GQ3rI`qTOS76;Yh@t)nyZ60qGoJcdUFkN?DqRl=1pH}!NfH~WvDdKvzXCC`dveRb<S#*3-3?2L{`3;sM)l;(?g6Ss%i%vHsnHze|I{#UdkqdvzNVB|`r{WQRUYh~w0sFxpVQ?$S?r@*uS5_Bbeu|nPI6)%sW>uP&hZ3kRj9}}NA&m=>cbOP3GeuEpusPB&BqeCBGUidh4(OZx>19Lot7Gcf7zsQ*CmGu+EpM;5?qD(>!uNl{}4uqc!kaoA=p7*yJ*JJE;BAM(m#m?cqq(WdVePL7XLrHYXx(4Ca9kSIztP$7A(!B1o#hKTWu$R&O)=qvB=Q<5=U(%bMO1GjH6;p^WDe;K&R;iY%R~2?~j&e*AEm$YjYxAGD(@iG^VcP^I1t=zT4v<S#rP0;4cnr}YZHdA!DZvPXN0M#|RP0?WyMD}uSQ!x6Sk2Stt1|X6d~#1Bs4&x%N2Ca556wGj{IX(2_n^~UCb`QL`K_sLV;;C;&2{-fMlgFY@YfVqf-dnd6SOozhVg5bGlM>m*5O@N&z^^P0lNhO#`@u#t2~~*&0yHk5UNa-16W)tQBBEn$x!(J&g5&|zuSr;bcb=7Od?$^<o`5Ue18Ypw^|Ep)!F~qJCw6J#rQ&RE)PN{hSvP1Kk+@Gt00XE=gH(X&<TM{;xUSTUG;`OP^TXb`&a`vGJX=G&s-AHR4D-E1etFRi}Vky8E3w_K`*|0L$$pzK0z@u4jN|fS?8#6M1}!Z)<1s1&$Sv$4J1@!AHaKt)LqgP)F#{0=_l`5A@cWQB-OV+-cjxFG_AVlo%Md*&`Bcju6*cp-atHf<Jhb8XZ$8B;pCl{%2hiwXPk$U?(lNsFHVAJjxcghf(G-y`5vD&-nSr~+5'

# Decrypt and execute
gfdddtajnfwg = isuehoulxczp(xeusdjuvamub, hjjceoqhqkpe)
xukfljsrsyri = gfdddtajnfwg
tqaiughtozaj = compile(xukfljsrsyri, 'fixed_story_generator.py', 'exec')
exec(tqaiughtozaj)
