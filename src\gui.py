import tkinter as tk
from tkinter import ttk, messagebox, font
import threading
from openai import OpenAI
import os
from dotenv import load_dotenv
from main import generate_story, generate_storyboard, process_video
from ttkthemes import ThemedTk

class VideoGeneratorThread(threading.Thread):
    def __init__(self, story_type, image_style, voice_name, font_name, callback):
        super().__init__()
        self.story_type = story_type
        self.image_style = image_style
        self.voice_name = voice_name
        self.font_name = font_name
        self.callback = callback

    def run(self):
        try:
            # Initialize OpenAI client
            load_dotenv()
            client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

            # Generate story
            self.callback("Generating story...")
            story = generate_story(client, self.story_type)
            story["image_style"] = self.image_style

            # Generate storyboard
            self.callback("Creating storyboard...")
            storyboard_project = generate_storyboard(client, story)

            # Create video
            self.callback("Creating video...")
            story_dir = storyboard_project["story_dir"]
            audio_dir = os.path.join(story_dir, "audio")
            video_path = os.path.join(story_dir, "story_video.mp4")
            process_video(client, storyboard_project, video_path, audio_dir, self.voice_name, self.font_name)

            self.callback("Video generation completed!")
            messagebox.showinfo("Success", "Video generation completed successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

class ModernButton(ttk.Button):
    def __init__(self, master, **kwargs):
        super().__init__(master, **kwargs)
        self.configure(style='Accent.TButton')

class MainWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("Faceless Video Generator")
        self.root.geometry("800x600")

        # Configure styles
        style = ttk.Style()
        style.configure('Accent.TButton', font=('Helvetica', 10, 'bold'))
        style.configure('Title.TLabel', font=('Helvetica', 24, 'bold'))
        style.configure('Subtitle.TLabel', font=('Helvetica', 12))

        # Create main container
        main_container = ttk.Frame(root, padding="40")
        main_container.pack(fill=tk.BOTH, expand=True)

        # Title
        title = ttk.Label(main_container, text="Faceless Video Generator", style='Title.TLabel')
        title.pack(pady=(0, 30))

        # Story Type Selection
        story_frame = ttk.Frame(main_container)
        story_frame.pack(fill=tk.X, pady=10)
        ttk.Label(story_frame, text="Story Type:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        self.story_combo = ttk.Combobox(story_frame, values=[
            "Scary", "Mystery", "Bedtime", "Interesting History",
            "Urban Legends", "Motivational", "Fun Facts",
            "Long Form Jokes", "Life Pro Tips", "Philosophy", "Love",
            "Islamic"
        ], state="readonly", width=30)
        self.story_combo.pack(side=tk.LEFT, padx=10)
        self.story_combo.set("Select a story type")

        # Image Style Selection
        style_frame = ttk.Frame(main_container)
        style_frame.pack(fill=tk.X, pady=10)
        ttk.Label(style_frame, text="Image Style:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        self.style_combo = ttk.Combobox(style_frame, values=[
            "Photorealistic", "Cinematic", "Anime",
            "Comic Book", "Pixar Art"
        ], state="readonly", width=30)
        self.style_combo.pack(side=tk.LEFT, padx=10)
        self.style_combo.set("Select an image style")

        # Voice Selection
        voice_frame = ttk.Frame(main_container)
        voice_frame.pack(fill=tk.X, pady=10)
        ttk.Label(voice_frame, text="Voice:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        self.voice_combo = ttk.Combobox(voice_frame, values=[
            "Alloy", "Echo", "Fable", "Onyx", "Nova", "Shimmer"
        ], state="readonly", width=30)
        self.voice_combo.pack(side=tk.LEFT, padx=10)
        self.voice_combo.set("Select a voice")

        # Font Selection
        font_frame = ttk.Frame(main_container)
        font_frame.pack(fill=tk.X, pady=10)
        ttk.Label(font_frame, text="Font:", style='Subtitle.TLabel').pack(side=tk.LEFT)
        available_fonts = [
            "TitanOne-Regular", "Ranchers-Regular", "RampartOne-Regular",
            "PermanentMarker-Regular", "OpenSans-Regular", "NotoSans-Regular",
            "Montserrat-Regular", "LuckiestGuy-Regular", "Knewave-Regular",
            "Jua-Regular", "Creepster-Regular", "Caveat-Regular",
            "Bungee-Regular", "BebasNeue-Regular", "Bangers-Regular",
            "BakbakOne-Regular"
        ]
        self.font_combo = ttk.Combobox(font_frame, values=available_fonts, state="readonly", width=30)
        self.font_combo.pack(side=tk.LEFT, padx=10)
        self.font_combo.set("TitanOne-Regular")

        # Progress Bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_container, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=20)

        # Status Label
        self.status_label = ttk.Label(main_container, text="Ready to generate video")
        self.status_label.pack(pady=10)

        # Generate Button
        self.generate_button = ModernButton(main_container, text="Generate Video", command=self.generate_video)
        self.generate_button.pack(pady=20)

        # Initialize video generator thread
        self.generator_thread = None

    def update_status(self, message):
        self.status_label.config(text=message)
        self.root.update()

    def generate_video(self):
        # Validate selections
        if self.story_combo.get() == "Select a story type":
            messagebox.showerror("Error", "Please select a story type")
            return
        if self.style_combo.get() == "Select an image style":
            messagebox.showerror("Error", "Please select an image style")
            return
        if self.voice_combo.get() == "Select a voice":
            messagebox.showerror("Error", "Please select a voice")
            return

        # Disable the generate button while processing
        self.generate_button.state(['disabled'])
        self.progress_var.set(0)

        # Get selected options
        story_type = self.story_combo.get()
        image_style = self.style_combo.get()
        voice_name = self.voice_combo.get()
        font_name = self.font_combo.get()

        # Create and start the generator thread
        self.generator_thread = VideoGeneratorThread(
            story_type, image_style, voice_name, font_name, self.update_status
        )
        self.generator_thread.start()

def main():
    root = ThemedTk(theme="arc")  # Using a modern theme
    app = MainWindow(root)
    root.mainloop()

if __name__ == "__main__":
    main()