from moviepy.editor import (
    ImageClip,
    TextClip,
    CompositeVideoClip,
    AudioFileClip,
    CompositeAudioClip,
    VideoFileClip,
    concatenate_videoclips,
    ColorClip
)
from moviepy.audio.AudioClip import AudioClip, concatenate_audioclips
from audio_generator import generate_audio
from transitions import zoom, dissolve, zoom_with_dissolve
import os
import re
import json
import custom_captioner
import v3_captioner
from audio_processing import adjust_background_music_volume, mix_audio_with_background, add_audio_buffer
from pydub import AudioSegment
from PIL import Image
import numpy as np
from utils import load_config

script_dir = os.path.dirname(os.path.abspath(__file__))
font_path = os.path.join(os.path.dirname(script_dir), "font")
audio_path = os.path.join(os.path.dirname(script_dir), "audio")

def add_subtitles(output_file, output_file_subtitle, font_name="TitanOne", font_color="white", font_size=70,
              outline_color="black", outline_size=3, position="bottom", caption_words=3,
              highlight_current_word=True, word_highlight_color="yellow", highlight_style="text_color",
              highlight_bg_color="#3700B3", highlight_bg_opacity=0.7):
    # Check if font_name is already a full path to a font file
    if os.path.exists(font_name) and (font_name.lower().endswith('.ttf') or font_name.lower().endswith('.otf')):
        # It's already a full path to a font file, use it directly
        font_file = font_name
        print(f"Using provided font file path: {font_file}")
    else:
        # Store original font name
        original_font_name = font_name

        # Strip off any "-Regular" suffix if present
        if "-Regular" in font_name:
            base_font_name = font_name.replace("-Regular", "")
        else:
            base_font_name = font_name

        # Check for both .ttf and .otf extensions
        font_found = False
        font_file = None

        # First try with the original font name
        for ext in [".ttf", ".otf"]:
            temp_font_file = os.path.join(font_path, f"{original_font_name}{ext}")
            if os.path.exists(temp_font_file):
                font_file = temp_font_file
                font_found = True
                print(f"Found font file for subtitles: {font_file}")
                break

        # If not found, try with base name (without -Regular)
        if not font_found and base_font_name != original_font_name:
            for ext in [".ttf", ".otf"]:
                temp_font_file = os.path.join(font_path, f"{base_font_name}{ext}")
                if os.path.exists(temp_font_file):
                    font_file = temp_font_file
                    font_found = True
                    print(f"Found font file for subtitles (without -Regular): {font_file}")
                    break

        # If font not found, try to find any available font
        if not font_found:
            print(f"Warning: Font {font_name} not found for subtitles, looking for alternatives...")

            # Try TitanOne first
            titan_font = os.path.join(font_path, "TitanOne.ttf")
            if os.path.exists(titan_font):
                font_name = "TitanOne"
                font_file = titan_font
                print(f"Using TitanOne as fallback font for subtitles")
            else:
                # Find any available font in the directory
                found_any_font = False
                for file in os.listdir(font_path):
                    if file.lower().endswith(('.ttf', '.otf')):
                        font_file = os.path.join(font_path, file)
                        font_name = os.path.splitext(file)[0]
                        found_any_font = True
                        print(f"Using {font_name} as fallback font for subtitles")
                        break

                if not found_any_font:
                    print(f"No fonts found in font directory. Using system default font for subtitles.")
                    font_file = "Arial"  # Use system font

    # Convert hex color codes to color names if needed
    # custom_captioner accepts both but web color names are more reliable
    if font_color.startswith('#'):
        # Keep common color names, otherwise use the hex code
        if font_color.upper() == "#FFFFFF":
            font_color = "white"
        elif font_color.upper() == "#000000":
            font_color = "black"
        elif font_color.upper() == "#FFFF00":
            font_color = "yellow"
        elif font_color.upper() == "#FF0000":
            font_color = "red"
        elif font_color.upper() == "#00FF00":
            font_color = "green"
        elif font_color.upper() == "#0000FF":
            font_color = "blue"

    if outline_color.startswith('#'):
        # Keep common color names, otherwise use the hex code
        if outline_color.upper() == "#FFFFFF":
            outline_color = "white"
        elif outline_color.upper() == "#000000":
            outline_color = "black"
        elif outline_color.upper() == "#FFFF00":
            outline_color = "yellow"
        elif outline_color.upper() == "#FF0000":
            outline_color = "red"
        elif outline_color.upper() == "#00FF00":
            outline_color = "green"
        elif outline_color.upper() == "#0000FF":
            outline_color = "blue"

    # If word highlight color is a hex code, convert it
    if word_highlight_color.startswith('#'):
        if word_highlight_color.upper() == "#FFFFFF":
            word_highlight_color = "white"
        elif word_highlight_color.upper() == "#000000":
            word_highlight_color = "black"
        elif word_highlight_color.upper() == "#FFFF00":
            word_highlight_color = "yellow"
        elif word_highlight_color.upper() == "#FF0000":
            word_highlight_color = "red"
        elif word_highlight_color.upper() == "#00FF00":
            word_highlight_color = "green"
        elif word_highlight_color.upper() == "#0000FF":
            word_highlight_color = "blue"

    # Print debugging information
    print(f"Font file path: {font_file}")
    print(f"Font exists: {os.path.exists(font_file)}")
    print(f"Font color: {font_color}")
    print(f"Font size: {font_size}")
    print(f"Outline color: {outline_color}")
    print(f"Outline size: {outline_size}")
    print(f"Caption position: {position}")
    print(f"Words per caption: {caption_words}")
    print(f"Highlight current word: {highlight_current_word}")
    print(f"Word highlight color: {word_highlight_color}")

    try:
        # First try with the v3 captioner (based on version 3.0 which worked well)
        try:
            print("Using v3 captioner for reliable captions...")

            # Add captions to the video using the v3 captioner
            v3_captioner.add_captions(
                video_file=output_file,
                output_file=output_file_subtitle,
                font=font_file,
                font_size=font_size,
                font_color=font_color,
                stroke_width=outline_size,
                stroke_color=outline_color,
                highlight_current_word=highlight_current_word,
                word_highlight_color=word_highlight_color,
                words_per_caption=caption_words,
                padding=70,
                position=position,
                animation_style="none",  # Always use none for animation style
                highlight_style=highlight_style,
                highlight_bg_color=highlight_bg_color,
                highlight_bg_opacity=highlight_bg_opacity
            )
            print(f"Successfully added captions with v3 captioner: {output_file_subtitle}")
            return
        except Exception as v3_error:
            print(f"Error with v3 captioner: {str(v3_error)}")
            print("Falling back to original captioner...")

            # Using custom_captioner as fallback
            custom_captioner.add_captions(
                video_file=output_file,
                output_file=output_file_subtitle,
                font=font_file,
                font_size=font_size,
                font_color=font_color,
                stroke_width=outline_size,
                stroke_color=outline_color,
                highlight_current_word=highlight_current_word,
                word_highlight_color=word_highlight_color,
                words_per_caption=caption_words,
                padding=70,
                position=position,
                animation_style="none",  # Always use none for animation style
                highlight_style=highlight_style,
                highlight_bg_color=highlight_bg_color,
                highlight_bg_opacity=highlight_bg_opacity
            )
    except Exception as e:
        print(f"Error adding captions: {str(e)}")
        # Try again with simpler parameters
        print("Attempting to add captions with default parameters...")
        try:
            # Try with v3 captioner but simpler settings
            try:
                print("Trying v3 captioner with simplified settings...")
                v3_captioner.add_captions(
                    video_file=output_file,
                    output_file=output_file_subtitle,
                    font="Arial",  # Use Arial for maximum compatibility
                    font_size=40,
                    font_color="white",
                    stroke_width=2,
                    stroke_color="black",
                    highlight_current_word=False,  # Disable word highlighting for simplicity
                    words_per_caption=caption_words,
                    padding=70,
                    position=position,
                    highlight_style="text_color",  # Default to text color highlighting
                    highlight_bg_color="#3700B3",
                    highlight_bg_opacity=0.7
                )
                print(f"Successfully added captions with simplified v3 captioner: {output_file_subtitle}")
                return
            except Exception as v3_simple_error:
                print(f"Error with simplified v3 captioner: {str(v3_simple_error)}")

                # Fall back to original captioner with simplified settings
                custom_captioner.add_captions(
                    video_file=output_file,
                    output_file=output_file_subtitle,
                    font=font_file,
                    font_size=50,  # Reduced font size
                    font_color="yellow",  # More visible color
                    stroke_width=3,       # Standard stroke width
                    stroke_color="black",  # Standard stroke color
                    words_per_caption=caption_words,   # Use the user's selected value
                    position="bottom",     # Default position
                    animation_style="none",  # No animation
                    highlight_current_word=True,  # Keep highlighting
                    highlight_style="text_color",  # Default to text color highlighting
                    highlight_bg_color="#3700B3",
                    highlight_bg_opacity=0.7
                )
        except Exception as e2:
            print(f"Second attempt failed: {str(e2)}")

            # Absolute last resort - copy the original video
            try:
                import shutil
                print("All caption attempts failed. Copying original video as output.")
                shutil.copy2(output_file, output_file_subtitle)
                print(f"Copied original video to: {output_file_subtitle}")
            except Exception as copy_error:
                print(f"Error copying original video: {str(copy_error)}")
                raise


def create_video(client, storyboard_project, output_file, audio_dir, voice_name, font_name="TitanOne",
                tts_model="OpenAI", font_color="#FFFFFF", font_size=70, outline_color="#000000", outline_size=3,
                caption_position="bottom", caption_words=3, highlight_current_word=True,
                word_highlight_color="#FFFF00", bg_music_path=None, bg_music_volume=0.2,
                end_pause_duration=2.5, orientation="portrait", video_quality="720p",
                highlight_style="text_color", highlight_bg_color="#3700B3", highlight_bg_opacity=0.7,
                background_video_path=None):
    # Create audio directory if it doesn't exist
    if not os.path.exists(audio_dir):
        os.makedirs(audio_dir)

    # Normalize font name by checking if it exists
    original_font_name = font_name

    # Remove "-Regular" suffix if present
    if "-Regular" in font_name:
        base_font_name = font_name.replace("-Regular", "")
    else:
        base_font_name = font_name

    # Check for both .ttf and .otf extensions
    font_found = False
    font_file = None

    # First try with the original font name
    for ext in [".ttf", ".otf"]:
        temp_font_file = os.path.join(font_path, f"{original_font_name}{ext}")
        if os.path.exists(temp_font_file):
            font_file = temp_font_file
            font_found = True
            print(f"Found font file: {font_file}")
            break

    # If not found, try with base name (without -Regular)
    if not font_found and base_font_name != original_font_name:
        for ext in [".ttf", ".otf"]:
            temp_font_file = os.path.join(font_path, f"{base_font_name}{ext}")
            if os.path.exists(temp_font_file):
                font_file = temp_font_file
                font_found = True
                print(f"Found font file (without -Regular): {font_file}")
                break

    # If font not found, try to find any available font
    if not font_found:
        print(f"Warning: Font {font_name} not found, looking for alternatives...")

        # Try TitanOne first
        titan_font = os.path.join(font_path, "TitanOne.ttf")
        if os.path.exists(titan_font):
            font_name = "TitanOne"
            font_file = titan_font
            print(f"Using TitanOne as fallback font")
        else:
            # Find any available font in the directory
            found_any_font = False
            for file in os.listdir(font_path):
                if file.lower().endswith(('.ttf', '.otf')):
                    font_file = os.path.join(font_path, file)
                    font_name = os.path.splitext(file)[0]
                    found_any_font = True
                    print(f"Using {font_name} as fallback font")
                    break

            if not found_any_font:
                print(f"No fonts found in font directory. Using system default font.")
                font_name = "Arial"
                font_file = font_name  # MoviePy will use system font

    # Process background music if provided
    final_bg_music_path = None
    if bg_music_path and os.path.exists(bg_music_path):
        final_bg_music_path = bg_music_path
        print(f"Using background music: {os.path.basename(bg_music_path)}")
    elif bg_music_path:
        print(f"Warning: Background music file not found: {bg_music_path}")
        final_bg_music_path = None

    clips = []
    scene_audio_files = []

    # Check if we're using a background video instead of AI-generated images
    using_background_video = background_video_path is not None and os.path.exists(background_video_path)
    background_video_clip = None

    if using_background_video:
        print(f"Using background video: {background_video_path}")
        try:
            # Load the background video and mute its audio
            background_video_clip = VideoFileClip(background_video_path).without_audio()
            print(f"Background video loaded, duration: {background_video_clip.duration} seconds")

            # Get video dimensions based on orientation and quality
            try:
                config = load_config()

                # Get dimensions from config based on quality and orientation
                if video_quality in config["video_resolutions"] and orientation in config["video_resolutions"][video_quality]:
                    dimensions = config["video_resolutions"][video_quality][orientation]
                    width = dimensions["width"]
                    height = dimensions["height"]
                    print(f"Resizing background video to {width}x{height} for {video_quality} {orientation}")

                    # Get the aspect ratios
                    target_aspect_ratio = width / height
                    video_aspect_ratio = background_video_clip.w / background_video_clip.h

                    print(f"Target aspect ratio: {target_aspect_ratio:.2f}, Video aspect ratio: {video_aspect_ratio:.2f}")

                    # Check if we need to crop or scale
                    if abs(target_aspect_ratio - video_aspect_ratio) > 0.1:  # Aspect ratios are significantly different
                        print(f"Aspect ratios differ significantly. Using center crop and scale approach.")

                        if orientation == "portrait" and video_aspect_ratio > 1:  # Landscape video for portrait output
                            print(f"Converting landscape video to portrait: center cropping and scaling")

                            # Calculate crop dimensions to maintain center of video
                            crop_width = background_video_clip.h * target_aspect_ratio
                            crop_x1 = (background_video_clip.w - crop_width) / 2
                            crop_x2 = crop_x1 + crop_width

                            # Crop the video to match the target aspect ratio
                            background_video_clip = background_video_clip.crop(x1=max(0, crop_x1),
                                                                              y1=0,
                                                                              x2=min(background_video_clip.w, crop_x2),
                                                                              y2=background_video_clip.h)

                            # Then resize to the target dimensions
                            background_video_clip = background_video_clip.resize((width, height))

                        elif orientation == "landscape" and video_aspect_ratio < 1:  # Portrait video for landscape output
                            print(f"Converting portrait video to landscape: center cropping and scaling")

                            # Calculate crop dimensions to maintain center of video
                            crop_height = background_video_clip.w / target_aspect_ratio
                            crop_y1 = (background_video_clip.h - crop_height) / 2
                            crop_y2 = crop_y1 + crop_height

                            # Crop the video to match the target aspect ratio
                            background_video_clip = background_video_clip.crop(x1=0,
                                                                              y1=max(0, crop_y1),
                                                                              x2=background_video_clip.w,
                                                                              y2=min(background_video_clip.h, crop_y2))

                            # Then resize to the target dimensions
                            background_video_clip = background_video_clip.resize((width, height))
                        else:
                            # For other cases, use a different approach - resize and then crop
                            print(f"Using resize and crop approach for mixed aspect ratios")

                            # First resize to match the larger dimension
                            if orientation == "portrait":  # Target is portrait
                                # Resize to match height
                                scale_factor = height / background_video_clip.h
                                new_width = int(background_video_clip.w * scale_factor)
                                background_video_clip = background_video_clip.resize((new_width, height))

                                # Then crop width if needed
                                if new_width > width:
                                    crop_x1 = (new_width - width) / 2
                                    background_video_clip = background_video_clip.crop(x1=crop_x1,
                                                                                      y1=0,
                                                                                      x2=crop_x1 + width,
                                                                                      y2=height)
                            else:  # Target is landscape
                                # Resize to match width
                                scale_factor = width / background_video_clip.w
                                new_height = int(background_video_clip.h * scale_factor)
                                background_video_clip = background_video_clip.resize((width, new_height))

                                # Then crop height if needed
                                if new_height > height:
                                    crop_y1 = (new_height - height) / 2
                                    background_video_clip = background_video_clip.crop(x1=0,
                                                                                      y1=crop_y1,
                                                                                      x2=width,
                                                                                      y2=crop_y1 + height)
                    else:
                        # Aspect ratios are similar, just resize
                        print(f"Aspect ratios are similar. Simply resizing to {width}x{height}")
                        background_video_clip = background_video_clip.resize((width, height))
                else:
                    print(f"Warning: Could not find dimensions for {video_quality} {orientation}, using original video size")
            except Exception as e:
                print(f"Error setting video dimensions: {str(e)}")
                print("Using original background video dimensions")
        except Exception as e:
            print(f"Error loading background video: {str(e)}")
            print("Falling back to using AI-generated images")
            using_background_video = False
            background_video_clip = None

    # Process each scene
    total_audio_duration = 0
    for scene in storyboard_project['storyboards']:
        # Generate audio for the subtitle
        audio_file = scene.get('audio')  # Use the audio path directly from the storyboard

        # Check if audio file path is None or empty
        if audio_file is None or not audio_file:
            # Create a default audio path
            scene_num = scene.get('scene_number', 'unknown')
            if isinstance(scene_num, str):
                safe_scene_num = re.sub(r'[^\w]', '_', scene_num)
            else:
                safe_scene_num = str(scene_num)

            # Create a default audio path
            audio_file = os.path.join(audio_dir, f"scene_{safe_scene_num}.mp3")
            scene['audio'] = audio_file
            print(f"Created default audio path for scene {scene_num}: {audio_file}")

        # Ensure the audio directory exists
        audio_dir = os.path.dirname(audio_file)
        os.makedirs(audio_dir, exist_ok=True)

        # Check if subtitles is None or empty
        if scene.get('subtitles') is None or scene['subtitles'].strip() == "":
            print(f"Warning: Empty subtitles in scene {scene.get('scene_number', 'unknown')}. Using placeholder text.")
            scene['subtitles'] = "[No text]"  # Use a placeholder

        # Generate the audio file
        generate_audio(client, scene['subtitles'], audio_file, voice_name, tts_model)
        scene_audio_files.append(audio_file)

        # Verify the audio file exists before proceeding
        if not os.path.exists(audio_file):
            print(f"Error: Audio file not found at {audio_file}")
            print(f"Creating a placeholder audio file")
            # Create a placeholder silent audio file
            silent_audio = AudioSegment.silent(duration=2000)  # 2 seconds of silence
            silent_audio.export(audio_file, format="mp3")

        # Create audio clip
        audio_clip = AudioFileClip(audio_file)

        # Add a silent buffer with smooth fade-in and fade-out transitions to prevent audio glitches
        # Using minimal buffer duration to reduce pauses between scenes
        audio_clip_with_buffer = add_audio_buffer(
            audio_clip,
            buffer_duration=0.2,
            fade_in_duration=0.1,  # Quick fade-in at the start of each audio clip
            fade_out_duration=0.12  # Slightly longer fade-out for smoother ending
        )

        # Track total audio duration for background video
        total_audio_duration += audio_clip_with_buffer.duration

        if using_background_video:
            # If using background video, we'll create a subclip for each audio segment
            # We don't need to create individual clips here, we'll handle it after processing all scenes
            continue
        else:
            # Check if we have a valid image path
            if scene.get('image') is None or not os.path.exists(scene['image']):
                print(f"Warning: No valid image found for scene {scene.get('scene_number', 'unknown')}. Using a black frame.")
                # Create a black frame as a fallback
                # Create a black image with the same dimensions as the video
                try:
                    # Get dimensions from config
                    config = load_config()
                    dimensions = config["video_resolutions"][video_quality][orientation]
                    width = dimensions["width"]
                    height = dimensions["height"]

                    # Create a black frame
                    black_frame = np.zeros((height, width, 3), dtype=np.uint8)
                    black_image = Image.fromarray(black_frame)

                    # Save to a temporary file
                    temp_image_path = os.path.join(os.path.dirname(audio_dir), f"temp_black_frame_{scene.get('scene_number', '1')}.png")
                    black_image.save(temp_image_path)

                    # Use the black frame
                    image_clip = ImageClip(temp_image_path).set_duration(audio_clip_with_buffer.duration)
                except Exception as e:
                    print(f"Error creating black frame: {str(e)}. Using a simple color clip.")
                    # Fallback to a simple color clip
                    image_clip = ColorClip(size=(640, 1280), color=(0, 0, 0)).set_duration(audio_clip_with_buffer.duration)
            else:
                # Create image clip with duration matching the audio (including buffer)
                image_clip = ImageClip(scene['image']).set_duration(audio_clip_with_buffer.duration)

            # Combine image, text, and audio
            video_clip = image_clip.set_audio(audio_clip_with_buffer)

            # Apply transition effect
            transition_type = scene.get('transition_type', 'none')  # Default to 'none' if not specified

            # Apply zoom with dissolve for all clips with faster transitions
            # These parameters are reduced to minimize pauses between scenes
            fade_duration = 0.3  # Reduced visual fade duration for faster transitions
            audio_fade_in = 0.1  # Shorter audio fade-in duration
            audio_fade_out = 0.12  # Slightly longer fade-out for smoother ending

            if transition_type == 'zoom-in':
                processed_clip = zoom_with_dissolve(
                    video_clip,
                    mode='in',
                    fade_duration=fade_duration,
                    audio_fade_in_duration=audio_fade_in,
                    audio_fade_out_duration=audio_fade_out
                )
                clips.append(processed_clip)
            elif transition_type == 'zoom-out':
                processed_clip = zoom_with_dissolve(
                    video_clip,
                    mode='out',
                    fade_duration=fade_duration,
                    audio_fade_in_duration=audio_fade_in,
                    audio_fade_out_duration=audio_fade_out
                )
                clips.append(processed_clip)
            else:
                # Default to zoom-in with dissolve if no transition specified
                processed_clip = zoom_with_dissolve(
                    video_clip,
                    mode='in',
                    fade_duration=fade_duration,
                    audio_fade_in_duration=audio_fade_in,
                    audio_fade_out_duration=audio_fade_out
                )
                clips.append(processed_clip)

    # If using background video, create a single clip with all audio
    if using_background_video and background_video_clip is not None:
        print("Creating video from background video with overlaid audio...")

        # Check if background video is long enough
        if background_video_clip.duration < total_audio_duration:
            print(f"Warning: Background video ({background_video_clip.duration:.2f}s) is shorter than total audio duration ({total_audio_duration:.2f}s)")
            print("Looping background video to match audio duration")

            # Create a list of clips to concatenate
            bg_clips = []
            remaining_duration = total_audio_duration
            while remaining_duration > 0:
                clip_duration = min(background_video_clip.duration, remaining_duration)
                bg_clips.append(background_video_clip.subclip(0, clip_duration))
                remaining_duration -= clip_duration

            # Concatenate the clips
            background_video_clip = concatenate_videoclips(bg_clips, method="compose")

        # Trim background video if it's too long
        if background_video_clip.duration > total_audio_duration + end_pause_duration:
            print(f"Trimming background video to match audio duration plus end pause")
            background_video_clip = background_video_clip.subclip(0, total_audio_duration + end_pause_duration)

        # Create a composite audio track from all scene audio files
        all_audio_clips = []
        current_time = 0

        for audio_file in scene_audio_files:
            audio_clip = AudioFileClip(audio_file)
            # Add buffer and fades with minimal buffer duration to reduce pauses
            audio_clip_with_buffer = add_audio_buffer(
                audio_clip,
                buffer_duration=0.2,
                fade_in_duration=0.1,
                fade_out_duration=0.12
            )
            # Set start time for this audio clip
            audio_clip_with_buffer = audio_clip_with_buffer.set_start(current_time)
            all_audio_clips.append(audio_clip_with_buffer)
            current_time += audio_clip_with_buffer.duration

        # Create a composite audio clip
        # Using the globally imported CompositeAudioClip
        composite_audio = CompositeAudioClip(all_audio_clips)

        # Set the composite audio to the background video
        video_with_audio = background_video_clip.set_audio(composite_audio)

        # Add the clip to our list
        clips = [video_with_audio]

    # Add a fade-out transition at the end of the video to prevent cutting off the last word
    # and create a smooth ending instead of a static frame
    if clips and not using_background_video:
        # Get the last image path
        last_image_path = storyboard_project['storyboards'][-1].get('image')

        # Check if the last image path is valid
        if last_image_path is None or not os.path.exists(last_image_path):
            print("Warning: No valid image found for end pause. Creating a black frame.")
            # Create a black frame as a fallback
            try:
                # Get dimensions from config
                config = load_config()
                dimensions = config["video_resolutions"][video_quality][orientation]
                width = dimensions["width"]
                height = dimensions["height"]

                # Create a black frame
                black_frame = np.zeros((height, width, 3), dtype=np.uint8)
                black_image = Image.fromarray(black_frame)

                # Save to a temporary file
                temp_image_path = os.path.join(os.path.dirname(audio_dir), "temp_black_frame_end.png")
                black_image.save(temp_image_path)

                # Use the black frame
                last_image_path = temp_image_path
            except Exception as e:
                print(f"Error creating black frame for end pause: {str(e)}. Using a simple color clip.")
                # We'll handle this case below
                last_image_path = None

        pause_duration = end_pause_duration  # Use the configurable pause duration

        # Create a silent audio clip for the pause that will fade out
        # Using a lambda function that returns 0 for any time t to create silence
        # The parameter t is used by AudioClip internally even though we don't reference it
        silent_audio = AudioClip(lambda t: 0, duration=pause_duration)

        # Create the pause clip
        if last_image_path is not None and os.path.exists(last_image_path):
            # Create an image clip with the last image
            pause_image_clip = ImageClip(last_image_path).set_duration(pause_duration)
            # Combine image and silent audio
            pause_clip = pause_image_clip.set_audio(silent_audio)
        else:
            # Use a color clip as fallback
            pause_color_clip = ColorClip(size=(640, 1280), color=(0, 0, 0)).set_duration(pause_duration)
            # Combine color clip and silent audio
            pause_clip = pause_color_clip.set_audio(silent_audio)

        # Apply a fade-out effect to the pause clip
        # Use a longer fade duration for a smoother transition
        visual_fade_duration = min(1.5, pause_duration * 0.75)  # Use up to 75% of pause duration for fade, max 1.5 seconds

        # Apply visual fade-out
        pause_clip = pause_clip.fadeout(visual_fade_duration)

        # Ensure smooth transition from the last content clip to the pause clip
        # This prevents audio glitches at the transition point
        if len(clips) > 0:
            print("Ensuring smooth audio transition to end pause...")

            # Get the last clip to ensure smooth transition
            last_clip = clips[-1]

            # Apply a subtle fade-out to the last clip's audio
            # This eliminates any clicking or popping sounds at the transition
            if hasattr(last_clip, 'audio') and last_clip.audio is not None:
                # Calculate an appropriate fade duration based on the clip
                fade_duration = min(0.2, last_clip.duration * 0.1)
                print(f"Applying {fade_duration:.2f}s audio fade-out to last clip for smooth transition")

                # Apply the fade-out to the last clip's audio
                # This ensures a clean transition to the pause clip
                if not hasattr(last_clip, 'audio_fadeout_applied'):
                    try:
                        # Only apply if not already applied
                        last_clip = last_clip.audio_fadeout(fade_duration)
                        last_clip.audio_fadeout_applied = True  # Mark as processed
                        # Replace the last clip in the list with the faded version
                        clips[-1] = last_clip
                    except Exception as e:
                        print(f"Warning: Could not apply audio fade to last clip: {str(e)}")

        # Add the pause clip to the list of clips
        clips.append(pause_clip)

        print(f"Added a {pause_duration} second pause with {visual_fade_duration:.2f}s fade-out at the end of the video")

    # Concatenate all clips including the pause
    # Use method="compose" for reliable clip concatenation
    # This ensures proper audio handling between clips
    print("Concatenating clips with compose method for smooth audio transitions")

    # Use the compose method which is more reliable and doesn't cause errors
    final_clip = concatenate_videoclips(clips, method="compose")

    # Apply background music if provided
    if final_bg_music_path and os.path.exists(final_bg_music_path):
        print(f"Adding background music: {final_bg_music_path}")
        print(f"Background music volume: {bg_music_volume}")
        try:
            # Load the background music
            bg_music = AudioFileClip(final_bg_music_path)
            print(f"Loaded background music, duration: {bg_music.duration} seconds")

            # Loop the background music if it's shorter than the video
            if bg_music.duration < final_clip.duration:
                print(f"Looping background music to match video duration: {final_clip.duration} seconds")
                # Create a list of clips to concatenate
                bg_clips = []
                remaining_duration = final_clip.duration
                while remaining_duration > 0:
                    clip_duration = min(bg_music.duration, remaining_duration)
                    bg_clips.append(bg_music.subclip(0, clip_duration))
                    remaining_duration -= clip_duration
                # Use concatenate_audioclips for audio
                bg_music = concatenate_audioclips(bg_clips)

            # Trim to match video duration if it's longer
            if bg_music.duration > final_clip.duration:
                print(f"Trimming background music to match video duration")
                bg_music = bg_music.subclip(0, final_clip.duration)

            # Adjust volume based on user setting
            print(f"Adjusting background music volume to: {bg_music_volume}")
            bg_music = bg_music.volumex(float(bg_music_volume))

            # Apply fade-in and fade-out to the background music for smooth transitions
            # Calculate fade durations for optimal audio quality
            fade_in_duration = min(1.0, bg_music.duration * 0.05)  # 5% of music duration, max 1 second
            fade_out_duration = min(1.5, end_pause_duration * 0.75)  # Use up to 75% of pause duration, max 1.5 seconds

            # Apply the fade effects to the background music
            print(f"Adding {fade_in_duration:.2f}s fade-in and {fade_out_duration:.2f}s fade-out to background music")

            # Check if music is long enough for the specified fades
            if bg_music.duration > (fade_in_duration + fade_out_duration):
                # Apply both fade-in and fade-out
                bg_music = bg_music.audio_fadein(fade_in_duration).audio_fadeout(fade_out_duration)
            else:
                # Music is very short, use shorter fades
                short_fade = bg_music.duration / 4
                print(f"Background music too short for standard fades, applying {short_fade:.2f}s fades")
                bg_music = bg_music.audio_fadein(short_fade).audio_fadeout(short_fade)

            # Get the original audio from the final clip
            original_audio = final_clip.audio

            # Create a composite audio clip with the faded background music
            print("Creating composite audio clip with smooth transitions")
            # Using the globally imported CompositeAudioClip
            final_audio = CompositeAudioClip([original_audio, bg_music])

            # Set the new audio to the final clip
            final_clip = final_clip.set_audio(final_audio)

            print("Background music added successfully")
        except Exception as e:
            print(f"Warning: Error adding background music: {str(e)}")
            import traceback
            traceback.print_exc()

    # Get video dimensions based on orientation and quality
    try:
        config = load_config()

        # Get dimensions from config based on quality and orientation
        if video_quality in config["video_resolutions"] and orientation in config["video_resolutions"][video_quality]:
            dimensions = config["video_resolutions"][video_quality][orientation]
            width = dimensions["width"]
            height = dimensions["height"]
            print(f"Using video dimensions for {video_quality} {orientation}: {width}x{height}")

            # Resize the clip to the specified dimensions
            final_clip = final_clip.resize((width, height))
        else:
            print(f"Warning: Could not find dimensions for {video_quality} {orientation}, using default size")
    except Exception as e:
        print(f"Error setting video dimensions: {str(e)}")
        print("Using default video dimensions")

    # Apply a final fade-out effect to the entire video
    # This ensures the video ends with a smooth transition to black
    fade_duration = min(1.5, end_pause_duration * 0.75)  # Use up to 75% of pause duration for fade, max 1.5 seconds
    print(f"Applying final {fade_duration} second fade-out to entire video")

    # Apply the fade-out to the entire video
    # This will create a smooth transition to black at the end
    final_clip = final_clip.fadeout(fade_duration)

    print(f"Creating final video file with quality: {video_quality}, orientation: {orientation}...")
    final_clip.write_videofile(output_file, fps=24)
    print(f"Video created successfully: {output_file}")

    # Add subtitles with the validated font and custom styling
    subtitle_output = output_file.replace('.mp4', '_subtitle.mp4')
    print(f"Adding subtitles with font {font_name} (size: {font_size}, color: {font_color}, outline: {outline_color} {outline_size}px)...")

    # Ensure color values are properly formatted
    # Convert common hex colors to web color names
    if isinstance(font_color, str) and font_color.startswith('#'):
        if font_color.upper() == "#FFFFFF":
            font_color = "white"
        elif font_color.upper() == "#000000":
            font_color = "black"
        elif font_color.upper() == "#FFFF00":
            font_color = "yellow"
        elif font_color.upper() == "#FF0000":
            font_color = "red"
        elif font_color.upper() == "#00FF00":
            font_color = "green"
        elif font_color.upper() == "#0000FF":
            font_color = "blue"

    if isinstance(outline_color, str) and outline_color.startswith('#'):
        if outline_color.upper() == "#FFFFFF":
            outline_color = "white"
        elif outline_color.upper() == "#000000":
            outline_color = "black"
        elif outline_color.upper() == "#FFFF00":
            outline_color = "yellow"
        elif outline_color.upper() == "#FF0000":
            outline_color = "red"
        elif outline_color.upper() == "#00FF00":
            outline_color = "green"
        elif outline_color.upper() == "#0000FF":
            outline_color = "blue"

    # Validate font exists and get the full path if needed
    font_file_path = font_name
    font_exists = False

    # Check if the font name is a full path
    if os.path.exists(font_name):
        font_file_path = font_name
        font_exists = True
        print(f"Font file path: {font_file_path}")
        print(f"Font exists: {font_exists}")
    else:
        # Try to find the font in the font directory
        for ext in [".ttf", ".otf", ""]:
            potential_path = os.path.join(font_path, f"{font_name}{ext}")
            if os.path.exists(potential_path):
                font_file_path = potential_path
                font_exists = True
                print(f"Found font file for subtitles: {font_file_path}")
                break

    # Use the actual font file path, not just the name
    try:
        add_subtitles(
            output_file,
            subtitle_output,
            font_file_path,
            font_color,
            font_size,
            outline_color,
            outline_size,
            caption_position,
            caption_words,
            highlight_current_word,
            word_highlight_color,
            highlight_style,
            highlight_bg_color,
            highlight_bg_opacity
        )
        print(f"Captioned video created: {subtitle_output}")
    except Exception as e:
        print(f"Error adding captions: {str(e)}")
        # Try with simpler parameters
        try:
            print("Trying with simplified caption parameters...")
            # Try to find TitanOne font
            titan_font = "TitanOne"
            for ext in [".ttf", ".otf", ""]:
                potential_path = os.path.join(font_path, f"{titan_font}{ext}")
                if os.path.exists(potential_path):
                    titan_font = potential_path
                    print(f"Found TitanOne font at: {titan_font}")
                    break

            add_subtitles(
                output_file,
                subtitle_output,
                titan_font,
                "yellow",
                50,
                "black",
                3,
                "bottom",
                caption_words,
                True,
                "white",
                "text_color",  # Default to text color highlighting
                "#3700B3",
                0.7
            )
            print(f"Captioned video created with default settings: {subtitle_output}")
        except Exception as e2:
            print(f"Failed to add captions: {str(e2)}")
            # Try one more time with minimal parameters and no font specification
            try:
                print("Trying with minimal caption parameters...")
                add_subtitles(
                    output_file,
                    subtitle_output,
                    "Arial",
                    "white",
                    40,
                    "black",
                    2,
                    "bottom",
                    caption_words,
                    False,
                    "white",
                    "text_color",  # Default to text color highlighting
                    "#3700B3",
                    0.7
                )
                print(f"Captioned video created with minimal settings: {subtitle_output}")
            except Exception as e3:
                print(f"All caption attempts failed: {str(e3)}")
                # If all attempts fail, copy the original video
                try:
                    import shutil
                    print("All caption attempts failed. Copying original video as output.")
                    shutil.copy2(output_file, subtitle_output)
                    print(f"Original video copied to: {subtitle_output}")
                except Exception as copy_error:
                    print(f"Error copying original video: {str(copy_error)}")
                    print("WARNING: Unable to create captioned video.")
