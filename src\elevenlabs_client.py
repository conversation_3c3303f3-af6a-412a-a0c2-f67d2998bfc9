"""
ElevenLabs API client for text-to-speech functionality.
"""

import os
import json
import requests
import logging
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load configuration
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    config_path = os.path.join(parent_dir, "config.json")

    if os.path.exists(config_path):
        with open(config_path, "r") as f:
            config = json.load(f)
            elevenlabs_config = config.get("elevenlabs", {})
            DEFAULT_STABILITY = elevenlabs_config.get("stability", 0.5)
            DEFAULT_SIMILARITY = elevenlabs_config.get("similarity", 0.75)
            DEFAULT_STYLE = elevenlabs_config.get("style", 0.0)
            DEFAULT_SPEAKER_BOOST = elevenlabs_config.get("speaker_boost", True)
            DEFAULT_MODEL = elevenlabs_config.get("model", "eleven_multilingual_v2")
    else:
        raise FileNotFoundError(f"Config file not found: {config_path}")
except Exception as e:
    logger.warning(f"Error loading ElevenLabs config: {str(e)}. Using defaults.")
    DEFAULT_STABILITY = 0.5
    DEFAULT_SIMILARITY = 0.75
    DEFAULT_STYLE = 0.0
    DEFAULT_SPEAKER_BOOST = True
    DEFAULT_MODEL = "eleven_multilingual_v2"

class ElevenLabsClient:
    """Client for interacting with the ElevenLabs API"""

    def _load_api_key(self) -> Optional[str]:
        """Load API key from multiple potential sources"""
        # Try environment variable first
        api_key = os.getenv("ELEVENLABS_API_KEY")
        
        if not api_key:
            # Try loading from .env files
            dotenv_paths = [
                os.path.join(os.path.dirname(script_dir), ".env"),  # Root directory
                os.path.join(script_dir, ".env"),  # src directory
            ]
            
            for env_path in dotenv_paths:
                if os.path.exists(env_path):
                    try:
                        with open(env_path, 'r') as f:
                            for line in f:
                                if line.strip().startswith('ELEVENLABS_API_KEY='):
                                    api_key = line.strip().split('=', 1)[1].strip('"').strip("'")
                                    os.environ["ELEVENLABS_API_KEY"] = api_key
                                    return api_key
                    except Exception as e:
                        logger.warning(f"Error reading .env file {env_path}: {e}")
                        continue
        
        return api_key

    def __init__(self):
        """Initialize the ElevenLabs client"""
        self.api_key = self._load_api_key()
        self.base_url = "https://api.elevenlabs.io/v1"
        self.is_available = False
        self.client = None
        self.initialization_error = None
        self.voices_cache = None

        # Add debug logging for API key detection
        if self.api_key:
            masked_key = f"{self.api_key[:4]}...{self.api_key[-4:]}" if len(self.api_key) > 8 else "***"
            logger.info(f"ElevenLabs API key found: {masked_key}")
            # Check if API key is valid using a direct API call
            self.is_available = self._check_api_key_direct()
        else:
            logger.warning("ElevenLabs API key not found in environment variables or .env files")
            self.initialization_error = "API key not found"

        if self.is_available:
            try:
                # Try to import and initialize the official client
                try:
                    from elevenlabs.client import ElevenLabs
                    self.client = ElevenLabs(api_key=self.api_key)
                    logger.info("ElevenLabs client initialized successfully")
                except ImportError:
                    logger.warning("ElevenLabs Python package not installed, using direct API calls")
                    self.initialization_error = "Using direct API calls (package not installed)"
                    # We'll still set is_available to True since direct API works
            except Exception as e:
                logger.error(f"Error initializing ElevenLabs client: {str(e)}")
                self.initialization_error = f"Error initializing client: {str(e)}"
                # Keep is_available True if direct API works
        else:
            if not self.initialization_error:
                self.initialization_error = "API key validation failed"
            logger.warning(f"ElevenLabs TTS will not be available. Reason: {self.initialization_error}")

    def _check_api_key_direct(self) -> bool:
        """Check if the API key is valid by making a direct API call"""
        if not self.api_key:
            self.initialization_error = "No API key provided"
            return False

        try:
            logger.info("Checking ElevenLabs API key with direct API call...")
            
            # First validate the API key format
            if not isinstance(self.api_key, str) or len(self.api_key) < 32:
                self.initialization_error = "Invalid API key format"
                logger.error("ElevenLabs API key appears to be malformed")
                return False
                
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            # Try to get user information first (lighter request)
            user_response = requests.get(
                f"{self.base_url}/user/subscription",
                headers=headers,
                timeout=10
            )

            if user_response.status_code == 401:
                self.initialization_error = "Invalid API key (authentication failed)"
                logger.error("ElevenLabs API key is invalid")
                return False
            elif user_response.status_code == 429:
                self.initialization_error = "Rate limit exceeded"
                logger.error("ElevenLabs API rate limit exceeded")
                return False
            elif user_response.status_code != 200:
                self.initialization_error = f"API error: {user_response.status_code}"
                logger.error(f"ElevenLabs API error: {user_response.status_code} - {user_response.text}")
                return False

            # Now check voices endpoint
            voices_response = requests.get(
                f"{self.base_url}/voices",
                headers=headers,
                timeout=10
            )

            if voices_response.status_code == 200:
                try:
                    voices = voices_response.json()
                    # Consider the key valid even if no voices are found
                    # The API can return either a list directly or have a 'voices' key
                    if isinstance(voices, (list, dict)):
                        # Cache the voices if they're in a valid format
                        voice_list = voices if isinstance(voices, list) else voices.get('voices', [])
                        if isinstance(voice_list, list):
                            self.voices_cache = voice_list
                        logger.info("ElevenLabs API key is valid (direct check)")
                        return True
                except json.JSONDecodeError:
                    self.initialization_error = "Invalid API response (JSON error)"
                    logger.error("Error decoding voices response")
                    return False
            else:
                self.initialization_error = f"Voices API error: {voices_response.status_code}"
                logger.error(f"Error getting voices: {voices_response.status_code} - {voices_response.text}")
                return False

        except requests.exceptions.Timeout:
            self.initialization_error = "API request timed out"
            logger.error("ElevenLabs API request timed out")
            return False
        except requests.exceptions.ConnectionError:
            self.initialization_error = "Connection error (check internet)"
            logger.error("Connection error while checking ElevenLabs API")
            return False
        except Exception as e:
            self.initialization_error = f"Unexpected error: {str(e)}"
            logger.error(f"Error checking ElevenLabs API key: {str(e)}")
            return False

    def get_voices(self) -> List[Dict[str, Any]]:
        """Get all available voices from ElevenLabs"""
        if not self.is_available:
            logger.warning(f"Cannot get voices - ElevenLabs client not available ({self.initialization_error})")
            return []

        try:
            # First check the cache
            if self.voices_cache:
                return self.voices_cache

            # If using the official client
            if self.client:
                try:
                    voices = self.client.voices.get_all()
                    if isinstance(voices, list):
                        self.voices_cache = voices
                        return voices
                    else:
                        logger.warning("Unexpected response from ElevenLabs client, falling back to direct API")
                except Exception as e:
                    logger.warning(f"Error with official client, falling back to direct API: {e}")
            
            # Direct API call
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            
            try:
                response = requests.get(
                    f"{self.base_url}/voices",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    # The API returns either a list directly or has a 'voices' key
                    voices = data if isinstance(data, list) else data.get('voices', [])
                    
                    # Make sure each voice has required fields
                    valid_voices = []
                    for voice in voices:
                        if isinstance(voice, dict) and all(k in voice for k in ['name', 'voice_id']):
                            # Ensure category exists, default to 'unknown' if not present
                            if 'category' not in voice:
                                voice['category'] = 'unknown'
                            valid_voices.append(voice)
                    
                    self.voices_cache = valid_voices
                    return valid_voices
                else:
                    logger.error(f"Error getting voices: {response.status_code} - {response.text}")
                    return []
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Network error getting voices: {str(e)}")
                return []
                
        except Exception as e:
            logger.error(f"Unexpected error getting voices: {str(e)}")
            return []

    def generate_audio(
        self,
        text: str,
        voice_id: str,
        output_file: str,
        stability: float = DEFAULT_STABILITY,
        similarity: float = DEFAULT_SIMILARITY,
        style: float = DEFAULT_STYLE,
        speaker_boost: bool = DEFAULT_SPEAKER_BOOST,
        model: str = DEFAULT_MODEL
    ) -> bool:
        """
        Generate audio from text using ElevenLabs API

        Args:
            text: Text to convert to speech
            voice_id: ID of the voice to use
            output_file: Path to save the audio file
            stability: Voice stability (0.0 to 1.0)
            similarity: Voice similarity (0.0 to 1.0)
            style: Speaking style (0.0 to 1.0)
            speaker_boost: Whether to enhance speaker clarity
            model: Model ID to use for generation

        Returns:
            True if successful, False otherwise
        """
        if not self.is_available:
            error_msg = self.initialization_error or "API key not set or invalid"
            logger.warning(f"ElevenLabs client not available ({error_msg}). Cannot generate audio.")
            return False

        # Try using the client library first if available
        if self.client:
            try:
                logger.info(f"Generating audio using client library with voice ID: {voice_id}")
                # Set voice settings
                voice_settings = {
                    "stability": stability,
                    "similarity_boost": similarity,
                    "style": style,
                    "use_speaker_boost": speaker_boost
                }

                # Generate audio
                audio = self.client.text_to_speech.convert(
                    text=text,
                    voice_id=voice_id,
                    model_id=model,
                    voice_settings=voice_settings,
                    output_format="mp3_44100_128"
                )

                # Handle both generator objects and bytes
                if hasattr(audio, '__iter__') and not isinstance(audio, (bytes, bytearray)):
                    logger.info("ElevenLabs returned a generator object, converting to bytes")
                    # Convert generator to bytes
                    audio_bytes = b''
                    for chunk in audio:
                        audio_bytes += chunk
                    audio = audio_bytes

                # Save the audio to file
                with open(output_file, "wb") as f:
                    f.write(audio)

                logger.info(f"ElevenLabs audio saved to {output_file}")
                return True
            except Exception as e:
                logger.error(f"Error generating ElevenLabs audio using client library: {str(e)}")
                logger.info("Falling back to direct API call...")
                # Fall back to direct API call

        # Use direct API call as fallback or primary method if client is not available
        try:
            logger.info(f"Generating audio using direct API call with voice ID: {voice_id}")

            # Set voice settings
            voice_settings = {
                "stability": stability,
                "similarity_boost": similarity,
                "style": style,
                "use_speaker_boost": speaker_boost
            }

            # Prepare request
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json",
                "Accept": "audio/mpeg"
            }

            data = {
                "text": text,
                "model_id": model,
                "voice_settings": voice_settings
            }

            # Make the API request
            response = requests.post(
                f"{self.base_url}/text-to-speech/{voice_id}",
                json=data,
                headers=headers
            )

            if response.status_code != 200:
                logger.error(f"Error generating ElevenLabs audio: {response.status_code} - {response.text}")
                return False

            # Save the audio to file
            with open(output_file, "wb") as f:
                f.write(response.content)

            logger.info(f"ElevenLabs audio saved to {output_file} using direct API call")
            return True

        except Exception as e:
            logger.error(f"Error generating ElevenLabs audio with direct API call: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def generate_preview(self, voice_id: str, output_file: str, preview_text: str = "Hello, this is a preview of my voice.") -> bool:
        """
        Generate a preview audio file for a voice

        Args:
            voice_id: ID of the voice to preview
            output_file: Path to save the preview audio
            preview_text: Text to use for the preview

        Returns:
            True if successful, False otherwise
        """
        return self.generate_audio(
            text=preview_text,
            voice_id=voice_id,
            output_file=output_file
        )

# Initialize the client
elevenlabs_client = ElevenLabsClient()
