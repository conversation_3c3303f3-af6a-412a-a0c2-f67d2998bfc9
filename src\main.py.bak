import os
import re
import json
import time
from dotenv import load_dotenv
from openai import OpenAI
from utils import pick_voice_name
from story_generator import (
    generate_story_and_title,
    generate_general_storyboard,
    generate_life_pro_tips_storyboard,
    generate_characters,
    generate_philosophy_storyboard,
    generate_fun_facts_storyboard,
)
from script_generator import generate_script
# Import the advanced image generator
from advanced_image_generator import generate_and_download_images, generate_image_for_custom_script
# Import the enhanced image prompt generator integration
from integration_enhanced_prompt import generate_and_download_enhanced_images, generate_enhanced_image_for_custom_script, generate_enhanced_image_for_custom_title
from video_creator import create_video as create_video_with_subtitles  # create_video supports video quality and orientation
from utils import (
    create_resource_dir,
    pick_story_type,
    pick_image_style,
    load_config,
)
from api import replicate_flux_api, fal_flux_api, together_flux_api
from video_creator import add_subtitles
from custom_captioner import ANIMATION_STYLES
import tkinter as tk
from tkinter import ttk, messagebox, font, colorchooser, filedialog
import threading
from ttkthemes import ThemedTk
import datetime
import webbrowser
import sys
import platform
import subprocess
import hashlib
from auth import show_login_dialog  # Import the login dialog
import ai_client  # Import our AI client manager
import voice_preview  # Import the voice preview module
from voice_selection_widget import VoiceSelectionWidget  # Import the voice selection widget

# Get the directory of the current script
script_dir = os.path.dirname(os.path.abspath(__file__))
# Construct the path to the .env file
dotenv_path = os.path.join(os.path.dirname(script_dir), ".env")
# Load the .env file
load_dotenv(dotenv_path)

# Define font directory path
font_dir = os.path.join(os.path.dirname(script_dir), "font")

def get_available_fonts():
    """Get a list of available fonts from the font directory"""
    fonts = []
    # Create font directory if it doesn't exist
    os.makedirs(font_dir, exist_ok=True)

    # Scan for font files in the font directory
    if os.path.exists(font_dir):
        for file in os.listdir(font_dir):
            if file.lower().endswith(('.ttf', '.otf')):
                # Use the filename without extension as the display name
                font_name = os.path.splitext(file)[0]
                fonts.append(font_name)

    # Sort fonts alphabetically
    fonts.sort()
    print(f"Found {len(fonts)} fonts in the font directory")
    return fonts

config = load_config()
# Initialize both OpenAI and Groq clients
ai_client.initialize_clients()

# Use OpenAI client by default
client = ai_client.get_current_client()

# Make sure all API keys are loaded from environment variables
REPLICATE_API_KEY = os.getenv("REPLICATE_API_KEY", "")
FAL_API_KEY = os.getenv("FAL_API_KEY", "")
TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY", "")

# Configuration
APP_NAME = "Azanx AI Autoshorts"
APP_VERSION = "7.0"
DEFAULT_FONT = "Segoe UI" if platform.system() == "Windows" else "Helvetica"

# Add supported AI providers
AI_PROVIDERS = ["OpenAI", "Groq"]

# Add supported TTS providers
TTS_PROVIDERS = ["OpenAI", "Voicely", "ElevenLabs"]

def switch_ai_provider(provider_name):
    """Switch between AI providers (OpenAI or Groq)"""
    global client

    try:
        # Convert UI names to lowercase for the client library
        provider_key = provider_name.lower()

        # Set the provider in the AI client manager
        ai_client.set_provider(provider_key)

        # Update the global client reference
        client = ai_client.get_current_client()

        return True, f"Switched to {provider_name} AI provider"
    except Exception as e:
        return False, f"Failed to switch AI provider: {str(e)}"

def generate_story(client, story_type, custom_title=None):
    # Generate story and title
    title, description, story = generate_story_and_title(client, story_type, custom_title)
    if story is None or title is None:
        raise Exception("Failed to generate a story and title. Please try again later.")

    story_dir = create_resource_dir(script_dir, story_type, title)

    # Import the fixed storyboard generator
    from fixed_story_generator import generate_storyboard

    # Generate storyboard using our fixed generator
    storyboard_project = generate_storyboard(client, story, title, story_type)

    # Initialize empty characters list
    characters = []

    if len(storyboard_project.get("storyboards")) == 0:
        raise Exception("Failed to generate storyboard. Please try again later.")

    # Remove scenes with empty subtitles or None subtitles
    storyboard_project["storyboards"] = [
        scene for scene in storyboard_project["storyboards"]
        if scene.get("subtitles") is not None and scene["subtitles"].strip()
    ]

    # Add transitions to each scene - alternating between zoom-in and zoom-out
    # The dissolve effect will be applied to all transitions in video_creator.py
    transition_types = ['zoom-in', 'zoom-out']
    for i, scene in enumerate(storyboard_project["storyboards"]):
        scene["transition_type"] = transition_types[i % len(transition_types)]

    storyboard_project["characters"] = characters
    storyboard_project["story_dir"] = story_dir  # Add story_dir to the project
    storyboard_project["title"] = title  # Add title to the project

    # Save the story
    with open(os.path.join(script_dir, story_dir, "story_english.txt"), "w", encoding="utf-8") as f:
        f.write(f"{title}\n\n{description}\n\n{story}")

    return storyboard_project


def generate_storyboard(client, story, image_generator_func=replicate_flux_api, video_quality="720p", orientation="portrait", check_pause_func=None, check_stop_func=None):
    # Check if we should use the RapidClips image generation approach
    use_rapidclips_approach = True

    if use_rapidclips_approach:
        # Import the RapidClips integration module
        from rapidclips_integration import generate_images_for_story

        # Determine which image service to use based on the image_generator_func
        image_service = "replicate"
        if image_generator_func.__name__ == "together_flux_api":
            image_service = "together"
        elif image_generator_func.__name__ == "fal_flux_api":
            # FAL AI is now supported in RapidClips
            image_service = "fal"

        print(f"Using {image_service} image service with RapidClips approach")
        print(f"Video quality: {video_quality}, Orientation: {orientation}")

        # Generate images using the RapidClips approach with the selected image style
        try:
            # Check if we should stop before starting image generation
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return story

            image_files = generate_images_for_story(
                story,
                image_service_name=image_service,
                orientation=orientation,
                video_quality=video_quality,
                image_style=story.get("image_style", "Cinematic"),  # Pass the selected image style
                check_pause_func=check_pause_func,
                check_stop_func=check_stop_func
            )
        except Exception as e:
            print(f"Error with RapidClips approach: {e}")
            print("Falling back to enhanced image prompt generator")

            # Check if we should stop before trying the fallback
            if check_stop_func and check_stop_func():
                print("Image generation stopped by user")
                return story

            # Fallback to enhanced image prompt generator if RapidClips fails
            image_files = generate_and_download_enhanced_images(
                story,
                story["story_dir"],
                story.get("image_style", "Cinematic"),
                story_type=story.get('story_type', story.get('type', 'general')),
                image_generator_func=image_generator_func,
                video_quality=video_quality,
                orientation=orientation,
                check_pause_func=check_pause_func,
                check_stop_func=check_stop_func
            )
    else:
        # Check if we should stop before starting image generation
        if check_stop_func and check_stop_func():
            print("Image generation stopped by user")
            return story

        # Use the original enhanced image prompt generator as fallback
        image_files = generate_and_download_enhanced_images(
            story,
            story["story_dir"],
            story.get("image_style", "Cinematic"),
            story_type=story.get('story_type', story.get('type', 'general')),  # Pass story type for better context
            image_generator_func=image_generator_func,
            video_quality=video_quality,
            orientation=orientation
        )

    # Update storyboard_project with image and audio paths
    for i, storyboard in enumerate(story['storyboards']):
        storyboard['image'] = image_files[i] if i < len(image_files) else None

        # Create a safe scene number for the audio file path
        # Remove any special characters and spaces from scene_number
        scene_num = storyboard['scene_number']
        if isinstance(scene_num, str):
            safe_scene_num = re.sub(r'[^\w]', '_', scene_num)
        else:
            safe_scene_num = str(scene_num)

        # Create audio directory if it doesn't exist
        audio_dir = os.path.join(story["story_dir"], "audio")
        os.makedirs(audio_dir, exist_ok=True)

        # Set the audio path with the safe scene number
        storyboard['audio'] = os.path.join(audio_dir, f"scene_{safe_scene_num}.mp3")

    # Save the storyboard_project to a json file
    with open(
        os.path.join(script_dir, story["story_dir"], "storyboard_project.json"),
        "w",
        encoding="utf-8",
    ) as f:
        json.dump(story, f, ensure_ascii=False, indent=4)

    return story


def process_video(client, storyboard_project, video_path, audio_dir, voice_name, font_name="TitanOne", tts_model="OpenAI", font_color="#FFFFFF", font_size=40, outline_color="#000000", outline_size=3, caption_position="bottom", caption_words=3, highlight_words=True, highlight_color="yellow", bg_music_path=None, bg_music_volume=0.2, end_pause_duration=2.5, orientation="portrait", video_quality="720p", highlight_style="text_color", highlight_bg_color="#3700B3", highlight_bg_opacity=0.7):
    if not storyboard_project.get("storyboards"):
        raise Exception("No storyboard data available")

    os.makedirs(audio_dir, exist_ok=True)
    create_video_with_subtitles(client, storyboard_project, video_path, audio_dir, voice_name, font_name, tts_model, font_color, font_size, outline_color, outline_size, caption_position, caption_words, highlight_words, highlight_color, bg_music_path, bg_music_volume, end_pause_duration, orientation, video_quality, highlight_style, highlight_bg_color, highlight_bg_opacity)


def process_custom_title(title, story_type="motivational", image_style="vibrant"):
    """
    Process a custom title to create a story structure for video generation

    Args:
        title: The user-provided title
        story_type: Type of story (for metadata purposes)
        image_style: Image style to apply

    Returns:
        Story structure with the custom title
    """
    # Create a safe directory name from the title
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().replace(' ', '_')
    story_dir = create_resource_dir(script_dir, story_type, safe_title)

    # Create an enhanced image prompt for the title
    try:
        from enhanced_image_prompt_generator import enhance_image_prompt
        image_prompt = enhance_image_prompt(
            text=title,
            story_type=story_type,
            image_style=image_style,
            is_custom_title=True
        )
        print(f"Using enhanced image prompt for title: {image_prompt[:100]}...")
    except Exception as e:
        print(f"Error using enhanced image prompt generator: {e}")
        # Fallback to a simple image prompt
        image_prompt = f"Cinematic scene representing '{title}'. Detailed environment without people or faces. Professional lighting, high-quality production."

    # Create a simple story structure with just the title
    story = {
        "title": title,
        "description": f"Custom title: {title}",
        "story_type": story_type,
        "image_style": image_style,
        "story_dir": story_dir,
        "storyboards": [
            {
                "scene_number": 1,
                "subtitles": title,
                "description": image_prompt,
                "scene_description": title,
                "image_prompt": image_prompt,
                "transition_type": "fade"  # Default transition
            }
        ],
        "characters": []  # Add empty characters list as it's expected
    }

    return story

def process_custom_script(script_content, story_type="motivational", image_style="vibrant", story_title=None):
    """
    Process a custom script to create a story structure for video generation

    Args:
        script_content: The user-provided script content
        story_type: Type of story (for metadata purposes)
        image_style: Image style to apply
        story_title: Optional custom title

    Returns:
        A story dictionary with the same structure as generated by the AI
    """
    # Generate a title if not provided
    if not story_title:
        title_prompt = f"Generate a short, catchy title (max 6 words) for this content, without quotes: {script_content[:500]}..."
        try:
            # Use ai_client.chat_completion to ensure proper model mapping between OpenAI and Groq
            title_response = ai_client.chat_completion(
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that generates short, catchy titles."},
                    {"role": "user", "content": title_prompt}
                ],
                model="gpt-3.5-turbo",  # This will be mapped to the appropriate model for the current provider
                max_tokens=20,
                temperature=0.7
            )
            story_title = title_response.choices[0].message.content.strip('"\'')
        except Exception as e:
            # Fallback to a simple title if there's an error
            print(f"Error generating title: {str(e)}")
            print("Using default title")
            story_title = f"Custom Script {datetime.datetime.now().strftime('%Y-%m-%d')}"

    # Create safe filename from title
    safe_title = "".join([c if c.isalnum() else "_" for c in story_title]).lower()
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    story_id = f"{safe_title}_{timestamp}"

    # Create directory for this story
    data_dir = os.path.join(script_dir, "data")
    story_dir = os.path.join(data_dir, story_id)
    os.makedirs(story_dir, exist_ok=True)

    # Save the script content to a text file
    script_file = os.path.join(story_dir, "story.txt")
    with open(script_file, "w", encoding="utf-8") as f:
        f.write(script_content)

    # Generate description for the story
    description_prompt = f"Write a brief description (3-4 sentences) summarizing this content: {script_content[:1000]}..."
    description_response = ai_client.chat_completion(
        messages=[
            {"role": "system", "content": "You are a helpful assistant that writes concise descriptions."},
            {"role": "user", "content": description_prompt}
        ],
        model="gpt-3.5-turbo",
        max_tokens=100,
        temperature=0.7
    )
    story_description = description_response.choices[0].message.content.strip()

    # Create the story structure
    story = {
        "title": story_title,
        "type": story_type,  # Keep for backward compatibility
        "story_type": story_type,  # Add standardized field name
        "id": story_id,
        "story_dir": story_dir,
        "image_style": image_style,
        "content": script_content,
        "description": story_description
    }

    # Split by paragraphs (empty lines)
    paragraphs = [p.strip() for p in re.split(r'\n\s*\n', script_content) if p.strip()]

    # If there's only one paragraph but it's long, split it into multiple scenes (around 5)
    # This is to create a more visually dynamic video without generating any new text content
    if len(paragraphs) == 1 and len(paragraphs[0]) > 250:
        # Split at logical points like sentences
        sentences = re.split(r'(?<=[.!?])\s+', paragraphs[0])

        # If we have enough sentences, split into multiple scenes
        if len(sentences) >= 3:
            # Aim for about 2-3 sentences per scene, but no more than 5 scenes total
            sentences_per_scene = max(2, min(3, len(sentences) // 4))
            new_paragraphs = []

            # Create scenes by grouping sentences
            for i in range(0, len(sentences), sentences_per_scene):
                scene_text = ' '.join(sentences[i:i+sentences_per_scene])
                if scene_text:
                    new_paragraphs.append(scene_text)

            # Limit to 5 scenes maximum
            paragraphs = new_paragraphs[:5]

    # Create scenes/storyboards
    storyboards = []

    # Transition types for visual variety (zoom-in, zoom-out)
    # The dissolve effect will be applied to all transitions in video_creator.py
    transition_types = ['zoom-in', 'zoom-out']

    # Import the PromptService for RapidClips-style image prompt generation
    from services.prompt_service import PromptService
    prompt_service = PromptService()

    # Create full subtitles text for context (used by RapidClips approach)
    full_subtitles = "\n".join(paragraphs)

    # Keep track of previous prompts for coherence
    previous_prompts = []

    for i, paragraph in enumerate(paragraphs):
        # Generate a relevant image prompt using the RapidClips approach
        try:
            # Use the RapidClips prompt service to generate an image prompt with the selected style
            image_prompt = prompt_service.generate_image_prompt(
                full_subtitles=full_subtitles,
                previous_prompts=previous_prompts,
                group_text=paragraph,
                image_style=image_style  # Pass the selected image style
            )

            # Add to previous prompts for coherence in future prompts
            previous_prompts.append(image_prompt)

            print(f"Using RapidClips image prompt for scene {i+1}: {image_prompt[:100]}...")
        except Exception as e:
            print(f"Error using RapidClips prompt service: {e}")

            # Fallback to the original approach if RapidClips fails
            prompt = f"""Create a detailed, descriptive image prompt (40-60 words) for: "{paragraph}"

The image should be in {image_style} style - a visually appealing scene that represents the text.

Your description MUST include ALL of the following elements:
1. Setting/environment (specific location, time of day)
2. Lighting details (quality, direction, color)
3. Color palette and mood
4. Camera angle and framing
5. Key visual elements and focal points
6. Composition details

IMPORTANT: The image MUST be in {image_style} style. Start your prompt with "{image_style} style:" to ensure the style is applied.
DO NOT use quotes. DO NOT mention text or captions. ONLY describe the visual scene.
Be extremely specific and detailed about visual elements.

IMPORTANT: The generated image MUST NOT contain any text, words, writing, captions, or labels of any kind.
Subtitles will be added separately in the video processing stage."""

            # Use ai_client.chat_completion to ensure proper model mapping between OpenAI and Groq
            image_prompt_response = ai_client.chat_completion(
                messages=[
                    {"role": "system", "content": f"You are an expert visual director who creates extremely detailed, vivid image prompts for text in the {image_style} visual style. You excel at translating text into rich visual scenes with specific details about setting, lighting, color palette, camera angles, composition, and mood. Your descriptions are highly specific and focus on visual elements that will make the generated image closely match the text content. IMPORTANT: You MUST create prompts in the {image_style} style and start your prompt with '{image_style} style:'. You NEVER include instructions for text, words, captions, or labels in your image prompts, as subtitles will be added separately in the video processing stage."},
                    {"role": "user", "content": prompt}
                ],
                model="gpt-3.5-turbo",  # This will be mapped to the appropriate model for the current provider
                max_tokens=150,
                temperature=0.7
            )

            # Get the image prompt and clean it up
            image_prompt = image_prompt_response.choices[0].message.content.strip('"')

            # Try the enhanced image prompt generator as a second fallback
            try:
                # Import here to avoid circular imports
                from enhanced_image_prompt_generator import enhance_image_prompt

                # Generate a more relevant image prompt using our enhanced generator
                enhanced_prompt = enhance_image_prompt(
                    text=paragraph,
                    story_type=story_type,
                    image_style=image_style,
                    is_custom_script=True
                )

                # Use the enhanced prompt if available
                if enhanced_prompt:
                    image_prompt = enhanced_prompt
                    print(f"Using enhanced image prompt for scene: {image_prompt[:100]}...")
            except Exception as e2:
                print(f"Error using enhanced image prompt generator: {e2}")
                # Continue with the original prompt if there's an error
                pass

        # Alternate between transition types
        transition_type = transition_types[i % len(transition_types)]

        # Create scene with the structure expected by the generate_storyboard function
        scene = {
            "scene_number": i + 1,
            "subtitles": paragraph,
            "description": image_prompt,  # This is the key field for image generation
            "scene_description": paragraph[:150] + "..." if len(paragraph) > 150 else paragraph,
            "image_prompt": image_prompt,  # Add redundant field for compatibility
            "transition_type": transition_type  # Add transition type for animation
        }
        storyboards.append(scene)

    # Add storyboards to the story structure
    story["storyboards"] = storyboards
    story["characters"] = []  # Add empty characters list as it's expected

    # Save the image prompts to a file for reference
    image_prompts_file = os.path.join(story_dir, "image_prompts.txt")
    with open(image_prompts_file, "w", encoding="utf-8") as f:
        for i, scene in enumerate(storyboards):
            scene_num = scene["scene_number"]
            prompt = scene.get("image_prompt", "")
            f.write(f"Scene {scene_num}: {prompt}\n\n")

    print(f"Image prompts saved to {image_prompts_file}")

    # Save the storyboard project
    storyboard_file = os.path.join(story_dir, "storyboard_project.json")
    with open(storyboard_file, "w", encoding="utf-8") as f:
        json.dump(story, f, indent=2, ensure_ascii=False)

    return story


class VideoGeneratorThread(threading.Thread):
    def __init__(self, story_type, image_style, tts_model, image_model, voice_name,
                 font_name, font_color, font_size, outline_color, outline_size,
                 caption_position, caption_words, callback, update_progress,
                 highlight_words=True, highlight_color="yellow",
                 custom_script=None, custom_title=None,
                 bg_music_path=None, bg_music_volume=0.2,
                 llm_provider="OpenAI", ai_custom_title=None, end_pause_duration=3.5,
                 orientation="portrait", video_quality="720p",
                 highlight_style="text_color", highlight_bg_color="#3700B3", highlight_bg_opacity=0.7):
        """Thread for video generation to avoid UI freezing"""
        threading.Thread.__init__(self)
        self.story_type = story_type
        self.image_style = image_style
        self.tts_model = tts_model
        self.image_model = image_model
        self.voice_name = voice_name
        self.font_name = font_name
        self.font_color = font_color
        self.font_size = font_size
        self.outline_color = outline_color
        self.outline_size = outline_size
        self.caption_position = caption_position
        self.caption_words = caption_words
        self.highlight_words = highlight_words
        self.highlight_color = highlight_color
        self.highlight_style = highlight_style  # Style of highlighting (text_color or background)
        self.highlight_bg_color = highlight_bg_color  # Background color for highlighted words
        self.highlight_bg_opacity = highlight_bg_opacity  # Opacity of the background highlight
        self.callback = callback
        self.update_progress = update_progress
        self.custom_script = custom_script
        self.custom_title = custom_title  # Title for custom script
        self.ai_custom_title = ai_custom_title  # Title for AI-generated script
        self.bg_music_path = bg_music_path
        self.bg_music_volume = bg_music_volume
        self.llm_provider = llm_provider
        self.end_pause_duration = end_pause_duration  # Duration of pause at the end of the video
        self.orientation = orientation  # Video orientation (portrait or landscape)
        self.video_quality = video_quality  # Video quality (720p, 1080p, 2K, 4K)
        self.result = None

        # Add pause and stop flags
        self.paused = False
        self.pause_lock = threading.Lock()  # Lock for thread-safe pause operations
        self.stop_requested = False



    def pause(self):
        """Pause the thread execution"""
        with self.pause_lock:
            self.paused = True
            self.callback("Video generation paused")

    def resume(self):
        """Resume the thread execution"""
        with self.pause_lock:
            self.paused = False
            self.callback("Video generation resumed")

    def stop(self):
        """Request the thread to stop execution"""
        self.stop_requested = True
        with self.pause_lock:
            self.paused = False  # Ensure we're not paused so we can process the stop request

        self.callback("Video generation stopping...")

    def _check_pause(self):
        """Check if thread is paused and wait if it is"""
        while True:
            with self.pause_lock:
                if not self.paused:
                    break

            # Check if stop was requested while paused
            if self.stop_requested:
                break

            # Sleep briefly to avoid busy waiting
            time.sleep(0.1)

    def track_process(self, process_name):
        """Track processes that might be launched during video creation"""
        # This is a placeholder method that doesn't actually track processes yet
        # In a future implementation, this could be used to track and manage external processes
        self.callback(f"Starting {process_name} processes...")
        # For now, just log the process name and continue
        pass

    def run(self):
        try:
            # Set the LLM provider
            self.callback(f"Using {self.llm_provider} as AI provider...")

            # Check if stop requested
            if self.stop_requested:
                self.result = {
                    "success": False,
                    "message": "Video generation stopped by user",
                    "video_path": None
                }
                return

            # Check if paused
            self._check_pause()

            success, message = switch_ai_provider(self.llm_provider)
            if not success:
                self.callback(f"Error switching AI provider: {message}. Using OpenAI as fallback.")
                switch_ai_provider("OpenAI")

            # Get the current client after switching provider
            current_client = ai_client.get_current_client()

            # Determine if we're using a custom script or AI-generated story
            if self.custom_script:
                # Process custom script
                self.callback("Processing custom script...")
                self.update_progress(10)

                # Check if paused or stopped
                self._check_pause()
                if self.stop_requested:
                    self.result = {
                        "success": False,
                        "message": "Video generation stopped by user",
                        "video_path": None
                    }
                    return

                # Use the custom title if provided, otherwise generate one
                custom_title = self.custom_title if self.custom_title and self.custom_title.strip() else None

                # Process the custom script
                story = process_custom_script(
                    self.custom_script,
                    story_type=self.story_type,
                    image_style=self.image_style,
                    story_title=custom_title
                )
                self.callback(f"Custom script processed with title: {story['title']}")
            else:
                # Generate AI story
                self.callback(f"Generating story using {self.llm_provider}...")
                self.update_progress(10)

                # Check if paused or stopped
                self._check_pause()
                if self.stop_requested:
                    self.result = {
                        "success": False,
                        "message": "Video generation stopped by user",
                        "video_path": None
                    }
                    return

                # Check if we have a custom title for the AI-generated script
                if self.ai_custom_title:
                    self.callback(f"Using custom title: {self.ai_custom_title}")

                # Generate the story with the custom title if provided
                story = generate_story(current_client, self.story_type, custom_title=self.ai_custom_title)
                story["image_style"] = self.image_style
                self.callback(f"Story generated with title: {story['title']}")

            # Select image generation API based on user choice
            if self.image_model == "Replicate Flux":
                image_generator_func = replicate_flux_api
            elif self.image_model == "FAL AI Flux":
                image_generator_func = fal_flux_api
            elif self.image_model == "Together AI Flux":
                image_generator_func = together_flux_api
            else:
                image_generator_func = replicate_flux_api  # Default

            # Generate storyboard
            self.callback(f"Creating storyboard with {len(story.get('storyboards', []))} scenes...")
            self.update_progress(20)

            # Check if paused or stopped
            self._check_pause()
            if self.stop_requested:
                self.result = {
                    "success": False,
                    "message": "Video generation stopped by user",
                    "video_path": None
                }
                return

            self.callback(f"Generating images with {self.image_model} using RapidClips approach...")
            self.update_progress(30)
            storyboard_project = generate_storyboard(
                current_client,
                story,
                image_generator_func,
                video_quality=self.video_quality,
                orientation=self.orientation,
                check_pause_func=self._check_pause,
                check_stop_func=lambda: self.stop_requested
            )

            # Check if paused or stopped
            self._check_pause()
            if self.stop_requested:
                self.result = {
                    "success": False,
                    "message": "Video generation stopped by user",
                    "video_path": None
                }
                return

            # Create video
            self.callback("Creating video...")
            self.update_progress(70)
            story_dir = storyboard_project["story_dir"]
            audio_dir = os.path.join(story_dir, "audio")
            video_path = os.path.join(story_dir, "story_video.mp4")

            # Start tracking FFmpeg processes that might be launched during video creation
            self.track_process("ffmpeg")

            # Log AI provider, TTS model, voice, and transcription information
            self.callback(f"AI Provider: {self.llm_provider}")

            # Log which provider is being used for transcription
            if self.llm_provider == "Groq":
                self.callback(f"Using Groq for transcription and subtitles")
            else:
                self.callback(f"Using OpenAI Whisper for transcription and subtitles")

            # Log TTS information
            if self.tts_model == "Voicely":
                self.callback(f"Using Voicely TTS with voice: {self.voice_name}")
            else:
                self.callback(f"Using OpenAI TTS with voice: {self.voice_name}")

            # Validate font exists
            font_path = os.path.join(os.path.dirname(script_dir), "font")
            selected_font = self.font_name

            # Check for both .ttf and .otf extensions
            font_found = False
            for ext in [".ttf", ".otf"]:
                font_file = os.path.join(font_path, f"{selected_font}{ext}")
                if os.path.exists(font_file):
                    font_found = True
                    self.callback(f"Using font: {selected_font}")
                    break

            # If font not found, fall back to TitanOne or another available font
            if not font_found:
                self.callback(f"Font {selected_font} not found, looking for alternatives...")

                # Try TitanOne first
                if os.path.exists(os.path.join(font_path, "TitanOne.ttf")):
                    selected_font = "TitanOne"
                    self.callback(f"Using TitanOne as fallback font")
                else:
                    # Find any available font
                    available_fonts = get_available_fonts()
                    if available_fonts:
                        selected_font = available_fonts[0]
                        self.callback(f"Using {selected_font} as fallback font")
                    else:
                        self.callback(f"No fonts found in font directory. Video generation may fail.")

            # Log animation and highlighting settings
            self.callback(f"Subtitle settings: Font {selected_font}, Size {self.font_size}px, Color {self.font_color}")
            self.callback(f"Caption position: {self.caption_position}, Words per caption: {self.caption_words}")
            self.callback(f"Word highlighting: {self.highlight_words}")
            if self.highlight_words:
                self.callback(f"Word highlight color: {self.highlight_color}")

            try:
                # Process video with selected TTS model and voice
                self.callback("Generating audio and creating video...")
                # Log end pause duration and video settings
                self.callback(f"End pause duration: {self.end_pause_duration} seconds")
                self.callback(f"Video quality: {self.video_quality}, Orientation: {self.orientation}")

                # Track any new FFmpeg processes that might be launched during audio generation
                self.track_process("ffmpeg")

                # Check if paused or stopped
                self._check_pause()
                if self.stop_requested:
                    self.result = {
                        "success": False,
                        "message": "Video generation stopped by user",
                        "video_path": None
                    }
                    return

                process_video(current_client, storyboard_project, video_path, audio_dir,
                             self.voice_name, selected_font, self.tts_model,
                             self.font_color, self.font_size, self.outline_color, self.outline_size,
                             self.caption_position, self.caption_words,
                             self.highlight_words, self.highlight_color,
                             self.bg_music_path, self.bg_music_volume, self.end_pause_duration,
                             self.orientation, self.video_quality,
                             self.highlight_style, self.highlight_bg_color, self.highlight_bg_opacity)

            except Exception as e:
                error_msg = str(e)
                # Check for voice-related errors
                if "voice" in error_msg.lower() or "edge tts" in error_msg.lower():
                    # Instead of switching voices, try again with the same voice but different settings
                    self.callback(f"Error with voice: {self.voice_name}. Trying again with adjusted settings...")

                    # Try with the same voice but different font and other settings
                    # This avoids the issue of generating audio twice with different voices
                    process_video(current_client, storyboard_project, video_path, audio_dir,
                                self.voice_name, "TitanOne", self.tts_model,
                                "yellow", 50, "black", 3,
                                "bottom", self.caption_words, True, "yellow",
                                self.bg_music_path, self.bg_music_volume, self.end_pause_duration,
                                self.orientation, self.video_quality,
                                "text_color", "#3700B3", 0.7)
                # Check for font-related errors
                elif "font" in error_msg.lower() or "PIL" in error_msg.upper() or "stroke" in error_msg.lower() or "captioner" in error_msg.lower():
                    self.callback(f"Error with font styling. Trying with default font settings...")
                    # Fall back to safe font settings
                    process_video(current_client, storyboard_project, video_path, audio_dir,
                                self.voice_name, "TitanOne", self.tts_model,
                                "yellow", 50, "black", 3,
                                "bottom", self.caption_words, True, "yellow",
                                self.bg_music_path, self.bg_music_volume, self.end_pause_duration,
                                self.orientation, self.video_quality,
                                self.highlight_style, self.highlight_bg_color, self.highlight_bg_opacity)
                else:
                    # For other errors, try with simpler parameters overall
                    self.callback(f"Error: {str(e)}. Trying with default settings...")
                    process_video(current_client, storyboard_project, video_path, audio_dir,
                                "en-US-AriaNeural" if self.tts_model == "Voicely" else "alloy",
                                "TitanOne", self.tts_model, "yellow", 50, "black", 3,
                                "bottom", self.caption_words, True, "yellow",
                                self.bg_music_path, self.bg_music_volume, self.end_pause_duration,
                                self.orientation, self.video_quality,
                                "text_color", "#3700B3", 0.7)

            # Check for subtitle video path
            subtitle_video_path = video_path.replace('.mp4', '_subtitle.mp4')
            if os.path.exists(subtitle_video_path):
                self.callback("Video creation completed! Adding final touches...")
                self.update_progress(95)
                self.callback(f"Video saved at: {subtitle_video_path}")
                self.update_progress(100)
                self.result = {
                    "success": True,
                    "message": "Video generation completed!",
                    "video_path": subtitle_video_path
                }
            else:
                self.callback(f"Basic video created at: {video_path}")
                self.result = {
                    "success": True,
                    "message": "Video generation completed, but subtitled version could not be created.",
                    "video_path": video_path
                }

        except Exception as e:
            self.callback(f"Error during video generation: {str(e)}")
            self.result = {
                "success": False,
                "message": f"Error: {str(e)}",
                "video_path": None
            }
            import traceback
            traceback.print_exc()


class ModernUI:
    # Material Design color palette
    COLORS = {
        # Primary colors
        'primary': '#6200EE',      # Material Design primary
        'primary_hover': '#5000CA', # Darker primary
        'primary_dark': '#3700B3',  # Even darker primary
        'primary_light': '#BB86FC', # Light primary variant

        # Secondary colors
        'secondary': '#03DAC6',    # Material Design secondary
        'secondary_hover': '#00B3A6', # Darker secondary
        'secondary_dark': '#018786',  # Even darker secondary

        # Accent colors
        'accent': '#FF9800',       # Material Design orange
        'accent_hover': '#F57C00', # Darker orange
        'accent_dark': '#E65100',  # Even darker orange

        # Feedback colors
        'success': '#4CAF50',      # Material Design green
        'error': '#F44336',        # Material Design red
        'warning': '#FFC107',      # Material Design yellow
        'info': '#2196F3',         # Material Design blue

        # Background and surface colors
        'background': '#F5F5F5',   # Material Design background
        'surface': '#FFFFFF',      # Material Design surface
        'card': '#FFFFFF',         # Card backgrounds

        # Elevation colors (for shadows)
        'elevation_1': 'rgba(0, 0, 0, 0.05)',  # Light shadow
        'elevation_2': 'rgba(0, 0, 0, 0.07)',  # Medium shadow
        'elevation_3': 'rgba(0, 0, 0, 0.09)',  # Heavy shadow

        # Text colors
        'text': '#212121',         # Material Design primary text
        'text_secondary': '#757575', # Material Design secondary text
        'text_disabled': '#9E9E9E', # Material Design disabled text
        'text_hint': '#9E9E9E',    # Material Design hint text

        # Border and divider colors
        'border': '#E0E0E0',       # Light border
        'divider': '#EEEEEE',      # Divider color

        # Input and control colors
        'input_bg': '#F5F5F5',     # Light background for inputs
        'highlight': '#EDE7F6',    # Very light primary highlight

        # Navigation colors
        'nav_bg': '#3700B3',       # Dark primary for navigation
        'nav_text': '#FFFFFF',     # Light text for navigation
        'nav_highlight': '#BB86FC', # Light primary for navigation highlight

        # Gradient colors
        'gradient_start': '#6200EE', # Gradient start color
        'gradient_end': '#3700B3',   # Gradient end color
    }

    @staticmethod
    def apply_theme(root):
        """Apply Material Design theme to the application"""
        style = ttk.Style()

        # Configure Tk elements
        root.configure(bg=ModernUI.COLORS['background'])
        root.option_add('*Font', (DEFAULT_FONT, 12))  # Material Design uses slightly larger fonts

        # Configure TTK elements
        style.configure('TFrame', background=ModernUI.COLORS['background'])
        style.configure('Card.TFrame', background=ModernUI.COLORS['surface'])

        # Material Design Typography System
        # - Display: Large, prominent text (headers)
        # - Headline: Important text that's smaller than Display
        # - Title: Section headers
        # - Body: Regular text
        # - Caption: Small text for annotations

        # Label styles with Material Design typography
        style.configure('TLabel',
                      background=ModernUI.COLORS['background'],
                      foreground=ModernUI.COLORS['text'],
                      font=(DEFAULT_FONT, 14))  # Body text

        style.configure('Card.TLabel',
                      background=ModernUI.COLORS['surface'],
                      foreground=ModernUI.COLORS['text'],
                      font=(DEFAULT_FONT, 14))

        style.configure('Display.TLabel',
                      font=(DEFAULT_FONT, 34, 'bold'),
                      foreground=ModernUI.COLORS['text'],
                      background=ModernUI.COLORS['background'])

        style.configure('Headline.TLabel',
                      font=(DEFAULT_FONT, 24, 'bold'),
                      foreground=ModernUI.COLORS['primary'],
                      background=ModernUI.COLORS['background'])

        style.configure('Title.TLabel',
                      font=(DEFAULT_FONT, 20, 'bold'),
                      foreground=ModernUI.COLORS['text'],
                      background=ModernUI.COLORS['background'])

        style.configure('Subtitle.TLabel',
                      font=(DEFAULT_FONT, 16),
                      foreground=ModernUI.COLORS['text_secondary'],
                      background=ModernUI.COLORS['background'])

        style.configure('Body.TLabel',
                      font=(DEFAULT_FONT, 14),
                      foreground=ModernUI.COLORS['text'],
                      background=ModernUI.COLORS['background'])

        style.configure('Caption.TLabel',
                      font=(DEFAULT_FONT, 12),
                      foreground=ModernUI.COLORS['text_secondary'],
                      background=ModernUI.COLORS['background'])

        # Keep compatibility with existing label styles
        style.configure('Header.TLabel',
                      font=(DEFAULT_FONT, 18, 'bold'),
                      foreground=ModernUI.COLORS['primary'],
                      background=ModernUI.COLORS['background'])

        style.configure('Section.TLabel',
                      font=(DEFAULT_FONT, 16, 'bold'),
                      foreground=ModernUI.COLORS['text'],
                      background=ModernUI.COLORS['background'])

        style.configure('FormField.TLabel',
                      font=(DEFAULT_FONT, 14),
                      foreground=ModernUI.COLORS['text'],
                      background=ModernUI.COLORS['background'])

        style.configure('FormHelp.TLabel',
                      font=(DEFAULT_FONT, 12),
                      foreground=ModernUI.COLORS['text_secondary'],
                      background=ModernUI.COLORS['background'])

        style.configure('Nav.TLabel',
                      font=(DEFAULT_FONT, 14),
                      foreground=ModernUI.COLORS['nav_text'],
                      background=ModernUI.COLORS['nav_bg'])

        style.configure('Status.TLabel',
                      font=(DEFAULT_FONT, 12, 'italic'),
                      foreground=ModernUI.COLORS['text_secondary'],
                      background=ModernUI.COLORS['background'])

        # Button styles with Material Design principles
        # - Contained: Solid background with elevation
        # - Outlined: Border with transparent background
        # - Text: No border or background, just text

        # Base button style
        style.configure('TButton',
                      font=(DEFAULT_FONT, 14),
                      padding=[16, 10])

        # Contained (Primary) button
        style.configure('Primary.TButton',
                      background=ModernUI.COLORS['primary'],
                      foreground='white',
                      font=(DEFAULT_FONT, 14, 'bold'),
                      relief=tk.RAISED,
                      borderwidth=0)

        style.map('Primary.TButton',
                background=[('active', ModernUI.COLORS['primary_hover']),
                           ('pressed', ModernUI.COLORS['primary_dark'])],
                relief=[('pressed', tk.SUNKEN)])

        # Outlined button
        style.configure('Outlined.TButton',
                      background=ModernUI.COLORS['background'],
                      foreground=ModernUI.COLORS['primary'],
                      borderwidth=1,
                      relief=tk.SOLID)

        style.map('Outlined.TButton',
                background=[('active', ModernUI.COLORS['highlight'])],
                foreground=[('active', ModernUI.COLORS['primary_dark'])])

        # Text button
        style.configure('Text.TButton',
                      background=ModernUI.COLORS['background'],
                      foreground=ModernUI.COLORS['primary'],
                      borderwidth=0,
                      relief=tk.FLAT)

        style.map('Text.TButton',
                background=[('active', ModernUI.COLORS['highlight'])],
                foreground=[('active', ModernUI.COLORS['primary_dark'])])

        # Secondary button (contained)
        style.configure('Secondary.TButton',
                      background=ModernUI.COLORS['secondary'],
                      foreground='white',
                      font=(DEFAULT_FONT, 14, 'bold'),
                      relief=tk.RAISED,
                      borderwidth=0)

        style.map('Secondary.TButton',
                background=[('active', ModernUI.COLORS['secondary_hover']),
                           ('pressed', ModernUI.COLORS['secondary_dark'])],
                relief=[('pressed', tk.SUNKEN)])

        # Accent button
        style.configure('Accent.TButton',
                      background=ModernUI.COLORS['accent'],
                      foreground='white',
                      font=(DEFAULT_FONT, 14, 'bold'),
                      relief=tk.RAISED,
                      borderwidth=0)

        style.map('Accent.TButton',
                background=[('active', ModernUI.COLORS['accent_hover']),
                           ('pressed', ModernUI.COLORS['accent_dark'])],
                relief=[('pressed', tk.SUNKEN)])

        # Combobox with Material Design styling
        style.configure('TCombobox',
                     background=ModernUI.COLORS['surface'],
                     fieldbackground=ModernUI.COLORS['surface'],
                     selectbackground=ModernUI.COLORS['primary'],
                     selectforeground='white',
                     padding=[12, 10],  # Increased padding for better touch targets
                     arrowsize=16,
                     borderwidth=1,
                     font=(DEFAULT_FONT, 14))

        style.map('TCombobox',
                fieldbackground=[('readonly', ModernUI.COLORS['surface']),
                                ('focus', ModernUI.COLORS['surface'])],
                selectbackground=[('readonly', ModernUI.COLORS['primary'])],
                selectforeground=[('readonly', 'white')],
                bordercolor=[('focus', ModernUI.COLORS['primary'])])

        # Progress bar with Material Design appearance
        style.configure('TProgressbar',
                     background=ModernUI.COLORS['primary'],
                     troughcolor=ModernUI.COLORS['background'],
                     thickness=6,  # Thinner for Material Design
                     borderwidth=0)

        # Notebook (tabs) with Material Design styling
        style.configure('TNotebook',
                     background=ModernUI.COLORS['background'],
                     tabmargins=[0, 0, 0, 0],
                     borderwidth=0)

        style.configure('TNotebook.Tab',
                     font=(DEFAULT_FONT, 14),
                     padding=[20, 12],
                     background=ModernUI.COLORS['background'],
                     borderwidth=0)

        style.map('TNotebook.Tab',
                background=[('selected', ModernUI.COLORS['primary']),
                           ('active', ModernUI.COLORS['highlight'])],
                foreground=[('selected', 'white'),
                           ('active', ModernUI.COLORS['primary_dark'])],
                borderwidth=[('selected', 0)])

        # Separator with subtle color
        style.configure('TSeparator', background=ModernUI.COLORS['divider'])

        # Frame styles
        style.configure('Card.TFrame',
                      relief='flat',
                      borderwidth=0,
                      background=ModernUI.COLORS['surface'])

        style.configure('Elevated.TFrame',
                      relief='solid',
                      borderwidth=1,
                      background=ModernUI.COLORS['surface'],
                      bordercolor=ModernUI.COLORS['border'])

        style.configure('Nav.TFrame',
                      background=ModernUI.COLORS['nav_bg'],
                      relief='flat',
                      borderwidth=0)

        # Scale with Material Design appearance
        style.configure('TScale',
                     background=ModernUI.COLORS['background'],
                     troughcolor=ModernUI.COLORS['border'],
                     sliderthickness=20,  # Larger for better touch targets
                     sliderlength=20)

        style.map('TScale',
                troughcolor=[('focus', ModernUI.COLORS['primary_light'])],
                background=[('focus', ModernUI.COLORS['background'])])

        # Entry field styling with Material Design focus state
        style.configure('TEntry',
                     padding=[12, 10],  # Increased padding for better touch targets
                     fieldbackground=ModernUI.COLORS['surface'],
                     foreground=ModernUI.COLORS['text'],
                     font=(DEFAULT_FONT, 14),
                     borderwidth=1,
                     relief='solid',
                     bordercolor=ModernUI.COLORS['border'])

        # Add focus state for entry fields
        style.map('TEntry',
                bordercolor=[('focus', ModernUI.COLORS['primary'])],
                lightcolor=[('focus', ModernUI.COLORS['primary'])],
                darkcolor=[('focus', ModernUI.COLORS['primary'])],
                fieldbackground=[('focus', ModernUI.COLORS['surface'])])

        # Checkbutton with Material Design styling
        style.configure('TCheckbutton',
                     background=ModernUI.COLORS['background'],
                     foreground=ModernUI.COLORS['text'],
                     font=(DEFAULT_FONT, 14),
                     padding=[5, 5])

        style.map('TCheckbutton',
                background=[('active', ModernUI.COLORS['background'])],
                foreground=[('active', ModernUI.COLORS['primary'])],
                indicatorcolor=[('selected', ModernUI.COLORS['primary']),
                               ('active', ModernUI.COLORS['primary_hover'])])

        # Radiobutton with Material Design styling
        style.configure('TRadiobutton',
                     background=ModernUI.COLORS['background'],
                     foreground=ModernUI.COLORS['text'],
                     font=(DEFAULT_FONT, 14),
                     padding=[5, 5])

        style.map('TRadiobutton',
                background=[('active', ModernUI.COLORS['background'])],
                foreground=[('active', ModernUI.COLORS['primary'])],
                indicatorcolor=[('selected', ModernUI.COLORS['primary']),
                               ('active', ModernUI.COLORS['primary_hover'])])

        # Spinbox styling
        style.configure('TSpinbox',
                     arrowsize=14,
                     padding=[10, 8],
                     fieldbackground=ModernUI.COLORS['surface'],
                     foreground=ModernUI.COLORS['text'],
                     borderwidth=1,
                     font=(DEFAULT_FONT, 14))

        style.map('TSpinbox',
                fieldbackground=[('readonly', ModernUI.COLORS['surface']),
                                ('focus', ModernUI.COLORS['surface'])],
                arrowcolor=[('active', ModernUI.COLORS['primary'])],
                bordercolor=[('focus', ModernUI.COLORS['primary'])])

        return style

class ModernButton(tk.Button):
    """Material Design button with hover effects, ripple animation, and elevation"""
    def __init__(self, master, text="Button", command=None, type="primary", width=None, font=None, icon=None, **kwargs):
        self.type = type
        self.master = master

        # Set colors based on button type
        if type == "primary":
            bg_color = ModernUI.COLORS['primary']
            fg_color = 'white'
            active_bg = ModernUI.COLORS['primary_hover']
            active_fg = 'white'
            relief = tk.RAISED
            borderwidth = 0
        elif type == "secondary":
            bg_color = ModernUI.COLORS['secondary']
            fg_color = 'white'
            active_bg = ModernUI.COLORS['secondary_hover']
            active_fg = 'white'
            relief = tk.RAISED
            borderwidth = 0
        elif type == "accent":
            bg_color = ModernUI.COLORS['accent']
            fg_color = 'white'
            active_bg = ModernUI.COLORS['accent_hover']
            active_fg = 'white'
            relief = tk.RAISED
            borderwidth = 0
        elif type == "success":
            bg_color = ModernUI.COLORS['success']
            fg_color = 'white'
            active_bg = '#059669'  # Darker green
            active_fg = 'white'
            relief = tk.RAISED
            borderwidth = 0
        elif type == "danger":
            bg_color = ModernUI.COLORS['error']
            fg_color = 'white'
            active_bg = '#DC2626'  # Darker red
            active_fg = 'white'
            relief = tk.RAISED
            borderwidth = 0
        elif type == "outlined":
            bg_color = ModernUI.COLORS['background']
            fg_color = ModernUI.COLORS['primary']
            active_bg = ModernUI.COLORS['highlight']
            active_fg = ModernUI.COLORS['primary_dark']
            relief = tk.SOLID
            borderwidth = 1
        elif type == "text":
            bg_color = ModernUI.COLORS['background']
            fg_color = ModernUI.COLORS['primary']
            active_bg = ModernUI.COLORS['highlight']
            active_fg = ModernUI.COLORS['primary_dark']
            relief = tk.FLAT
            borderwidth = 0
        elif type == "outline":  # For backward compatibility
            bg_color = ModernUI.COLORS['background']
            fg_color = ModernUI.COLORS['primary']
            active_bg = ModernUI.COLORS['highlight']
            active_fg = ModernUI.COLORS['primary_dark']
            relief = tk.SOLID
            borderwidth = 1
        elif type == "ghost":  # For backward compatibility
            bg_color = ModernUI.COLORS['background']
            fg_color = ModernUI.COLORS['text_secondary']
            active_bg = ModernUI.COLORS['highlight']
            active_fg = ModernUI.COLORS['primary']
            relief = tk.FLAT
            borderwidth = 0
        else:  # Light/default
            bg_color = ModernUI.COLORS['surface']
            fg_color = ModernUI.COLORS['text']
            active_bg = ModernUI.COLORS['border']
            active_fg = ModernUI.COLORS['text']
            relief = tk.FLAT
            borderwidth = 0

        width_val = width if width else 15

        # Use custom font or create default based on button type with larger size
        if font is None:
            button_font = (DEFAULT_FONT, 14, 'bold' if type in ["primary", "secondary", "accent", "success", "danger"] else '')
        else:
            button_font = font

        # Initialize with all parameters
        super().__init__(
            master,
            text=text,
            command=command,
            font=button_font,
            bg=bg_color,
            fg=fg_color,
            activebackground=active_bg,
            activeforeground=active_fg,
            relief=relief,
            borderwidth=borderwidth,
            padx=20,         # More horizontal padding for Material Design
            pady=12,         # More vertical padding for Material Design
            width=width_val,
            cursor="hand2",
            **kwargs
        )

        # Add hover effect
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)

        # Add press effect
        self.bind("<ButtonPress-1>", self._on_press)
        self.bind("<ButtonRelease-1>", self._on_release)

    def _on_enter(self, _):
        # Hover effect with smooth transition
        if self.type == "primary":
            self.config(bg=ModernUI.COLORS['primary_hover'])
        elif self.type == "secondary":
            self.config(bg=ModernUI.COLORS['secondary_hover'])
        elif self.type == "accent":
            self.config(bg=ModernUI.COLORS['accent_hover'])
        elif self.type == "success":
            self.config(bg='#059669')  # Darker green
        elif self.type == "danger":
            self.config(bg='#DC2626')  # Darker red
        elif self.type in ["outlined", "outline"]:
            self.config(bg=ModernUI.COLORS['highlight'], fg=ModernUI.COLORS['primary_dark'])
        elif self.type in ["text", "ghost"]:
            self.config(bg=ModernUI.COLORS['highlight'], fg=ModernUI.COLORS['primary'])
        else:
            self.config(bg=ModernUI.COLORS['border'])

    def _on_leave(self, _):
        # Return to normal state
        if self.type == "primary":
            self.config(bg=ModernUI.COLORS['primary'])
        elif self.type == "secondary":
            self.config(bg=ModernUI.COLORS['secondary'])
        elif self.type == "accent":
            self.config(bg=ModernUI.COLORS['accent'])
        elif self.type == "success":
            self.config(bg=ModernUI.COLORS['success'])
        elif self.type == "danger":
            self.config(bg=ModernUI.COLORS['error'])
        elif self.type in ["outlined", "outline"]:
            self.config(bg=ModernUI.COLORS['background'], fg=ModernUI.COLORS['primary'])
        elif self.type in ["text", "ghost"]:
            self.config(bg=ModernUI.COLORS['background'], fg=ModernUI.COLORS['primary'])
        else:
            self.config(bg=ModernUI.COLORS['surface'])

    def _on_press(self, _):
        # Slight darkening effect when pressed
        if self.type == "primary":
            self.config(bg=ModernUI.COLORS['primary_dark'])
            self.config(relief=tk.SUNKEN)
        elif self.type == "secondary":
            self.config(bg=ModernUI.COLORS['secondary_dark'])
            self.config(relief=tk.SUNKEN)
        elif self.type == "accent":
            self.config(bg=ModernUI.COLORS['accent_dark'])
            self.config(relief=tk.SUNKEN)
        elif self.type == "success":
            self.config(bg='#047857')  # Even darker green
            self.config(relief=tk.SUNKEN)
        elif self.type == "danger":
            self.config(bg='#B91C1C')  # Even darker red
            self.config(relief=tk.SUNKEN)
        elif self.type in ["outlined", "outline", "text", "ghost"]:
            self.config(bg=ModernUI.COLORS['border'])
        else:
            self.config(bg=ModernUI.COLORS['divider'])

    def _on_release(self, e):
        # Return to hover state since mouse is still over button
        self._on_enter(e)

        # Reset relief for contained buttons
        if self.type in ["primary", "secondary", "accent", "success", "danger"]:
            self.config(relief=tk.RAISED)

class CardFrame(ttk.Frame):
    """Material Design card with optional title, elevation, and rounded corners"""
    def __init__(self, master, title=None, padding=16, elevation=1, **kwargs):
        # Determine style based on elevation
        if elevation > 1:
            style_name = 'Elevated.TFrame'
        else:
            style_name = 'Card.TFrame'

        # Create a frame with appropriate style
        super().__init__(master, style=style_name, padding=padding, **kwargs)

        # Add inner padding and configure column
        self.columnconfigure(0, weight=1)

        # Store elevation level
        self.elevation = elevation

        # Add title if provided with Material Design styling
        if title:
            # Create a title frame with proper styling
            title_frame = ttk.Frame(self, style=style_name)
            title_frame.grid(row=0, column=0, sticky='ew', pady=(0, 10))

            # Title with Material Design typography
            title_label = ttk.Label(title_frame, text=title, style='Title.TLabel')
            title_label.pack(side=tk.LEFT, anchor='w')

            # Add a subtle divider
            separator = ttk.Separator(self, orient=tk.HORIZONTAL)
            separator.grid(row=1, column=0, sticky='ew', pady=(0, 12))

            # Content will start at row 2
            self.content_row = 2
        else:
            # Content will start at row 0
            self.content_row = 0

        # Apply post-initialization styling
        self.update_idletasks()  # Ensure the widget is fully created

class ColorPickerButton(ttk.Frame):
    """Material Design color picker button with enhanced color preview"""
    def __init__(self, master, initial_color="#FFFFFF", command=None, **kwargs):
        super().__init__(master, **kwargs)

        self.current_color = initial_color
        self.callback = command
        self.enabled = True

        # Create a container frame with Material Design spacing
        container = ttk.Frame(self)
        container.pack(fill=tk.X, expand=True)

        # Enhanced color preview with Material Design appearance
        preview_frame = ttk.Frame(container, padding=2)
        preview_frame.pack(side=tk.LEFT, padx=(0, 12))

        # Color preview with larger size and subtle border
        self.color_preview = tk.Canvas(
            preview_frame,
            width=36,
            height=36,
            bg=initial_color,
            highlightthickness=1,
            highlightbackground=ModernUI.COLORS['border']
        )
        self.color_preview.pack(padx=1, pady=1)

        # Button with Material Design styling
        self.button = ModernButton(
            container,
            text="Choose Color",
            type="outlined",  # Use Material Design outlined button
            width=12,
            command=self._pick_color
        )
        self.button.pack(side=tk.LEFT)

        # Add a color hex display with Material Design typography
        self.color_label = ttk.Label(
            container,
            text=initial_color.upper(),
            style="Body.TLabel",  # Use Material Design typography
            width=8
        )
        self.color_label.pack(side=tk.LEFT, padx=(12, 0))

    def set_state(self, state):
        """Enable or disable the color picker button"""
        self.button.config(state=state)
        self.enabled = (state == tk.NORMAL)

    def _pick_color(self):
        """Open color chooser dialog and update the selected color"""
        if not self.enabled:
            return

        color = colorchooser.askcolor(initialcolor=self.current_color, title="Choose Color")
        if color[1]:  # If a color was selected (not canceled)
            self.current_color = color[1]
            self.color_preview.config(bg=self.current_color)
            self.color_label.config(text=self.current_color.upper())
            if self.callback:
                self.callback(self.current_color)

    def get_color(self):
        """Return the currently selected color"""
        return self.current_color

    def set_color(self, color):
        """Set the color programmatically"""
        self.current_color = color
        self.color_preview.config(bg=color)
        self.color_label.config(text=color.upper())

    def set_state(self, state):
        """Set the enabled state of the button"""
        if state == tk.DISABLED:
            self.enabled = False
            self.button.config(state=tk.DISABLED)
            self.color_preview.config(highlightbackground=ModernUI.COLORS['text_disabled'])
        else:
            self.enabled = True
            self.button.config(state=tk.NORMAL)
            self.color_preview.config(highlightbackground=ModernUI.COLORS['border'])

class SliderWithValue(ttk.Frame):
    """Material Design slider with value label and improved visual styling"""
    def __init__(self, master, from_=0, to=100, initial=50, label_width=5, length=200, command=None, value_format=None, **kwargs):
        super().__init__(master, **kwargs)

        self.from_ = from_
        self.to = to
        self.value_var = tk.DoubleVar(value=initial)
        self.command = command
        self.value_format = value_format  # Can be None, 'percent', 'pixels', or 'words'

        # Create a container with Material Design spacing
        container = ttk.Frame(self)
        container.pack(fill=tk.X, expand=True, pady=4)

        # Add a title label if provided in kwargs
        if 'title' in kwargs:
            title_label = ttk.Label(container, text=kwargs.pop('title'), style='Body.TLabel')
            title_label.pack(side=tk.TOP, anchor='w', pady=(0, 8))

        # Create a frame for the slider with Material Design layout
        slider_frame = ttk.Frame(container)
        slider_frame.pack(side=tk.TOP, fill=tk.X, expand=True)

        # Slider with Material Design styling
        self.slider = ttk.Scale(
            slider_frame,
            from_=from_,
            to=to,
            variable=self.value_var,
            orient=tk.HORIZONTAL,
            length=length,
            command=self._on_slide
        )
        self.slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 12))

        # Value label with Material Design styling
        value_frame = ttk.Frame(slider_frame, style='Card.TFrame', padding=4)
        value_frame.pack(side=tk.RIGHT)

        self.value_label = ttk.Label(
            value_frame,
            text=self._format_value(initial),
            width=label_width,
            anchor=tk.CENTER,
            style='Body.TLabel'  # Use Material Design typography
        )
        self.value_label.pack(fill=tk.BOTH, expand=True)

        # Add min/max labels if range is significant and show_range is True
        show_range = kwargs.pop('show_range', False) if 'show_range' in kwargs else False
        if abs(to - from_) > 10 and show_range:
            range_frame = ttk.Frame(container)
            range_frame.pack(side=tk.TOP, fill=tk.X, expand=True, pady=(4, 0))

            # Min value label
            min_label = ttk.Label(
                range_frame,
                text=self._format_value(from_),
                style='Caption.TLabel',  # Use Material Design typography
                font=(DEFAULT_FONT, 10)
            )
            min_label.pack(side=tk.LEFT)

            # Spacer
            ttk.Frame(range_frame).pack(side=tk.LEFT, fill=tk.X, expand=True)

            # Max value label
            max_label = ttk.Label(
                range_frame,
                text=self._format_value(to),
                style='Caption.TLabel',  # Use Material Design typography
                font=(DEFAULT_FONT, 10)
            )
            max_label.pack(side=tk.RIGHT)

    def _format_value(self, value):
        """Format the value for display"""
        if self.value_format == 'percent':
            return f"{int(value * 100)}%"
        elif self.value_format == 'pixels':
            if value == int(value):  # If it's a whole number
                return f"{int(value)}px"
            else:
                return f"{value:.1f}px"
        elif self.value_format == 'words':
            return f"{int(value)}"
        elif self.value_format == 'seconds':
            return f"{value:.1f}s"
        else:
            # Auto-detect based on value
            if isinstance(value, float) and value <= 2:  # For rates like 0.8-1.2
                return f"{int(value * 100)}%"
            elif value >= 1.0 and value <= 5.0:  # For end pause duration
                return f"{value:.1f}s"
            elif value == int(value):  # If it's a whole number
                return f"{int(value)}"
            else:
                return f"{value:.1f}"

    def _on_slide(self, *_):
        value = self.value_var.get()
        self.value_label.config(text=self._format_value(value))
        if self.command:
            self.command(value)

    def get(self):
        """Get the current value of the slider"""
        return self.value_var.get()

    def set(self, value):
        """Set the value of the slider"""
        self.value_var.set(value)
        self.value_label.config(text=self._format_value(value))

    def set_state(self, state):
        """Enable or disable the slider"""
        self.slider.config(state=state)

class RadioButtonGroup(ttk.Frame):
    """Group of radio buttons with modern styling"""
    def __init__(self, master, options, variable, default=None, **kwargs):
        super().__init__(master, **kwargs)

        self.variable = variable
        self.radio_buttons = []  # Store references to radio buttons

        if default is not None:
            self.variable.set(default)

        for value, text in options:
            rb = tk.Radiobutton(
                self,
                text=text,
                value=value,
                variable=self.variable,
                font=(DEFAULT_FONT, 10),
                bg=ModernUI.COLORS['card'],
                activebackground=ModernUI.COLORS['highlight'],
                highlightthickness=0,
                borderwidth=0,
                cursor="hand2"
            )
            rb.pack(side=tk.LEFT, padx=10, pady=5)
            self.radio_buttons.append(rb)

    def get(self):
        return self.variable.get()

    def set_state(self, state):
        """Set the state of all radio buttons in the group"""
        for rb in self.radio_buttons:
            rb.config(state=state)

class MainApp:
    def __init__(self, root):
        self.root = root
        self.root.title(APP_NAME)
        self.root.geometry("1200x800")
        self.root.minsize(1100, 750)

        # Apply modern theme
        self.style = ModernUI.apply_theme(root)

        # Create a shared StringVar for AI provider selection
        # This will be used by both dashboard and script generator UIs
        self.shared_llm_provider_var = tk.StringVar(value="Groq")  # Default to Groq

        # Enhanced DPI awareness for Windows
        if platform.system() == "Windows":
            self.root.tk.call('tk', 'scaling', 1.3)

        # Set application icon
        try:
            icon_path = os.path.join(os.path.dirname(script_dir), "assets", "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass

        # Default caption settings
        self.font_color = "#FFFFFF"  # White
        self.outline_color = "#000000"  # Black
        self.font_size = 40
        self.outline_size = 3
        self.caption_words = 3
        self.highlight_style = "text_color"  # Default highlight style
        self.highlight_bg_color = "#3700B3"  # Default background highlight color
        self.highlight_bg_opacity = 0.7  # Default background highlight opacity

        # Processing flag
        self.is_processing = False

        # Log messages storage
        self.log_messages = []

        # Create main container
        self.main_container = ttk.Frame(root)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Create sidebar and content area
        self.setup_layout()

        # Set up pages
        self.setup_dashboard()
        self.setup_subtitle_generator()
        self.setup_script_generator()
        self.setup_settings()
        self.setup_history()

        # Show dashboard by default
        self.show_page("dashboard")

        # Initialize video generator thread
        self.generator_thread = None

        # Initialize tooltip manager
        self.tooltip_text = None

        # Ensure tooltip_text attribute exists in secure builds
        if not hasattr(self, 'tooltip_text'):
            self.tooltip_text = None

        # Reduce flicker and improve responsiveness
        self.root.update_idletasks()

        # Bind window resize events
        self.root.bind("<Configure>", self.on_window_configure)

    def log_message(self, message):
        """Add a message to the log and display it in the console"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)

        # Update the log console if it exists
        if hasattr(self, 'log_console'):
            self.log_console.config(state=tk.NORMAL)
            self.log_console.insert(tk.END, log_entry + "\n")
            self.log_console.see(tk.END)  # Scroll to the bottom
            self.log_console.config(state=tk.DISABLED)
            self.root.update()

    def on_window_configure(self, event):
        """Optimize window resizing"""
        if event.widget == self.root:
            pass  # The handler helps smooth resizing

    def setup_layout(self):
        """Create the main application layout with sidebar and content area"""
        # Modern header bar with gradient background
        self.header_bar = tk.Frame(self.main_container, height=60)
        self.header_bar.pack(fill=tk.X, side=tk.TOP, padx=0, pady=0)

        # Create gradient effect with Canvas
        header_canvas = tk.Canvas(self.header_bar, height=60, highlightthickness=0)
        header_canvas.pack(fill=tk.X, expand=True)

        # Create gradient from primary to primary_dark
        gradient_colors = [ModernUI.COLORS['primary'], ModernUI.COLORS['primary_dark']]
        self._create_gradient(header_canvas, gradient_colors, 'horizontal')

        # Create a frame for header content that sits on top of the canvas
        header_content = tk.Frame(self.header_bar, bg=ModernUI.COLORS['primary'])
        header_content.place(relx=0, rely=0, relwidth=1, relheight=1)

        # Left section: App logo and title
        left_section = tk.Frame(header_content, bg=ModernUI.COLORS['primary'])
        left_section.pack(side=tk.LEFT, fill=tk.Y, padx=15)

        # App logo
        logo_label = tk.Label(
            left_section,
            text="🎬",  # Movie camera emoji as logo
            font=(DEFAULT_FONT, 28),
            fg="white",
            bg=ModernUI.COLORS['primary']
        )
        logo_label.pack(side=tk.LEFT, padx=(0, 15), pady=10)

        # App title with improved styling - make it larger and more prominent
        title_frame = tk.Frame(left_section, bg=ModernUI.COLORS['primary'])
        title_frame.pack(side=tk.LEFT, pady=10)

        app_title = tk.Label(
            title_frame,
            text="Azanx AI Autoshorts",  # Correct application name
            font=(DEFAULT_FONT, 22, 'bold'),
            fg="white",
            bg=ModernUI.COLORS['primary']
        )
        app_title.pack(side=tk.TOP, anchor='w')

        # Tagline with better contrast
        app_tagline = tk.Label(
            title_frame,
            text="AI-Powered Video Creation",
            font=(DEFAULT_FONT, 12),
            fg="#F3F4F6",  # Lighter gray for better contrast
            bg=ModernUI.COLORS['primary']
        )
        app_tagline.pack(side=tk.TOP, anchor='w', pady=(2, 0))

        # Center section: Spacer
        center_section = tk.Frame(header_content, bg=ModernUI.COLORS['primary'])
        center_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=12)

        # Right section: Quick action buttons
        right_section = tk.Frame(header_content, bg=ModernUI.COLORS['primary'])
        right_section.pack(side=tk.RIGHT, fill=tk.Y, padx=15)

        # Create action buttons
        action_buttons = [
            {"icon": "🔄", "tooltip": "Refresh Fonts & Music", "command": self.refresh_resources},
            {"icon": "⚙️", "tooltip": "Settings", "command": lambda: self.show_page("settings")},
            {"icon": "❓", "tooltip": "WhatsApp Support", "command": self.show_help}
        ]

        for btn_data in action_buttons:
            btn_frame = tk.Frame(right_section, bg=ModernUI.COLORS['primary'])
            btn_frame.pack(side=tk.LEFT, padx=8)

            btn = tk.Button(
                btn_frame,
                text=btn_data["icon"],
                font=(DEFAULT_FONT, 16),
                fg="white",
                bg=ModernUI.COLORS['primary'],
                relief=tk.FLAT,
                borderwidth=0,
                highlightthickness=0,
                padx=5,
                pady=5,
                cursor="hand2",
                activebackground=ModernUI.COLORS['primary_dark'],
                activeforeground="white",
                command=btn_data["command"]
            )
            btn.pack(pady=5)

            # Add tooltip
            btn.bind("<Enter>", lambda e, b=btn, t=btn_data["tooltip"]: self.show_tooltip(e, b, t))
            btn.bind("<Leave>", lambda e: self.hide_tooltip())

        # Version badge
        version_badge = tk.Label(
            right_section,
            text=f"v{APP_VERSION}",
            font=(DEFAULT_FONT, 9),
            fg="white",
            bg=ModernUI.COLORS['primary_dark'],
            padx=8,
            pady=2
        )
        version_badge.pack(side=tk.LEFT, padx=(15, 0), pady=15)

        # Make the version badge rounded
        version_badge.bind("<Configure>", lambda e: self._round_rectangle(version_badge, 10))

        # Content area container
        content_container = ttk.Frame(self.main_container)
        content_container.pack(fill=tk.BOTH, expand=True, side=tk.TOP)

        # Modern sidebar with dark background - reduced width
        self.sidebar = ttk.Frame(content_container, style='Nav.TFrame', width=180)
        self.sidebar.pack(fill=tk.Y, side=tk.LEFT, padx=0, pady=0)
        self.sidebar.pack_propagate(False)  # Prevent sidebar width changes

        # App logo/title with improved styling
        logo_frame = ttk.Frame(self.sidebar, style='Nav.TFrame')
        logo_frame.pack(fill=tk.X, padx=15, pady=25)

        # App logo icon
        logo_icon = ttk.Label(
            logo_frame,
            text="🎬",  # Movie camera emoji as logo
            font=(DEFAULT_FONT, 22),
            foreground=ModernUI.COLORS['accent'],
            style='Nav.TLabel'
        )
        logo_icon.pack(side=tk.LEFT, padx=(0, 10))

        # Title container
        title_container = ttk.Frame(logo_frame, style='Nav.TFrame')
        title_container.pack(side=tk.LEFT)

        # App name with two-tone styling
        ttk.Label(
            title_container,
            text="Faceless",
            style='Nav.TLabel',
            font=(DEFAULT_FONT, 18, 'bold')
        ).pack(side=tk.TOP, anchor='w')

        ttk.Label(
            title_container,
            text="Video Generator",
            style='Nav.TLabel',
            font=(DEFAULT_FONT, 12),
            foreground=ModernUI.COLORS['accent']
        ).pack(side=tk.TOP, anchor='w')

        # Add separator
        separator = ttk.Separator(self.sidebar, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=15, pady=10)

        # Navigation section title
        nav_title = ttk.Label(
            self.sidebar,
            text="NAVIGATION",
            style='Nav.TLabel',
            font=(DEFAULT_FONT, 9),
            foreground=ModernUI.COLORS['text_secondary']
        )
        nav_title.pack(anchor='w', padx=20, pady=(15, 10))

        # Navigation buttons
        self.nav_buttons = []
        self.current_page = "dashboard"  # Default page

        # Navigation data: (page_id, icon, button_text, tooltip)
        nav_data = [
            ("dashboard", "🏠", "Dashboard", "Generate and customize videos"),
            ("subtitle_generator", "🎞️", "Subtitle Generator", "Add subtitles to existing videos"),
            ("script_generator", "📝", "Script Generator", "Create engaging scripts for videos"),
            ("settings", "⚙️", "Settings", "Configure application settings"),
            ("history", "📜", "History", "View previously generated videos")
        ]

        # Create a frame to hold all navigation buttons
        nav_buttons_frame = ttk.Frame(self.sidebar, style='Nav.TFrame')
        nav_buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        for page_id, icon, text, tooltip in nav_data:
            # Create a frame for each button with indicator
            button_container = ttk.Frame(nav_buttons_frame, style='Nav.TFrame')
            button_container.pack(fill=tk.X, pady=3)

            # Active indicator bar (hidden by default)
            indicator = tk.Frame(
                button_container,
                width=4,
                background=ModernUI.COLORS['accent'],
                highlightthickness=0
            )
            indicator.pack(side=tk.LEFT, fill=tk.Y)

            # Hide indicator initially
            if page_id != self.current_page:
                indicator.configure(background=ModernUI.COLORS['nav_bg'])

            # Button frame
            button_frame = ttk.Frame(button_container, style='Nav.TFrame')
            button_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # Create the button with icon and text
            button = tk.Button(
                button_frame,
                text=f"{icon}  {text}",
                anchor="w",
                bg=ModernUI.COLORS['nav_bg'],
                fg=ModernUI.COLORS['nav_text'],
                font=(DEFAULT_FONT, 12),
                borderwidth=0,
                padx=15,
                pady=12,
                highlightthickness=0,
                cursor="hand2",
                activebackground=ModernUI.COLORS['nav_highlight'],
                activeforeground=ModernUI.COLORS['nav_text'],
                wraplength=150,  # Ensure text doesn't get truncated
                width=15,        # Fixed width to ensure consistent sidebar size
                command=lambda p=page_id: self.show_page(p)
            )
            button.pack(fill=tk.X)

            # Store both the button and its indicator for state management
            self.nav_buttons.append((page_id, button, indicator))

            # Tooltip binding
            button.bind("<Enter>", lambda e, b=button, t=tooltip: self.show_tooltip(e, b, t), add="+")
            button.bind("<Leave>", lambda e: self.hide_tooltip(), add="+")

        # Add a spacer
        ttk.Frame(self.sidebar, style='Nav.TFrame', height=20).pack(fill=tk.X)

        # Add separator before footer
        separator2 = ttk.Separator(self.sidebar, orient=tk.HORIZONTAL)
        separator2.pack(fill=tk.X, padx=15, pady=10)

        # Sidebar footer with help links
        footer_frame = ttk.Frame(self.sidebar, style='Nav.TFrame')
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=20)

        # Version info
        version_label = ttk.Label(
            self.sidebar,
            text=f"Version {APP_VERSION}",
            style='Nav.TLabel',
            font=(DEFAULT_FONT, 9),
            foreground=ModernUI.COLORS['text_secondary']
        )
        version_label.pack(side=tk.BOTTOM, anchor='w', padx=20, pady=10)

        # Get current user email
        from auth import get_current_user_email
        current_user = get_current_user_email()
        if current_user:
            # Show user email with truncation if too long
            display_email = current_user
            if len(display_email) > 25:
                display_email = display_email[:22] + '...'

            user_label = ttk.Label(
                self.sidebar,
                text=f"Logged in as: {display_email}",
                style='Nav.TLabel',
                font=(DEFAULT_FONT, 9),
                foreground=ModernUI.COLORS['text_secondary']
            )
            user_label.pack(side=tk.BOTTOM, anchor='w', padx=20, pady=(0, 5))

        # WhatsApp support button with improved styling
        help_button = tk.Button(
            footer_frame,
            text="❓ WhatsApp Support",
            anchor="w",
            bg=ModernUI.COLORS['nav_bg'],
            fg=ModernUI.COLORS['accent'],
            font=(DEFAULT_FONT, 11),
            borderwidth=0,
            padx=10,
            pady=8,
            highlightthickness=0,
            cursor="hand2",
            activebackground=ModernUI.COLORS['nav_highlight'],
            command=self.show_help
        )
        help_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        # About button with improved styling
        about_button = tk.Button(
            footer_frame,
            text="ℹ️ About",
            anchor="w",
            bg=ModernUI.COLORS['nav_bg'],
            fg=ModernUI.COLORS['accent'],
            font=(DEFAULT_FONT, 11),
            borderwidth=0,
            padx=10,
            pady=8,
            highlightthickness=0,
            cursor="hand2",
            activebackground=ModernUI.COLORS['nav_highlight'],
            command=self.show_about
        )
        about_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        # Logout button with improved styling
        logout_button = tk.Button(
            footer_frame,
            text="🚪 Logout",
            anchor="w",
            bg=ModernUI.COLORS['nav_bg'],
            fg="#e74c3c",  # Red color for logout
            font=(DEFAULT_FONT, 11),
            borderwidth=0,
            padx=10,
            pady=8,
            highlightthickness=0,
            cursor="hand2",
            activebackground=ModernUI.COLORS['nav_highlight'],
            command=self.logout
        )
        logout_button.pack(side=tk.TOP, fill=tk.X)

        # Main content area
        self.content_area = ttk.Frame(content_container)
        self.content_area.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Create frames for different pages
        self.pages = {}
        for page_id in ["dashboard", "subtitle_generator", "script_generator", "settings", "history"]:
            self.pages[page_id] = ttk.Frame(self.content_area)
            self.pages[page_id].pack(fill=tk.BOTH, expand=True)
            self.pages[page_id].pack_forget()  # Hide initially

    # We no longer need the on_nav_hover method since we're using activebackground
    # property in the buttons, which handles hover effects automatically
    def on_nav_hover(self, event, button, entering):
        """This method is kept for compatibility but no longer needed"""
        pass

    def _apply_hover_state(self, button, entering):
        """This method is kept for compatibility but no longer needed"""
        pass

    def show_page(self, page_id):
        """Switch between pages"""
        # Hide all pages
        for page in self.pages.values():
            page.pack_forget()

        # Show selected page
        self.pages[page_id].pack(fill=tk.BOTH, expand=True)

        # Update nav button styles
        for pid, button, indicator in self.nav_buttons:
            if pid == page_id:
                # Active page styling
                button.config(bg=ModernUI.COLORS['nav_highlight'], fg=ModernUI.COLORS['accent'])
                indicator.config(background=ModernUI.COLORS['accent'])
            else:
                # Inactive page styling
                button.config(bg=ModernUI.COLORS['nav_bg'], fg=ModernUI.COLORS['nav_text'])
                indicator.config(background=ModernUI.COLORS['nav_bg'])

        self.current_page = page_id

    def show_tooltip(self, event, widget, text):
        """Show tooltip with text near the widget"""
        # Ensure tooltip_text attribute exists
        if not hasattr(self, 'tooltip_text'):
            self.tooltip_text = None

        if self.tooltip_text is None and text:
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25

            # Create a toplevel window
            self.tooltip_text = tk.Toplevel(widget)
            self.tooltip_text.wm_overrideredirect(True)  # Remove window decorations
            self.tooltip_text.wm_geometry(f"+{x}+{y}")

            # Add a label with tooltip text
            label = tk.Label(
                self.tooltip_text,
                text=text,
                justify=tk.LEFT,
                background=ModernUI.COLORS['secondary'],
                foreground=ModernUI.COLORS['text'],
                relief=tk.SOLID,
                borderwidth=1,
                font=(DEFAULT_FONT, 9, "normal"),
                padx=5,
                pady=3
            )
            label.pack()

            # Schedule hiding the tooltip after 2 seconds
            self.tooltip_text.after(2000, self.hide_tooltip)

    def hide_tooltip(self):
        """Hide tooltip window"""
        # Ensure tooltip_text attribute exists
        if not hasattr(self, 'tooltip_text'):
            self.tooltip_text = None

        if self.tooltip_text:
            self.tooltip_text.destroy()
            self.tooltip_text = None

    def _create_gradient(self, canvas, colors, direction='vertical'):
        """Create a gradient background on a canvas"""
        width = canvas.winfo_width()
        height = canvas.winfo_height()

        # If the canvas hasn't been drawn yet, use default size
        if width <= 1:
            width = canvas.winfo_reqwidth()
        if height <= 1:
            height = canvas.winfo_reqheight()

        # Create gradient
        if direction == 'vertical':
            # Vertical gradient
            for i in range(height):
                # Calculate color for this line
                ratio = i / height
                r1, g1, b1 = self._hex_to_rgb(colors[0])
                r2, g2, b2 = self._hex_to_rgb(colors[1])
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                color = f'#{r:02x}{g:02x}{b:02x}'
                canvas.create_line(0, i, width, i, fill=color)
        else:
            # Horizontal gradient
            for i in range(width):
                # Calculate color for this line
                ratio = i / width
                r1, g1, b1 = self._hex_to_rgb(colors[0])
                r2, g2, b2 = self._hex_to_rgb(colors[1])
                r = int(r1 * (1 - ratio) + r2 * ratio)
                g = int(g1 * (1 - ratio) + g2 * ratio)
                b = int(b1 * (1 - ratio) + b2 * ratio)
                color = f'#{r:02x}{g:02x}{b:02x}'
                canvas.create_line(i, 0, i, height, fill=color)

    def _hex_to_rgb(self, hex_color):
        """Convert hex color to RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _round_rectangle(self, widget, radius):
        """Apply rounded corners to a widget using a mask"""
        # This is a placeholder for the actual implementation
        # In a real implementation, we would create a rounded rectangle mask
        # and apply it to the widget, but this requires platform-specific code
        pass

    def _on_search_focus_in(self, event):
        """Handle search field focus in"""
        if self.search_var.get() == "Search videos, templates...":
            self.search_var.set("")
            self.search_entry.config(fg=ModernUI.COLORS['text'])

    def _on_search_focus_out(self, event):
        """Handle search field focus out"""
        if not self.search_var.get().strip():
            self.search_var.set("Search videos, templates...")
            self.search_entry.config(fg=ModernUI.COLORS['text_secondary'])

    def show_account_info(self):
        """Show account information dialog"""
        # Get current user email
        from auth import get_current_user_email
        current_user = get_current_user_email() or "Unknown User"

        messagebox.showinfo("Account Information", f"User: {current_user}\nPlan: Professional\nCredits: 100")

    def logout(self):
        """Log out the current user and restart the application"""
        from auth import logout_user

        # Confirm logout
        if messagebox.askyesno("Confirm Logout", "Are you sure you want to log out?"):
            # Clear credentials
            if logout_user():
                messagebox.showinfo("Logout Successful", "You have been logged out successfully.")

                # Restart the application
                self.root.destroy()

                # Re-launch the application
                import sys
                import os
                import subprocess

                # Get the current script path
                script_path = sys.argv[0]

                # Start a new process with the same arguments
                subprocess.Popen([sys.executable, script_path] + sys.argv[1:])
            else:
                messagebox.showerror("Logout Failed", "Failed to log out. Please try again.")

    def show_help(self):
        """Open WhatsApp chat for support"""
        try:
            # Open WhatsApp chat link
            webbrowser.open("https://wa.me/+923107520004")
        except:
            messagebox.showinfo("Help", "Please contact us on WhatsApp at +923107520004 for support.")

    def show_about(self):
        """Show about dialog"""
        about_window = tk.Toplevel(self.root)
        about_window.title("About")
        about_window.geometry("400x300")
        about_window.resizable(False, False)
        ModernUI.apply_theme(about_window)

        # Center window
        about_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + self.root.winfo_width()//2 - 200,
            self.root.winfo_rooty() + self.root.winfo_height()//2 - 150
        ))

        # Content frame
        content = ttk.Frame(about_window, padding=25)
        content.pack(fill=tk.BOTH, expand=True)

        # App title
        ttk.Label(content, text=APP_NAME, style='Title.TLabel').pack(pady=(0, 5))
        ttk.Label(content, text=f"Version {APP_VERSION}", style='Subtitle.TLabel').pack(pady=(0, 15))

        # Description
        description = ttk.Label(
            content,
            text="Generate faceless videos with AI-generated images and voice narration.",
            wraplength=350,
            justify=tk.CENTER
        )
        description.pack(pady=10)

        # Credits
        ttk.Label(content, text="© 2023-2025 Azanx AI. All Rights Reserved").pack(pady=(20, 5))

        # Close button
        close_btn = ModernButton(content, text="Close", command=about_window.destroy, type="primary")
        close_btn.pack(pady=15)

    def setup_subtitle_generator(self):
        """Set up the subtitle generator page"""
        # Import the SubtitleGeneratorPage class
        from subtitle_generator import SubtitleGeneratorPage

        # Create the subtitle generator page
        self.subtitle_generator_page = SubtitleGeneratorPage(self, self.pages["subtitle_generator"])

    def setup_dashboard(self):
        """Set up the dashboard (main video generation) page"""
        dashboard = self.pages["dashboard"]

        # Create a scrollable content area
        self.dash_canvas = tk.Canvas(dashboard, bg=ModernUI.COLORS['background'], highlightthickness=0)
        self.dash_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(dashboard, orient=tk.VERTICAL, command=self.dash_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure the canvas
        self.dash_canvas.configure(yscrollcommand=scrollbar.set)
        self.dash_canvas.bind('<Configure>', self._on_canvas_configure)

        # Bind mouse wheel events for scrolling
        self._bind_mousewheel(self.dash_canvas)

        # Create a frame inside the canvas
        self.dash_content = ttk.Frame(self.dash_canvas)
        self.dash_canvas.create_window((0, 0), window=self.dash_content, anchor="nw", tags="self.dash_content")

        # Update mouse wheel bindings after content is created
        self.dash_content.bind("<Configure>", lambda e: self._bind_mousewheel_to_children(self.dash_content, self.dash_canvas))

        # Top content area (2 columns) - adjusted column widths
        top_frame = ttk.Frame(self.dash_content)
        top_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Configure grid columns with better proportions
        top_frame.columnconfigure(0, weight=2)  # Options column
        top_frame.columnconfigure(1, weight=1)  # Status column

        # === LEFT COLUMN (Options) ===
        options_column = ttk.Frame(top_frame)
        options_column.grid(row=0, column=0, sticky="nsew", padx=(0, 8))

        # Title
        header_frame = ttk.Frame(options_column)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(header_frame, text="New Video", style='Header.TLabel').pack(side=tk.LEFT)

        # === MAIN OPTIONS CARD ===
        main_options = CardFrame(options_column, title="Content Options")
        main_options.pack(fill=tk.X, pady=(0, 10))

        options_grid = ttk.Frame(main_options)
        options_grid.grid(row=main_options.content_row, column=0, sticky="nsew")

        # Configure grid
        options_grid.columnconfigure(0, weight=1)  # Label column
        options_grid.columnconfigure(1, weight=3)  # Input column

        # Story Type Selection with improved layout
        field_frame = ttk.Frame(options_grid)
        field_frame.grid(row=0, column=0, columnspan=2, sticky=tk.EW, pady=(10, 15))

        # Label with better styling
        ttk.Label(field_frame, text="Story Type:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # Combobox with consistent width and improved styling
        self.story_combo = ttk.Combobox(field_frame, values=[
            "Scary", "Mystery", "Bedtime", "Interesting History",
            "Urban Legends", "Motivational", "Fun Facts",
            "Long Form Jokes", "Life Pro Tips", "Philosophy", "Love",
            "AITA Stories", "Storytime", "POV", "Day in the Life",
            "True Crime", "Celebrity Facts", "Conspiracy Theories",
            "Money Saving Tips", "Fitness Hacks", "Psychology Facts",
            "Product Reviews", "Travel Guides", "DIY Tutorials",
            "Cooking Tips", "Dating Advice", "Pet Tips", "Islamic"
        ], state="readonly", width=40)
        self.story_combo.current(6)  # Default to Fun Facts
        self.story_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Image Style Selection with improved layout
        field_frame2 = ttk.Frame(options_grid)
        field_frame2.grid(row=1, column=0, columnspan=2, sticky=tk.EW, pady=(0, 15))

        # Label with better styling
        ttk.Label(field_frame2, text="Image Style:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # Combobox with consistent width and improved styling
        self.style_combo = ttk.Combobox(field_frame2, values=[
            "Photorealistic", "Cinematic", "Anime", "Comic Book",
            "Pixar Art", "Digital Art", "Oil Painting", "Watercolor",
            "Pixel Art", "Dark Aesthetic", "Neon Cyberpunk", "Minimalist",
            "Film Noir", "Retro 80s", "Vaporwave", "Cottagecore",
            "Hyperrealistic", "Flat Design", "3D Cartoon",
            "Pastel Dreamscape", "Fantasy Vibrant", "Nostalgic Filter",
            "VHS Aesthetic", "Y2K", "God Anime Vine", "Ghibli"
        ], state="readonly", width=40)
        self.style_combo.current(1)  # Default to Cinematic
        self.style_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Image Model Selection with improved layout
        field_frame3 = ttk.Frame(options_grid)
        field_frame3.grid(row=2, column=0, columnspan=2, sticky=tk.EW, pady=(0, 15))

        # Label with better styling
        ttk.Label(field_frame3, text="Image Generator:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # Combobox with consistent width and improved styling
        self.image_model_combo = ttk.Combobox(field_frame3, values=[
            "Replicate Flux", "FAL AI Flux", "Together AI Flux"
        ], state="readonly", width=40)
        self.image_model_combo.current(2)  # Default to Together AI Flux
        self.image_model_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # LLM (AI Provider) Selection with improved layout
        field_frame4 = ttk.Frame(options_grid)
        field_frame4.grid(row=3, column=0, columnspan=2, sticky=tk.EW, pady=(0, 15))

        # Label with better styling
        ttk.Label(field_frame4, text="AI Provider:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))

        # Radio button group for AI provider selection
        radio_frame = ttk.Frame(field_frame4)
        radio_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Use the shared StringVar for AI provider selection
        # This ensures both dashboard and script generator UIs stay in sync

        # Create radio buttons for each provider
        for i, provider in enumerate(AI_PROVIDERS):
            rb = ttk.Radiobutton(
                radio_frame,
                text=provider,
                variable=self.shared_llm_provider_var,
                value=provider,
                command=self.on_ai_provider_change
            )
            rb.pack(side=tk.LEFT, padx=(0 if i == 0 else 15, 0))

        # Add a label to show the current AI provider status
        self.ai_provider_status = ttk.Label(field_frame4, text="Status: Groq Ready", foreground=ModernUI.COLORS['success'])
        self.ai_provider_status.pack(side=tk.LEFT, padx=10)

        # === VOICE OPTIONS CARD ===
        voice_options = CardFrame(options_column, title="Voice Options")
        voice_options.pack(fill=tk.X, pady=(0, 10))

        voice_grid = ttk.Frame(voice_options)
        voice_grid.grid(row=voice_options.content_row, column=0, sticky="nsew")

        # Configure grid
        voice_grid.columnconfigure(0, weight=1)  # Label column
        voice_grid.columnconfigure(1, weight=3)  # Input column

        # TTS Model Selection
        ttk.Label(voice_grid, text="TTS Model:", style='FormField.TLabel').grid(row=0, column=0, sticky=tk.W, pady=10)
        self.tts_model_combo = ttk.Combobox(voice_grid, values=[
            "OpenAI", "Voicely", "ElevenLabs"
        ], state="readonly", width=35)
        self.tts_model_combo.current(1)  # Default to Voicely
        self.tts_model_combo.grid(row=0, column=1, sticky=tk.W, pady=10, padx=5)

        # Voice Selection
        ttk.Label(voice_grid, text="Voice:", style='FormField.TLabel').grid(row=1, column=0, sticky=tk.W, pady=10)

        # Load all English voices from the JSON file
        try:
            with open(os.path.join(script_dir, "english_edge_voices.json"), "r") as f:
                english_voices = json.load(f)

            # Create a list of voice names for the dropdown
            voice_options = []
            for voice in english_voices:
                short_name = voice["ShortName"]
                friendly_name = voice["FriendlyName"]
                locale = voice["Locale"]
                gender = voice["Gender"]
                # Format: ShortName (Locale - Gender)
                voice_options.append(f"{short_name}")

            # Sort voices by locale and gender for better organization
            voice_options.sort()
        except Exception as e:
            print(f"Error loading voices: {str(e)}")
            # Fallback to a default list if the JSON file is not available
            voice_options = [
                "en-US-AriaNeural", "en-US-JennyNeural", "en-US-GuyNeural", "en-US-AndrewNeural",
                "en-GB-SoniaNeural", "en-GB-RyanNeural",
                "en-AU-NatashaNeural", "en-CA-ClaraNeural", "en-IN-NeerjaNeural",
                "en-US-MichelleNeural", "en-US-BrianNeural", "en-US-EmmaNeural", "en-US-EricNeural"
            ]

        # Create a frame for the voice selection with a filter
        voice_selection_frame = ttk.Frame(voice_grid)
        voice_selection_frame.grid(row=1, column=1, sticky=tk.W, pady=10, padx=5)

        # Add a filter dropdown for locales (only visible when Voicely is selected)
        self.locale_filter_frame = ttk.Frame(voice_selection_frame)
        # Only show the locale filter if Voicely is selected initially
        if self.tts_model_combo.get() == "Voicely":
            self.locale_filter_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(self.locale_filter_frame, text="Filter by locale:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 5))

        # Get unique locales and map them to full country names
        locale_to_country = {
            "en-US": "United States",
            "en-GB": "United Kingdom",
            "en-AU": "Australia",
            "en-CA": "Canada",
            "en-IN": "India",
            "en-IE": "Ireland",
            "en-NZ": "New Zealand",
            "en-NG": "Nigeria",
            "en-PH": "Philippines",
            "en-SG": "Singapore",
            "en-ZA": "South Africa",
            "en-TZ": "Tanzania",
            "en-KE": "Kenya",
            "en-HK": "Hong Kong"
        }

        try:
            # Get unique locale codes
            locale_codes = sorted(list(set(voice["Locale"] for voice in english_voices)))

            # Create a list of tuples with (locale_code, country_name)
            locale_display = []
            for code in locale_codes:
                country_name = locale_to_country.get(code, code)
                locale_display.append((code, country_name))

            # Sort by country name
            locale_display.sort(key=lambda x: x[1])

            # Create the final options list with "All" at the beginning
            locale_options = ["All"] + [f"{country} ({code})" for code, country in locale_display]

            # Create a mapping from display name back to locale code for filtering
            self.country_to_locale = {"All": "All"}
            for code, country in locale_display:
                self.country_to_locale[f"{country} ({code})"] = code

        except:
            # Fallback options
            locale_options = ["All", "United States (en-US)", "United Kingdom (en-GB)",
                             "Australia (en-AU)", "Canada (en-CA)", "India (en-IN)"]

            # Fallback mapping
            self.country_to_locale = {
                "All": "All",
                "United States (en-US)": "en-US",
                "United Kingdom (en-GB)": "en-GB",
                "Australia (en-AU)": "en-AU",
                "Canada (en-CA)": "en-CA",
                "India (en-IN)": "en-IN"
            }

        self.locale_filter_var = tk.StringVar(value="All")
        self.locale_filter_combo = ttk.Combobox(
            self.locale_filter_frame,
            values=locale_options,
            textvariable=self.locale_filter_var,
            state="readonly",
            width=20
        )
        self.locale_filter_combo.pack(side=tk.LEFT)
        self.locale_filter_combo.bind("<<ComboboxSelected>>", self.filter_voices)

        # Add a prominent Preview Voice button at the top
        preview_button_frame = ttk.Frame(voice_selection_frame)
        preview_button_frame.pack(fill=tk.X, pady=(0, 5))

        preview_voice_button = ModernButton(
            preview_button_frame,
            text="Preview Selected Voice",
            command=self.preview_selected_voice,
            type="primary",  # Use primary style for prominence
            width=25
        )
        preview_voice_button.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Voice selection widget with play button
        self.voice_combo = VoiceSelectionWidget(voice_selection_frame, values=voice_options, width=25)
        self.voice_combo.pack(fill=tk.X)

        # Set default to Aria if available, otherwise first voice
        aria_found = False
        for voice in voice_options:
            if "AriaNeural" in voice:
                self.voice_combo.set(voice)
                aria_found = True
                break

        if not aria_found and voice_options:
            self.voice_combo.current(0)

        # Add a button to refresh the voice list
        refresh_button = ttk.Button(
            voice_selection_frame,
            text="Refresh Voice List",
            command=self.refresh_voice_list
        )
        refresh_button.pack(anchor=tk.E, pady=(5, 0))

        # Font selector
        ttk.Label(voice_grid, text="Subtitle Font:", style='FormField.TLabel').grid(row=2, column=0, sticky=tk.W, pady=10)

        # Get available fonts from the font directory
        available_fonts = get_available_fonts()

        # If no fonts found, use a default list
        if not available_fonts:
            available_fonts = ["TitanOne", "Ranchers", "RampartOne", "PermanentMarker", "OpenSans", "NotoSans",
                             "Montserrat", "LuckiestGuy", "Knewave", "Jua", "Creepster", "Caveat",
                             "Bungee", "BebasNeue", "Bangers", "BakbakOne"]
            print("No fonts found in font directory, using default font list")

        # Create a frame to hold the combobox and helper text
        font_container = ttk.Frame(voice_grid)
        font_container.grid(row=2, column=1, sticky=tk.W, pady=10, padx=5)

        # Create the combobox in the container frame
        self.font_combo = ttk.Combobox(
            font_container,
            values=available_fonts,
            state="readonly", width=35
        )

        # Set default font (TitanOne if available, otherwise first font in list)
        if "TitanOne" in available_fonts:
            self.font_combo.set("TitanOne")
        else:
            self.font_combo.current(0)

        self.font_combo.pack(side=tk.TOP, anchor=tk.W, fill=tk.X)

        # Add helper text about adding custom fonts below the combobox
        font_help = ttk.Label(
            font_container,
            text="Add your own .ttf or .otf fonts to the 'font' folder",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        font_help.pack(side=tk.TOP, anchor=tk.W, pady=(2, 0))

        # Speech Rate slider
        ttk.Label(voice_grid, text="Speech Rate:", style='FormField.TLabel').grid(row=3, column=0, sticky=tk.W, pady=10)
        self.speech_rate_var = tk.DoubleVar(value=1.0)
        self.speech_rate_slider = SliderWithValue(
            voice_grid,
            from_=0.8,
            to=1.2,
            initial=1.0,
            command=lambda val: None,  # No need for specific command
            value_format='percent'
        )
        self.speech_rate_slider.grid(row=3, column=1, sticky=tk.W, pady=10, padx=5)

        # Add background music controls to the voice options
        ttk.Separator(voice_grid, orient=tk.HORIZONTAL).grid(row=4, column=0, columnspan=2, sticky=tk.EW, pady=15)
        ttk.Label(voice_grid, text="Background Music:", style='FormField.TLabel').grid(row=5, column=0, sticky=tk.W, pady=10)

        # Load music files directly from the music folder
        music_dir = os.path.join(os.path.dirname(script_dir), "music")
        music_options = ["None"]

        # Create music directory if it doesn't exist
        os.makedirs(music_dir, exist_ok=True)

        # Scan for MP3 files in the music directory
        for file in os.listdir(music_dir):
            if file.lower().endswith(".mp3"):
                # Use the filename without extension as the display name
                music_name = os.path.splitext(file)[0]
                music_options.append(music_name)

        self.bg_music_combo = ttk.Combobox(voice_grid, values=music_options, state="readonly", width=35)
        self.bg_music_combo.current(0)  # Default to None
        self.bg_music_combo.grid(row=5, column=1, sticky=tk.W, pady=10, padx=5)

        # Volume slider
        ttk.Label(voice_grid, text="Music Volume:", style='FormField.TLabel').grid(row=6, column=0, sticky=tk.W, pady=10)
        self.bg_music_volume_var = tk.DoubleVar(value=0.2)
        self.bg_music_volume_slider = SliderWithValue(
            voice_grid,
            from_=0.0,
            to=1.0,
            initial=0.2,
            command=lambda val: None,
            value_format='percent'
        )
        self.bg_music_volume_slider.grid(row=6, column=1, sticky=tk.W, pady=10, padx=5)

        # End Pause Duration slider
        ttk.Label(voice_grid, text="End Pause Duration:", style='FormField.TLabel').grid(row=7, column=0, sticky=tk.W, pady=10)
        self.end_pause_duration_var = tk.DoubleVar(value=3.5)
        self.end_pause_duration_slider = SliderWithValue(
            voice_grid,
            from_=1.0,
            to=5.0,
            initial=3.5,
            command=lambda val: None,
            value_format='seconds'
        )
        self.end_pause_duration_slider.grid(row=7, column=1, sticky=tk.W, pady=10, padx=5)

        # Add helper text for end pause duration
        end_pause_help = ttk.Label(
            voice_grid,
            text="Duration of pause at the end of video (in seconds)",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        end_pause_help.grid(row=8, column=1, sticky=tk.W, pady=(0, 10))

        # Add separator for video quality settings
        ttk.Separator(voice_grid, orient=tk.HORIZONTAL).grid(row=9, column=0, columnspan=2, sticky=tk.EW, pady=15)

        # Video Quality Settings
        ttk.Label(voice_grid, text="Video Quality:", style='FormField.TLabel').grid(row=10, column=0, sticky=tk.W, pady=10)
        self.video_quality_var = tk.StringVar(value="720p")
        self.video_quality_combo = ttk.Combobox(
            voice_grid,
            values=["720p", "1080p", "2K", "4K"],
            textvariable=self.video_quality_var,
            state="readonly",
            width=35
        )
        self.video_quality_combo.current(0)  # Default to 720p
        self.video_quality_combo.grid(row=10, column=1, sticky=tk.W, pady=10, padx=5)

        # Add helper text for video quality
        video_quality_help = ttk.Label(
            voice_grid,
            text="Higher quality requires more processing power",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        video_quality_help.grid(row=11, column=1, sticky=tk.W, pady=(0, 10))

        # Video Orientation
        ttk.Label(voice_grid, text="Video Orientation:", style='FormField.TLabel').grid(row=12, column=0, sticky=tk.W, pady=10)
        self.orientation_var = tk.StringVar(value="portrait")
        self.orientation_group = RadioButtonGroup(
            voice_grid,
            options=[("portrait", "Portrait (9:16)"), ("landscape", "Landscape (16:9)")],
            variable=self.orientation_var,
            default="portrait"
        )
        self.orientation_group.grid(row=12, column=1, sticky=tk.W, pady=10)

        # === SUBTITLE STYLING CARD ===
        subtitle_card = CardFrame(options_column, title="Subtitle Styling")
        subtitle_card.pack(fill=tk.X, pady=(0, 10))

        subtitle_grid = ttk.Frame(subtitle_card)
        subtitle_grid.grid(row=subtitle_card.content_row, column=0, sticky="nsew")

        # Configure grid
        subtitle_grid.columnconfigure(0, weight=1)  # Label column
        subtitle_grid.columnconfigure(1, weight=3)  # Input column

        # Font Color
        ttk.Label(subtitle_grid, text="Font Color:", style='FormField.TLabel').grid(row=0, column=0, sticky=tk.W, pady=10)
        self.font_color_picker = ColorPickerButton(
            subtitle_grid,
            initial_color=self.font_color,
            command=lambda color: setattr(self, 'font_color', color)
        )
        self.font_color_picker.grid(row=0, column=1, sticky=tk.W, pady=10)

        # Font Size
        ttk.Label(subtitle_grid, text="Font Size:", style='FormField.TLabel').grid(row=1, column=0, sticky=tk.W, pady=10)
        self.font_size_var = tk.IntVar(value=self.font_size)
        self.font_size_slider = SliderWithValue(
            subtitle_grid,
            from_=20,
            to=300,  # Increased maximum font size from 80 to 300
            initial=self.font_size,
            command=lambda val: setattr(self, 'font_size', int(val)),
            value_format='pixels'
        )
        self.font_size_slider.grid(row=1, column=1, sticky=tk.W, pady=10)

        # Outline Color
        ttk.Label(subtitle_grid, text="Outline Color:", style='FormField.TLabel').grid(row=2, column=0, sticky=tk.W, pady=10)
        self.outline_color_picker = ColorPickerButton(
            subtitle_grid,
            initial_color=self.outline_color,
            command=lambda color: setattr(self, 'outline_color', color)
        )
        self.outline_color_picker.grid(row=2, column=1, sticky=tk.W, pady=10)

        # Outline Size
        ttk.Label(subtitle_grid, text="Outline Size:", style='FormField.TLabel').grid(row=3, column=0, sticky=tk.W, pady=10)
        self.outline_size_var = tk.IntVar(value=self.outline_size)
        self.outline_size_slider = SliderWithValue(
            subtitle_grid,
            from_=0,
            to=8,
            initial=self.outline_size,
            command=lambda val: setattr(self, 'outline_size', int(val)),
            value_format='pixels'
        )
        self.outline_size_slider.grid(row=3, column=1, sticky=tk.W, pady=10)

        # Caption Position
        ttk.Label(subtitle_grid, text="Caption Position:", style='FormField.TLabel').grid(row=4, column=0, sticky=tk.W, pady=10)
        self.caption_position_var = tk.StringVar(value="bottom")
        self.caption_position_group = RadioButtonGroup(
            subtitle_grid,
            options=[("top", "Top"), ("center", "Center"), ("bottom", "Bottom")],
            variable=self.caption_position_var,
            default="bottom"
        )
        self.caption_position_group.grid(row=4, column=1, sticky=tk.W, pady=10)

        # Caption Grouping - NEW
        ttk.Label(subtitle_grid, text="Words Per Caption:", style='FormField.TLabel').grid(row=5, column=0, sticky=tk.W, pady=10)
        self.caption_words_var = tk.StringVar(value="3")
        self.caption_words_combo = ttk.Combobox(
            subtitle_grid,
            values=["1", "2", "3", "5", "10", "0"],
            textvariable=self.caption_words_var,
            state="readonly",
            width=35
        )
        self.caption_words_combo.current(2)  # Default to 3 words
        self.caption_words_combo.grid(row=5, column=1, sticky=tk.W, pady=10, padx=5)
        # Add tooltip or helper text
        caption_help = ttk.Label(
            subtitle_grid,
            text="Number of words to show at once (0 = full sentences)",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        caption_help.grid(row=6, column=1, sticky=tk.W, pady=(0, 10))

        # Word Highlighting
        ttk.Label(subtitle_grid, text="Word Highlighting:", style='FormField.TLabel').grid(row=7, column=0, sticky=tk.W, pady=10)
        self.highlight_words_var = tk.BooleanVar(value=True)
        self.highlight_words_check = ttk.Checkbutton(
            subtitle_grid,
            text="Enable word highlighting",
            variable=self.highlight_words_var,
            command=self.toggle_highlight_color
        )
        self.highlight_words_check.grid(row=7, column=1, sticky=tk.W, pady=10, padx=5)

        # Highlight Color
        ttk.Label(subtitle_grid, text="Highlight Color:", style='FormField.TLabel').grid(row=8, column=0, sticky=tk.W, pady=10)
        self.highlight_color = "#FFFF00"  # Default to yellow
        self.highlight_color_picker = ColorPickerButton(
            subtitle_grid,
            initial_color=self.highlight_color,
            command=lambda color: setattr(self, 'highlight_color', color)
        )
        self.highlight_color_picker.grid(row=8, column=1, sticky=tk.W, pady=10)

        # Highlight Style
        ttk.Label(subtitle_grid, text="Highlight Style:", style='FormField.TLabel').grid(row=9, column=0, sticky=tk.W, pady=10)
        self.highlight_style_var = tk.StringVar(value="text_color")
        self.highlight_style_group = RadioButtonGroup(
            subtitle_grid,
            options=[("text_color", "Text Color"), ("background", "Background")],
            variable=self.highlight_style_var,
            default="text_color"
        )
        self.highlight_style_group.grid(row=9, column=1, sticky=tk.W, pady=10)

        # Background Highlight Color
        ttk.Label(subtitle_grid, text="Background Color:", style='FormField.TLabel').grid(row=10, column=0, sticky=tk.W, pady=10)
        self.highlight_bg_color = "#3700B3"  # Default to purple
        self.highlight_bg_color_picker = ColorPickerButton(
            subtitle_grid,
            initial_color=self.highlight_bg_color,
            command=lambda color: setattr(self, 'highlight_bg_color', color)
        )
        self.highlight_bg_color_picker.grid(row=10, column=1, sticky=tk.W, pady=10)

        # Background Opacity
        ttk.Label(subtitle_grid, text="Background Opacity:", style='FormField.TLabel').grid(row=11, column=0, sticky=tk.W, pady=10)
        self.highlight_bg_opacity_var = tk.DoubleVar(value=0.7)
        self.highlight_bg_opacity_slider = SliderWithValue(
            subtitle_grid,
            from_=0.1,
            to=1.0,
            initial=0.7,
            command=lambda val: setattr(self, 'highlight_bg_opacity', float(val)),
            value_format='percent'
        )
        self.highlight_bg_opacity_slider.grid(row=11, column=1, sticky=tk.W, pady=10)

        # Event binding for TTS model change
        self.tts_model_combo.bind("<<ComboboxSelected>>", self.on_tts_model_change)

        # === RIGHT COLUMN (Status) ===
        status_column = ttk.Frame(top_frame)
        status_column.grid(row=0, column=1, sticky="nsew", padx=(8, 0))

        # === CUSTOM TITLE CARD (For AI-generated scripts) ===
        custom_title_card = CardFrame(status_column, title="Custom Title (Optional)")
        custom_title_card.pack(fill=tk.X, expand=True, pady=(0, 10))

        custom_title_frame = ttk.Frame(custom_title_card)
        custom_title_frame.grid(row=custom_title_card.content_row, column=0, sticky="nsew", padx=5, pady=5)

        # Add toggle for custom title
        self.use_custom_title_var = tk.BooleanVar(value=False)
        use_custom_title_check = ttk.Checkbutton(
            custom_title_frame,
            text="Use Custom Title for AI-Generated Script",
            variable=self.use_custom_title_var,
            command=self.toggle_custom_title
        )
        use_custom_title_check.pack(anchor=tk.W, pady=5)

        # Title input field
        ai_title_frame = ttk.Frame(custom_title_frame)
        ai_title_frame.pack(fill=tk.X, pady=5)

        ttk.Label(ai_title_frame, text="Enter Title:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))
        self.ai_title_entry = ttk.Entry(ai_title_frame, width=40, state=tk.DISABLED)
        self.ai_title_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add helper text
        title_help = ttk.Label(
            custom_title_frame,
            text="AI will generate a script based on this title",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        title_help.pack(anchor=tk.W, pady=(0, 10))

        # === CUSTOM SCRIPT CARD (Moved to right column) ===
        custom_script_card = CardFrame(status_column, title="Custom Script (Optional)")
        custom_script_card.pack(fill=tk.X, expand=True, pady=(0, 10))

        custom_script_frame = ttk.Frame(custom_script_card)
        custom_script_frame.grid(row=custom_script_card.content_row, column=0, sticky="nsew", padx=5, pady=5)

        # Add toggle for custom script
        self.use_custom_script_var = tk.BooleanVar(value=False)
        use_custom_script_check = ttk.Checkbutton(
            custom_script_frame,
            text="Use Custom Script Instead of AI Story Generator",
            variable=self.use_custom_script_var,
            command=self.toggle_custom_script
        )
        use_custom_script_check.pack(anchor=tk.W, pady=5)

        # Title for custom script
        title_frame = ttk.Frame(custom_script_frame)
        title_frame.pack(fill=tk.X, pady=5)

        ttk.Label(title_frame, text="Custom Title (Optional):", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))
        self.custom_title_entry = ttk.Entry(title_frame, width=40)
        self.custom_title_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Text area for script input
        script_label_frame = ttk.Frame(custom_script_frame)
        script_label_frame.pack(fill=tk.X, pady=(10, 5))

        ttk.Label(script_label_frame, text="Enter Your Script:", style='FormField.TLabel').pack(side=tk.LEFT)
        hint_label = ttk.Label(
            script_label_frame,
            text="Separate scenes with blank lines",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9)
        )
        hint_label.pack(side=tk.RIGHT)

        # Text editor for script with scrollbar
        script_editor_frame = ttk.Frame(custom_script_frame)
        script_editor_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        script_scrollbar = ttk.Scrollbar(script_editor_frame)
        script_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.script_editor = tk.Text(
            script_editor_frame,
            height=8,  # Reduced height to fit better in the right column
            bg=ModernUI.COLORS['input_bg'],
            fg=ModernUI.COLORS['text'],
            wrap=tk.WORD,
            yscrollcommand=script_scrollbar.set,
            relief=tk.FLAT,
            padx=10,
            pady=10,
            font=(DEFAULT_FONT, 10)
        )
        self.script_editor.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        script_scrollbar.config(command=self.script_editor.yview)

        # Set initial state to disabled
        self.script_editor.config(state=tk.DISABLED)
        self.custom_title_entry.config(state=tk.DISABLED)

        # Add sample script button
        sample_script_button = ModernButton(
            custom_script_frame,
            text="Load Sample Script",
            command=self.load_sample_script,
            type="outline",
            width=15
        )
        sample_script_button.pack(anchor=tk.E, pady=5)

        # === BATCH GENERATION CARD (Moved to right column) ===
        batch_card = CardFrame(status_column, title="Batch Generation")
        batch_card.pack(fill=tk.X, expand=True, pady=(0, 10))

        batch_frame = ttk.Frame(batch_card)
        batch_frame.grid(row=batch_card.content_row, column=0, sticky="nsew", padx=5, pady=5)

        # Add batch generation option
        self.batch_enabled_var = tk.BooleanVar(value=False)
        batch_check = ttk.Checkbutton(
            batch_frame,
            text="Enable Batch Generation",
            variable=self.batch_enabled_var,
            command=self.toggle_batch_generation
        )
        batch_check.pack(anchor=tk.W, pady=5)

        # Quantity selection frame
        quantity_frame = ttk.Frame(batch_frame)
        quantity_frame.pack(fill=tk.X, pady=5)

        ttk.Label(quantity_frame, text="Number of Videos:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 10))
        self.batch_quantity_var = tk.StringVar(value="5")
        self.batch_quantity_spinbox = ttk.Spinbox(
            quantity_frame,
            from_=1,
            to=20,
            textvariable=self.batch_quantity_var,
            width=5,
            state="disabled"
        )
        self.batch_quantity_spinbox.pack(side=tk.LEFT)

        # Add custom titles from file option
        self.batch_custom_titles_var = tk.BooleanVar(value=False)
        self.batch_custom_titles_check = ttk.Checkbutton(
            batch_frame,
            text="Use Custom Titles from File",
            variable=self.batch_custom_titles_var,
            command=self.toggle_batch_custom_titles,
            state="disabled"
        )
        self.batch_custom_titles_check.pack(anchor=tk.W, pady=5)

        # Custom titles file selection frame
        self.titles_file_frame = ttk.Frame(batch_frame)
        self.titles_file_frame.pack(fill=tk.X, pady=5)

        self.titles_file_var = tk.StringVar()
        self.titles_file_entry = ttk.Entry(
            self.titles_file_frame,
            textvariable=self.titles_file_var,
            width=25,
            state="disabled"
        )
        self.titles_file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.browse_titles_button = ttk.Button(
            self.titles_file_frame,
            text="Browse...",
            command=self.browse_titles_file,
            state="disabled"
        )
        self.browse_titles_button.pack(side=tk.RIGHT)

        # Add help text for titles file
        titles_help = ttk.Label(
            batch_frame,
            text="One title per line in text file. Number of titles should match batch quantity.",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9),
            wraplength=300
        )
        titles_help.pack(anchor=tk.W, pady=(0, 5))

        # Add sample titles button
        sample_titles_button = ttk.Button(
            batch_frame,
            text="Load Sample Titles",
            command=self.load_sample_titles,
            state="disabled"
        )
        sample_titles_button.pack(anchor=tk.W, pady=(0, 5))
        self.sample_titles_button = sample_titles_button

        # Add separator between title and script options
        ttk.Separator(batch_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        # Add custom scripts from folder option
        self.batch_custom_scripts_var = tk.BooleanVar(value=False)
        self.batch_custom_scripts_check = ttk.Checkbutton(
            batch_frame,
            text="Use Custom Scripts from Folder",
            variable=self.batch_custom_scripts_var,
            command=self.toggle_batch_custom_scripts,
            state="disabled"
        )
        self.batch_custom_scripts_check.pack(anchor=tk.W, pady=5)

        # Custom scripts folder selection frame
        self.scripts_folder_frame = ttk.Frame(batch_frame)
        self.scripts_folder_frame.pack(fill=tk.X, pady=5)

        self.scripts_folder_var = tk.StringVar()
        self.scripts_folder_entry = ttk.Entry(
            self.scripts_folder_frame,
            textvariable=self.scripts_folder_var,
            width=25,
            state="disabled"
        )
        self.scripts_folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.browse_scripts_folder_button = ttk.Button(
            self.scripts_folder_frame,
            text="Browse...",
            command=self.browse_scripts_folder,
            state="disabled"
        )
        self.browse_scripts_folder_button.pack(side=tk.RIGHT)

        # Add help text for scripts folder
        scripts_help = ttk.Label(
            batch_frame,
            text="Select a folder containing .txt files with custom scripts. Each file will be used to generate one video. Filenames will be used as titles.",
            foreground=ModernUI.COLORS['text_secondary'],
            font=(DEFAULT_FONT, 9),
            wraplength=300
        )
        scripts_help.pack(anchor=tk.W, pady=(0, 5))

        # Add sample scripts button
        sample_scripts_button = ttk.Button(
            batch_frame,
            text="Create Sample Scripts",
            command=self.create_sample_scripts_folder,
            state="disabled"
        )
        sample_scripts_button.pack(anchor=tk.W, pady=(0, 5))
        self.sample_scripts_button = sample_scripts_button

        # Generate Button Section (Moved to right column)
        generate_frame = ttk.Frame(status_column)
        generate_frame.pack(fill=tk.X, pady=(0, 15))

        # Generate button centered
        button_container = ttk.Frame(generate_frame)
        button_container.pack(side=tk.TOP, anchor=tk.CENTER)

        self.generate_button = ModernButton(
            button_container,
            text="GENERATE VIDEO",
            command=self.generate_video,
            type="accent",
            width=20
        )
        self.generate_button.pack(pady=5)

        # Control buttons container (for pause/stop)
        self.control_buttons_frame = ttk.Frame(generate_frame)
        self.control_buttons_frame.pack(side=tk.TOP, anchor=tk.CENTER, pady=5)

        # Pause button
        self.pause_button = ModernButton(
            self.control_buttons_frame,
            text="PAUSE",
            command=self.pause_generation,
            type="outline",
            width=10
        )
        self.pause_button.pack(side=tk.LEFT, padx=5)
        self.pause_button.config(state=tk.DISABLED)

        # Stop button
        self.stop_button = ModernButton(
            self.control_buttons_frame,
            text="STOP",
            command=self.stop_generation,
            type="outline",
            width=10
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        self.stop_button.config(state=tk.DISABLED)

        # Status Card
        status_card = CardFrame(status_column, title="Generation Status")
        status_card.pack(fill=tk.X, expand=True, pady=(0, 10))

        # Progress information
        progress_frame = ttk.Frame(status_card)
        progress_frame.grid(row=status_card.content_row, column=0, sticky="ew", padx=5, pady=5)

        # Progress Bar
        self.progress_var = tk.DoubleVar()
        ttk.Label(progress_frame, text="Progress:").pack(anchor=tk.W, pady=(10, 5))
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=300,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 15))

        # Status Message
        ttk.Label(progress_frame, text="Status:").pack(anchor=tk.W, pady=(5, 5))
        self.status_label = ttk.Label(
            progress_frame,
            text="Ready to generate video",
            wraplength=350,
            justify=tk.LEFT
        )
        self.status_label.pack(fill=tk.X, pady=(0, 10))

        # Console log for detailed progress
        ttk.Label(progress_frame, text="Console Log:").pack(anchor=tk.W, pady=(10, 5))

        log_frame = ttk.Frame(progress_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Scrollable text widget for logs
        log_scroll = ttk.Scrollbar(log_frame)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        self.log_console = tk.Text(
            log_frame,
            wrap=tk.WORD,
            height=10,
            width=45,
            bg=ModernUI.COLORS['input_bg'],
            fg=ModernUI.COLORS['text'],
            font=('Consolas', 9),
            relief=tk.FLAT,
            padx=10,
            pady=10,
            state=tk.DISABLED
        )
        self.log_console.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.config(command=self.log_console.yview)
        self.log_console.config(yscrollcommand=log_scroll.set)

        # Log initial message
        self.log_message("Application started, ready to generate videos")

        # Recent Video Card
        recent_card = CardFrame(status_column, title="Recent Video")
        recent_card.pack(fill=tk.X, expand=True, pady=(0, 0))

        recent_content = ttk.Frame(recent_card)
        recent_content.grid(row=recent_card.content_row, column=0, sticky="nsew", padx=5, pady=5)

        # Path display
        ttk.Label(recent_content, text="File:").pack(anchor=tk.W, pady=(10, 5))

        path_frame = ttk.Frame(recent_content)
        path_frame.pack(fill=tk.X, pady=(0, 15))

        self.recent_video_path = ttk.Label(
            path_frame,
            text="No videos generated yet",
            wraplength=350,
            foreground=ModernUI.COLORS['text_secondary']
        )
        self.recent_video_path.pack(side=tk.LEFT, fill=tk.X, pady=5)

        # Actions
        action_frame = ttk.Frame(recent_content)
        action_frame.pack(fill=tk.X, pady=(0, 10))

        self.open_video_button = ModernButton(
            action_frame,
            text="Open Video",
            command=self.open_recent_video,
            type="primary",
            state='disabled'
        )
        self.open_video_button.pack(side=tk.LEFT, padx=(0, 10))

        self.open_folder_button = ModernButton(
            action_frame,
            text="Open Folder",
            command=self.open_video_folder,
            type="outline",
            state='disabled'
        )
        self.open_folder_button.pack(side=tk.LEFT)

        # Recent video info
        self.recent_video_info = ttk.Label(
            recent_content,
            text="",
            wraplength=350,
            justify=tk.LEFT
        )
        self.recent_video_info.pack(fill=tk.X, pady=(5, 10))

        # Store most recent video path
        self.most_recent_video = None

        # Initialize the highlight controls state
        self.toggle_highlight_color()

    def _on_canvas_configure(self, event):
        """Update the scrollregion when the canvas is resized"""
        self.dash_canvas.configure(scrollregion=self.dash_canvas.bbox("all"))

        # Also adjust the canvas width to match the window
        canvas_width = event.width
        self.dash_canvas.itemconfig("self.dash_content", width=canvas_width)

    def _bind_mousewheel(self, canvas):
        """Bind mouse wheel events to a canvas for scrolling"""
        # Windows and macOS
        canvas.bind("<MouseWheel>", lambda event: self._on_mousewheel(event, canvas))
        # Linux
        canvas.bind("<Button-4>", lambda event: self._on_mousewheel(event, canvas, direction=1))
        canvas.bind("<Button-5>", lambda event: self._on_mousewheel(event, canvas, direction=-1))

        # Bind the same events to all children of the canvas
        self._bind_mousewheel_to_children(canvas, canvas)

    def _bind_mousewheel_to_children(self, widget, canvas):
        """Recursively bind mouse wheel events to all children of a widget"""
        # Bind to this widget
        widget.bind("<MouseWheel>", lambda event: self._on_mousewheel(event, canvas))
        widget.bind("<Button-4>", lambda event: self._on_mousewheel(event, canvas, direction=1))
        widget.bind("<Button-5>", lambda event: self._on_mousewheel(event, canvas, direction=-1))

        # Recursively bind to all children
        for child in widget.winfo_children():
            self._bind_mousewheel_to_children(child, canvas)

    def _on_mousewheel(self, event, canvas, direction=None):
        """Handle mouse wheel scrolling"""
        if direction is None:
            # Windows and macOS
            # For Windows, event.delta is negative when scrolling down
            # For macOS, event.delta is positive when scrolling down
            if platform.system() == "Darwin":  # macOS
                direction = -1 if event.delta > 0 else 1
            else:  # Windows
                direction = 1 if event.delta > 0 else -1

        # Scroll the canvas (adjust the amount as needed)
        canvas.yview_scroll(direction * -1, "units")

    def open_video_folder(self):
        """Open the folder containing the most recent video"""
        if self.most_recent_video and os.path.exists(self.most_recent_video):
            folder_path = os.path.dirname(self.most_recent_video)
            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", folder_path])
            else:  # Linux
                subprocess.Popen(["xdg-open", folder_path])

    def setup_settings(self):
        """Set up the settings page"""
        settings_page = self.pages["settings"]

        # Create a scrollable content area
        settings_canvas = tk.Canvas(settings_page, bg=ModernUI.COLORS['background'], highlightthickness=0)
        settings_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(settings_page, orient=tk.VERTICAL, command=settings_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure canvas
        settings_canvas.configure(yscrollcommand=scrollbar.set)
        settings_canvas.bind('<Configure>', lambda e: settings_canvas.configure(scrollregion=settings_canvas.bbox("all")))

        # Bind mouse wheel events for scrolling
        self._bind_mousewheel(settings_canvas)

        # Main content frame
        settings_content = ttk.Frame(settings_canvas)
        settings_canvas.create_window((0, 0), window=settings_content, anchor="nw")

        # Update mouse wheel bindings after content is created
        settings_content.bind("<Configure>", lambda e: self._bind_mousewheel_to_children(settings_content, settings_canvas))

        # Settings header
        header_frame = ttk.Frame(settings_content)
        header_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Label(header_frame, text="Application Settings", style='Header.TLabel').pack(side=tk.LEFT)
        ttk.Label(header_frame, text="Settings are managed through config.json", style='Subtitle.TLabel').pack(side=tk.RIGHT)

        # API Keys section
        api_card = CardFrame(settings_content, title="API Keys Configuration")
        api_card.pack(fill=tk.X, padx=20, pady=(0, 15))

        # API key info
        keys_frame = ttk.Frame(api_card)
        keys_frame.grid(row=api_card.content_row, column=0, sticky="nsew", padx=10, pady=10)

        # Configure grid
        keys_frame.columnconfigure(0, weight=1)  # Service name
        keys_frame.columnconfigure(1, weight=2)  # Status
        keys_frame.columnconfigure(2, weight=1)  # Action

        # OpenAI
        ttk.Label(keys_frame, text="OpenAI API:", style='FormField.TLabel').grid(row=0, column=0, sticky=tk.W, pady=10)
        openai_status = "Configured" if os.getenv("OPENAI_API_KEY") else "Not configured"
        openai_color = ModernUI.COLORS['success'] if os.getenv("OPENAI_API_KEY") else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=openai_status, foreground=openai_color).grid(row=0, column=1, sticky=tk.W, pady=10)

        # Replicate
        ttk.Label(keys_frame, text="Replicate API:", style='FormField.TLabel').grid(row=1, column=0, sticky=tk.W, pady=10)
        replicate_status = "Configured" if REPLICATE_API_KEY else "Not configured"
        replicate_color = ModernUI.COLORS['success'] if REPLICATE_API_KEY else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=replicate_status, foreground=replicate_color).grid(row=1, column=1, sticky=tk.W, pady=10)

        # FAL AI
        ttk.Label(keys_frame, text="FAL AI API:", style='FormField.TLabel').grid(row=2, column=0, sticky=tk.W, pady=10)
        fal_status = "Configured" if FAL_API_KEY else "Not configured"
        fal_color = ModernUI.COLORS['success'] if FAL_API_KEY else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=fal_status, foreground=fal_color).grid(row=2, column=1, sticky=tk.W, pady=10)

        # Together AI
        ttk.Label(keys_frame, text="Together AI API:", style='FormField.TLabel').grid(row=3, column=0, sticky=tk.W, pady=10)
        together_status = "Configured" if TOGETHER_API_KEY else "Not configured"
        together_color = ModernUI.COLORS['success'] if TOGETHER_API_KEY else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=together_status, foreground=together_color).grid(row=3, column=1, sticky=tk.W, pady=10)

        # Groq AI
        ttk.Label(keys_frame, text="Groq API:", style='FormField.TLabel').grid(row=4, column=0, sticky=tk.W, pady=10)
        groq_status = "Configured" if os.getenv("GROQ_API_KEY") else "Not configured"
        groq_color = ModernUI.COLORS['success'] if os.getenv("GROQ_API_KEY") else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=groq_status, foreground=groq_color).grid(row=4, column=1, sticky=tk.W, pady=10)

        # ElevenLabs
        ttk.Label(keys_frame, text="ElevenLabs API:", style='FormField.TLabel').grid(row=5, column=0, sticky=tk.W, pady=10)
        elevenlabs_status = "Configured" if os.getenv("ELEVENLABS_API_KEY") else "Not configured"
        elevenlabs_color = ModernUI.COLORS['success'] if os.getenv("ELEVENLABS_API_KEY") else ModernUI.COLORS['error']
        ttk.Label(keys_frame, text=elevenlabs_status, foreground=elevenlabs_color).grid(row=5, column=1, sticky=tk.W, pady=10)

        # Link to edit .env file
        env_note = ttk.Label(
            api_card,
            text="API keys are managed in the .env file in the root directory.",
            foreground=ModernUI.COLORS['text_secondary']
        )
        env_note.grid(row=api_card.content_row+1, column=0, sticky="w", padx=10, pady=(0, 10))

        # Open .env button
        open_env_button = ModernButton(
            api_card,
            text="Open .env File",
            command=self.open_env_file,
            type="primary"
        )
        open_env_button.grid(row=api_card.content_row+2, column=0, sticky="w", padx=10, pady=(0, 10))

        # Add ElevenLabs check button
        check_elevenlabs_button = ModernButton(
            api_card,
            text="Check ElevenLabs API",
            command=self.check_elevenlabs_api,
            type="outline"
        )
        check_elevenlabs_button.grid(row=api_card.content_row+2, column=0, sticky="e", padx=10, pady=(0, 10))



        # Configuration display
        config_card = CardFrame(settings_content, title="Current Configuration")
        config_card.pack(fill=tk.X, padx=20, pady=(0, 20), expand=True)

        # Configuration text with improved styling
        text_frame = ttk.Frame(config_card)
        text_frame.grid(row=config_card.content_row, column=0, sticky="nsew", padx=5, pady=5)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        # Scrollable text widget with better styling
        text_scroll = ttk.Scrollbar(text_frame)
        text_scroll.grid(row=0, column=1, sticky="ns")

        # Get current config with indentation for better readability
        config_text = json.dumps(config, indent=2)

        config_display = tk.Text(
            text_frame,
            wrap=tk.WORD,
            height=20,
            width=80,
            bg=ModernUI.COLORS['input_bg'],
            fg=ModernUI.COLORS['text'],
            font=('Consolas', 10),
            relief=tk.FLAT,
            padx=10,
            pady=10
        )
        config_display.insert(tk.END, config_text)
        config_display.configure(state='disabled')
        config_display.grid(row=0, column=0, sticky="nsew")

        text_scroll.config(command=config_display.yview)
        config_display.config(yscrollcommand=text_scroll.set)

        # Reload config button
        reload_config_button = ModernButton(
            config_card,
            text="Reload Configuration",
            command=self.reload_config,
            type="outline"
        )
        reload_config_button.grid(row=config_card.content_row+1, column=0, sticky="e", padx=10, pady=10)

        # Note: AI Provider settings have been moved to the main UI

    # AI Provider change handler moved to main UI

    def setup_script_generator(self):
        """Set up the script generator page with a two-column layout"""
        script_gen_page = self.pages["script_generator"]

        # Create a scrollable content area
        script_canvas = tk.Canvas(script_gen_page, bg=ModernUI.COLORS['background'], highlightthickness=0)
        script_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Add a scrollbar
        scrollbar = ttk.Scrollbar(script_gen_page, orient=tk.VERTICAL, command=script_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure the canvas
        script_canvas.configure(yscrollcommand=scrollbar.set)
        script_canvas.bind('<Configure>', lambda e: script_canvas.configure(scrollregion=script_canvas.bbox("all")))

        # Bind mouse wheel events for scrolling
        self._bind_mousewheel(script_canvas)

        # Create a frame inside the canvas
        script_content = ttk.Frame(script_canvas)
        script_canvas.create_window((0, 0), window=script_content, anchor="nw")

        # Update mouse wheel bindings after content is created
        script_content.bind("<Configure>", lambda e: self._bind_mousewheel_to_children(script_content, script_canvas))

        # Page header
        header_frame = ttk.Frame(script_content)
        header_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        # Page title
        page_title = ttk.Label(
            header_frame,
            text="Script Generator",
            style='Headline.TLabel'
        )
        page_title.pack(side=tk.LEFT)

        # Page description
        description_frame = ttk.Frame(script_content)
        description_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        description = ttk.Label(
            description_frame,
            text="Generate engaging scripts for your videos. Enter your concept, select a script type, and let AI create a compelling script.",
            style='Body.TLabel',
            wraplength=800
        )
        description.pack(anchor=tk.W)

        # Main content area with two columns
        main_frame = ttk.Frame(script_content)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Configure columns for the two-column layout
        main_frame.columnconfigure(0, weight=1)  # Options column
        main_frame.columnconfigure(1, weight=2)  # Script editor column

        # === LEFT COLUMN: OPTIONS ===
        options_column = ttk.Frame(main_frame)
        options_column.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        # Input section
        input_card = CardFrame(options_column, title="Script Options")
        input_card.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        input_grid = ttk.Frame(input_card)
        input_grid.grid(row=input_card.content_row, column=0, sticky="nsew", padx=15, pady=15)

        # Configure grid
        input_grid.columnconfigure(0, weight=1)
        input_grid.columnconfigure(1, weight=2)

        # Concept input
        ttk.Label(input_grid, text="Your Concept:", style='FormField.TLabel').grid(row=0, column=0, sticky=tk.W, pady=10)
        self.concept_var = tk.StringVar()
        self.concept_entry = ttk.Entry(
            input_grid,
            textvariable=self.concept_var,
            width=30
        )
        self.concept_entry.grid(row=0, column=1, sticky=tk.EW, pady=10, padx=5)

        # Script type selection
        ttk.Label(input_grid, text="Script Type:", style='FormField.TLabel').grid(row=1, column=0, sticky=tk.W, pady=10)
        self.script_type_var = tk.StringVar(value="Motivational")

        # Get all story types from utils.STORY_TYPES
        from utils import STORY_TYPES
        script_types = list(STORY_TYPES) + ["Custom"]

        self.script_type_combo = ttk.Combobox(
            input_grid,
            values=script_types,
            textvariable=self.script_type_var,
            state="readonly",
            width=20
        )
        self.script_type_combo.grid(row=1, column=1, sticky=tk.EW, pady=10, padx=5)

        # Bind event to show/hide custom options
        self.script_type_combo.bind("<<ComboboxSelected>>", self.toggle_custom_script_options)

        # Word count selection (always visible for all script types)
        ttk.Label(input_grid, text="Word Count:", style='FormField.TLabel').grid(row=2, column=0, sticky=tk.W, pady=10)

        word_count_frame = ttk.Frame(input_grid)
        word_count_frame.grid(row=2, column=1, sticky=tk.EW, pady=10, padx=5)

        self.word_count_var = tk.IntVar(value=150)
        self.word_count_scale = ttk.Scale(
            word_count_frame,
            from_=50,
            to=500,
            orient=tk.HORIZONTAL,
            variable=self.word_count_var,
            length=150
        )
        self.word_count_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Label to show current word count value
        self.word_count_label = ttk.Label(word_count_frame, text="150 words", style='Body.TLabel')
        self.word_count_label.pack(side=tk.LEFT, padx=10)

        # Add preset buttons for common word counts
        preset_frame = ttk.Frame(input_grid)
        preset_frame.grid(row=3, column=1, sticky=tk.EW, pady=(0, 10), padx=5)

        ttk.Label(preset_frame, text="Presets:", style='FormField.TLabel').pack(side=tk.LEFT, padx=(0, 5))

        for preset in [100, 150, 200, 300]:
            preset_btn = ttk.Button(
                preset_frame,
                text=str(preset),
                width=4,
                command=lambda p=preset: self.set_word_count_preset(p)
            )
            preset_btn.pack(side=tk.LEFT, padx=2)

        # Update label when scale is moved
        self.word_count_scale.bind("<Motion>", self.update_word_count_label)

        # Bind script type selection to update word count
        self.script_type_combo.bind("<<ComboboxSelected>>", self.on_script_type_change)

        # LLM Provider selection
        ttk.Label(input_grid, text="AI Provider:", style='FormField.TLabel').grid(row=4, column=0, sticky=tk.W, pady=10)

        llm_frame = ttk.Frame(input_grid)
        llm_frame.grid(row=4, column=1, sticky=tk.EW, pady=10, padx=5)

        # Use the shared StringVar for AI provider selection
        # This ensures both dashboard and script generator UIs stay in sync

        # Create radio buttons for each provider
        for i, provider in enumerate(AI_PROVIDERS):
            rb = ttk.Radiobutton(
                llm_frame,
                text=provider,
                variable=self.shared_llm_provider_var,
                value=provider,
                command=self.update_script_ai_status
            )
            rb.pack(side=tk.LEFT, padx=(0 if i == 0 else 15, 0))

        # Add a label to show the current AI provider status
        self.script_ai_provider_status = ttk.Label(llm_frame, text="Status: Groq Ready", foreground=ModernUI.COLORS['success'])
        self.script_ai_provider_status.pack(side=tk.LEFT, padx=10)

        # Generate button
        button_frame = ttk.Frame(input_grid)
        button_frame.grid(row=5, column=0, columnspan=2, sticky=tk.EW, pady=(20, 10))

        # Center the button
        button_container = ttk.Frame(button_frame)
        button_container.pack(side=tk.TOP, anchor=tk.CENTER)

        self.generate_script_button = ModernButton(
            button_container,
            text="GENERATE SCRIPT",
            command=self.generate_script,
            type="accent",
            width=20
        )
        self.generate_script_button.pack(pady=5)

        # Progress bar
        progress_frame = ttk.Frame(input_grid)
        progress_frame.grid(row=6, column=0, columnspan=2, sticky=tk.EW, pady=(10, 5))

        self.script_progress_var = tk.DoubleVar()
        self.script_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.script_progress_var,
            maximum=100
        )
        self.script_progress_bar.pack(fill=tk.X)

        # Status label
        self.script_status_label = ttk.Label(
            progress_frame,
            text="Ready to generate script",
            style='Status.TLabel'
        )
        self.script_status_label.pack(anchor=tk.W, pady=(5, 0))

        # === RIGHT COLUMN: SCRIPT EDITOR ===
        editor_column = ttk.Frame(main_frame)
        editor_column.grid(row=0, column=1, sticky="nsew")

        # Output section
        output_card = CardFrame(editor_column, title="Generated Script")
        output_card.pack(fill=tk.BOTH, expand=True)

        output_frame = ttk.Frame(output_card)
        output_frame.grid(row=output_card.content_row, column=0, sticky="nsew", padx=15, pady=15)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)

        # Create a frame for the script output with scrollbar
        script_frame = ttk.Frame(output_frame)
        script_frame.grid(row=0, column=0, sticky="nsew")
        script_frame.columnconfigure(0, weight=1)
        script_frame.rowconfigure(0, weight=1)

        # Add a scrollbar to the script frame
        script_scrollbar = ttk.Scrollbar(script_frame, orient=tk.VERTICAL)
        script_scrollbar.grid(row=0, column=1, sticky="ns")

        # Script output text area with improved scrolling
        self.script_output = tk.Text(
            script_frame,
            wrap=tk.WORD,
            font=(DEFAULT_FONT, 12),
            bg=ModernUI.COLORS['surface'],
            fg=ModernUI.COLORS['text'],
            padx=10,
            pady=10,
            relief=tk.FLAT,
            yscrollcommand=script_scrollbar.set
        )
        self.script_output.grid(row=0, column=0, sticky="nsew")
        script_scrollbar.config(command=self.script_output.yview)

        # Action buttons
        action_frame = ttk.Frame(output_frame)
        action_frame.grid(row=1, column=0, sticky="ew", pady=(15, 0))

        self.copy_button = ttk.Button(
            action_frame,
            text="Copy to Clipboard",
            style='Outlined.TButton',
            command=self.copy_script_to_clipboard
        )
        self.copy_button.pack(side=tk.LEFT, padx=(0, 10))

        self.regenerate_button = ttk.Button(
            action_frame,
            text="Regenerate",
            style='Outlined.TButton',
            command=self.regenerate_script
        )
        self.regenerate_button.pack(side=tk.LEFT, padx=(0, 10))

        self.download_button = ttk.Button(
            action_frame,
            text="Download as Text",
            style='Outlined.TButton',
            command=self.download_script
        )
        self.download_button.pack(side=tk.LEFT)

        # Use in Dashboard button with purple color
        self.use_in_dashboard_button = ModernButton(
            action_frame,
            text="Add to Dashboard",
            command=self.use_script_in_dashboard,
            type="primary"  # Use primary type which is purple
        )
        self.use_in_dashboard_button.pack(side=tk.RIGHT)

        # Disable buttons initially
        self.copy_button.state(['disabled'])
        self.regenerate_button.state(['disabled'])
        self.download_button.state(['disabled'])
        self.use_in_dashboard_button.config(state=tk.DISABLED)

        # Initialize script generator thread
        self.script_generator_thread = None

    def toggle_custom_script_options(self, event=None):
        """Show or hide custom script options based on script type selection"""
        # This method is kept for backward compatibility
        # Word count is now always visible for all script types
        pass

    def update_word_count_label(self, event=None):
        """Update the word count label when the scale is moved"""
        word_count = self.word_count_var.get()
        self.word_count_label.config(text=f"{word_count} words")

    def set_word_count_preset(self, preset_value):
        """Set the word count to a preset value"""
        self.word_count_var.set(preset_value)
        self.update_word_count_label()

    def on_script_type_change(self, event=None):
        """Update word count based on script type selection"""
        script_type = self.script_type_var.get()

        # Define default word counts for different script types
        default_word_counts = {
            "Motivational": 150,
            "Funny": 120,
            "Educational": 200,
            "Storytelling": 250,
            "Philosophy": 180,
            "Fun Facts": 150,
            "Custom": 150  # Default for custom
        }

        # Set the word count based on script type
        if script_type in default_word_counts:
            self.word_count_var.set(default_word_counts[script_type])
        else:
            # Default for any other script type
            self.word_count_var.set(150)

        # Update the label
        self.update_word_count_label()

    def update_script_ai_status(self, event=None):
        """Update the AI provider status label"""
        selected_provider = self.shared_llm_provider_var.get()

        # Check if the provider is available
        if selected_provider == "OpenAI" and ai_client.openai_client is None:
            self.script_ai_provider_status.config(text="Status: Not Available", foreground=ModernUI.COLORS['error'])
            messagebox.showwarning("API Key Missing", "OpenAI API key is not set. Please add it in the Settings page.")
            self.shared_llm_provider_var.set("Groq")  # Reset to Groq

            # Also update the dashboard status if it exists
            if hasattr(self, 'ai_provider_status'):
                self.ai_provider_status.config(text="Status: Not Available", foreground=ModernUI.COLORS['error'])
        elif selected_provider == "Groq" and ai_client.groq_client is None:
            self.script_ai_provider_status.config(text="Status: Not Available", foreground=ModernUI.COLORS['error'])
            messagebox.showwarning("API Key Missing", "Groq API key is not set. Please add it in the Settings page.")
            self.shared_llm_provider_var.set("OpenAI")  # Reset to OpenAI if available

            # Also update the dashboard status if it exists
            if hasattr(self, 'ai_provider_status'):
                self.ai_provider_status.config(text="Status: Not Available", foreground=ModernUI.COLORS['error'])
        else:
            # Try to switch to the selected provider
            success, message = switch_ai_provider(selected_provider)

            if success:
                self.script_ai_provider_status.config(text=f"Status: {selected_provider} Ready", foreground=ModernUI.COLORS['success'])

                # Also update the dashboard status if it exists
                if hasattr(self, 'ai_provider_status'):
                    self.ai_provider_status.config(text=f"Status: {selected_provider} Ready", foreground=ModernUI.COLORS['success'])
            else:
                # Get the current provider after the failed switch attempt
                current_provider = ai_client.get_current_provider().lower()
                provider_name = "OpenAI" if current_provider == "openai" else "Groq"

                # Update UI to reflect the actual provider being used
                self.script_ai_provider_status.config(text=f"Status: Using {provider_name}", foreground=ModernUI.COLORS['warning'])

                # Set the radio button to match the actual provider
                self.shared_llm_provider_var.set(provider_name)

                # Also update the dashboard status if it exists
                if hasattr(self, 'ai_provider_status'):
                    self.ai_provider_status.config(text=f"Status: Using {provider_name}", foreground=ModernUI.COLORS['warning'])

    def generate_script(self):
        """Generate a script based on user input"""
        try:
            # Add logging for debugging
            import logging
            logging.info("Starting script generation")

            # Get user input
            concept = self.concept_var.get().strip()
            script_type = self.script_type_var.get()
            llm_provider = self.shared_llm_provider_var.get()
            word_count = self.word_count_var.get()

            logging.info(f"Inputs - Concept: {concept}, Type: {script_type}, Provider: {llm_provider}, Word Count: {word_count}")

            # Validate input
            if not concept:
                messagebox.showwarning("Input Required", "Please enter your concept before generating a script.")
                return

            # Disable the generate button while processing
            self.generate_script_button.config(state=tk.DISABLED)
            self.script_progress_var.set(0)

            # Clear previous output
            self.script_output.delete(1.0, tk.END)
            self.script_output.insert(tk.END, "Initializing script generation...")

            # Disable action buttons
            self.copy_button.state(['disabled'])
            self.regenerate_button.state(['disabled'])
            self.download_button.state(['disabled'])
            self.use_in_dashboard_button.config(state=tk.DISABLED)

            # Check if API keys are available
            if llm_provider == "OpenAI" and not os.getenv("OPENAI_API_KEY"):
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, "Error: OpenAI API key not found. Please add your API key in the Settings page.")
                self.generate_script_button.config(state=tk.NORMAL)
                return

            if llm_provider == "Groq" and not os.getenv("GROQ_API_KEY"):
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, "Error: Groq API key not found. Please add your API key in the Settings page.")
                self.generate_script_button.config(state=tk.NORMAL)
                return

            # Create and start the generator thread
            from script_generator import generate_script as gen_script
            self.script_generator_thread = gen_script(
                concept,
                script_type,
                self.update_script_status,
                self.update_script_progress,
                llm_provider,
                word_count
            )

            logging.info("Script generator thread started")

        except Exception as e:
            # Handle any unexpected errors
            import traceback
            traceback.print_exc()
            logging.error(f"Error starting script generation: {str(e)}")
            self.script_output.delete(1.0, tk.END)
            self.script_output.insert(tk.END, f"Error starting script generation: {str(e)}\n\nPlease try again.")
            self.generate_script_button.config(state=tk.NORMAL)

    def update_script_status(self, message):
        """Update the script generator status label"""
        self.script_status_label.config(text=message)

    def update_script_progress(self, value):
        """Update the script generator progress bar"""
        self.script_progress_var.set(value)

        # If progress is complete, check for results after a short delay
        # This ensures the thread has time to set the result
        if value >= 100:
            import logging
            logging.info("Script generation progress at 100%, scheduling result check")

            # Use after() to add a small delay before checking results
            # This gives the thread time to set the result
            self.root.after(100, self.check_script_results)

    def check_script_results(self):
        """Check the results of the script generation thread"""
        try:
            # Add logging for debugging
            import logging
            logging.info("Checking script generation results")

            if not hasattr(self, 'script_generator_thread') or self.script_generator_thread is None:
                logging.error("Script generator thread is None")
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, "Error: Script generation thread not found.\n\nPlease try again.")
                self.generate_script_button.config(state=tk.NORMAL)
                return

            if not hasattr(self.script_generator_thread, 'result'):
                logging.error("Script generator thread has no result attribute")
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, "Error: Script generation is still in progress or failed.\n\nPlease try again.")
                self.generate_script_button.config(state=tk.NORMAL)
                return

            result = self.script_generator_thread.result
            logging.info(f"Script result: {result}")

            if result is None:
                # Handle the case when result is None
                logging.error("Script result is None")
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, "Error: Script generation failed with no result.\n\nPlease try again with a different AI provider.")

                # Suggest trying a different provider
                current_provider = self.shared_llm_provider_var.get()
                alternative_provider = "OpenAI" if current_provider == "Groq" else "Groq"
                self.script_output.insert(tk.END, f"\n\nTry switching to {alternative_provider} in the AI Provider selection.")
            elif result.get('success'):
                # Display the generated script
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, result.get('script', ''))

                # Enable action buttons
                self.copy_button.state(['!disabled'])
                self.regenerate_button.state(['!disabled'])
                self.download_button.state(['!disabled'])
                self.use_in_dashboard_button.config(state=tk.NORMAL)
            else:
                # Show error message
                error_message = result.get('message', 'An unknown error occurred')
                self.script_output.delete(1.0, tk.END)
                self.script_output.insert(tk.END, f"Error: {error_message}\n\nPlease try again.")

                # Suggest trying a different provider if it's an API error
                if "API" in error_message or "key" in error_message.lower() or "provider" in error_message.lower():
                    current_provider = self.shared_llm_provider_var.get()
                    alternative_provider = "OpenAI" if current_provider == "Groq" else "Groq"
                    self.script_output.insert(tk.END, f"\n\nTry switching to {alternative_provider} in the AI Provider selection.")
        except Exception as e:
            # Handle any unexpected errors in the checking process
            import traceback
            traceback.print_exc()
            logging.error(f"Error checking script results: {str(e)}")
            self.script_output.delete(1.0, tk.END)
            self.script_output.insert(tk.END, f"Error checking script results: {str(e)}\n\nPlease try again.")
        finally:
            # Always re-enable the generate button
            self.generate_script_button.config(state=tk.NORMAL)

    def copy_script_to_clipboard(self):
        """Copy the generated script to clipboard"""
        script_text = self.script_output.get(1.0, tk.END).strip()
        if script_text:
            self.root.clipboard_clear()
            self.root.clipboard_append(script_text)
            self.update_script_status("Script copied to clipboard!")

    def regenerate_script(self):
        """Regenerate the script with the same parameters"""
        self.generate_script()

    def download_script(self):
        """Download the generated script as a text file"""
        script_text = self.script_output.get(1.0, tk.END).strip()
        if not script_text:
            return

        # Create a safe filename from the concept
        concept = self.concept_var.get().strip()
        safe_concept = re.sub(r'[^\w\s-]', '', concept).strip().replace(' ', '_')
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"script_{safe_concept}_{timestamp}.txt"

        # Ask user for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialfile=default_filename
        )

        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(script_text)
                self.update_script_status(f"Script saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Save Error", f"Could not save the script: {str(e)}")

    def use_script_in_dashboard(self):
        """Use the generated script in the dashboard for video creation"""
        script_text = self.script_output.get(1.0, tk.END).strip()
        if not script_text:
            return

        # Switch to dashboard
        self.show_page("dashboard")

        # Enable custom script option in dashboard
        if hasattr(self, 'use_custom_script_var'):
            self.use_custom_script_var.set(True)
            self.toggle_custom_script()  # This will enable the script editor

            # Set the custom script in the dashboard
            if hasattr(self, 'script_editor'):
                self.script_editor.delete(1.0, tk.END)
                self.script_editor.insert(tk.END, script_text)

                # Set a default title based on the concept
                if hasattr(self, 'custom_title_entry') and hasattr(self, 'concept_var'):
                    concept = self.concept_var.get().strip()
                    if concept:
                        self.custom_title_entry.delete(0, tk.END)
                        self.custom_title_entry.insert(0, concept)

                # Show a message to the user
                messagebox.showinfo("Script Added to Dashboard", "The script has been added to the Dashboard. You can now customize and generate a video with this script.")
            else:
                messagebox.showwarning("Feature Not Available", "The custom script feature is not available in the current version of the Dashboard.")
        else:
            messagebox.showwarning("Feature Not Available", "The custom script feature is not available in the current version of the Dashboard.")

    def setup_history(self):
        """Set up the history page"""
        history_page = self.pages["history"]

        # History header
        header_frame = ttk.Frame(history_page)
        header_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Label(header_frame, text="Video History", style='Header.TLabel').pack(side=tk.LEFT)

        # Refresh button
        self.refresh_history_button = ModernButton(
            header_frame,
            text="🔄 Refresh",
            command=self.refresh_history,
            type="outline"
        )
        self.refresh_history_button.pack(side=tk.RIGHT)

        # History list card
        history_card = CardFrame(history_page, title="Generated Videos")
        history_card.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # List container
        list_container = ttk.Frame(history_card)
        list_container.grid(row=history_card.content_row, column=0, sticky="nsew", padx=5, pady=5)
        list_container.columnconfigure(0, weight=1)
        list_container.rowconfigure(0, weight=1)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.grid(row=0, column=1, sticky="ns")

        # History list with modern styling
        self.history_list = tk.Listbox(
            list_container,
            bg=ModernUI.COLORS['input_bg'],
            fg=ModernUI.COLORS['text'],
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            borderwidth=0,
            selectbackground=ModernUI.COLORS['primary'],
            selectforeground='white',
            activestyle='none',
            highlightthickness=0,
            yscrollcommand=scrollbar.set
        )
        self.history_list.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        scrollbar.config(command=self.history_list.yview)

        # Add double-click binding for quick opening
        self.history_list.bind("<Double-1>", lambda e: self.open_selected_video())

        # Add mouse wheel binding for the history list
        self.history_list.bind("<MouseWheel>", lambda e: self.history_list.yview_scroll(int(-1*(e.delta/120)), "units"))
        self.history_list.bind("<Button-4>", lambda e: self.history_list.yview_scroll(-1, "units"))
        self.history_list.bind("<Button-5>", lambda e: self.history_list.yview_scroll(1, "units"))

        # Actions frame
        action_frame = ttk.Frame(history_card)
        action_frame.grid(row=history_card.content_row+1, column=0, sticky="e", padx=10, pady=10)

        # Open selected video button
        self.open_selected_button = ModernButton(
            action_frame,
            text="Open Selected Video",
            command=self.open_selected_video,
            type="primary"
        )
        self.open_selected_button.pack(side=tk.RIGHT, padx=(0, 10))

        # Open folder button
        self.open_folder_selected_button = ModernButton(
            action_frame,
            text="Open Containing Folder",
            command=self.open_selected_folder,
            type="outline"
        )
        self.open_folder_selected_button.pack(side=tk.RIGHT)

        # Initial refresh
        self.refresh_history()

    def get_end_pause_duration(self):
        """Get the end pause duration from the slider"""
        try:
            return self.end_pause_duration_slider.get()
        except:
            # Return default value if slider not available
            return 3.5

    def on_tts_model_change(self, event):
        """Update voice options when TTS model changes"""
        model = self.tts_model_combo.get()

        # Show or hide the locale filter based on the selected TTS model
        if hasattr(self, 'locale_filter_frame'):
            if model == "Voicely":
                # Show locale filter for Voicely/Edge TTS
                self.locale_filter_frame.pack(fill=tk.X, pady=(0, 5))

                # Load voices from JSON and filter them
                self.filter_voices()
            else:
                # Hide locale filter for other TTS models
                self.locale_filter_frame.pack_forget()

                # Set OpenAI voices
                if model == "OpenAI":
                    self.voice_combo.configure(values=[
                        "alloy", "echo", "fable", "onyx", "nova", "shimmer"
                    ])
                    self.voice_combo.current(0)
                elif model == "ElevenLabs":
                    # Try to get ElevenLabs voices
                    try:
                        from elevenlabs_client import elevenlabs_client

                        # Show loading message in the dropdown
                        self.voice_combo.configure(values=["Loading ElevenLabs voices..."])
                        self.voice_combo.current(0)
                        self.root.update()  # Force UI update to show loading message

                        # Check if API key is available
                        if not elevenlabs_client.api_key:
                            # ElevenLabs API key not set in environment
                            self.voice_combo.configure(values=["ElevenLabs API key not set"])
                            self.voice_combo.current(0)
                            messagebox.showwarning("ElevenLabs API Key", "ElevenLabs API key not set. Please add your API key to the .env file.")
                            return

                        # Get voices from ElevenLabs (this will use direct API call if needed)
                        voices = elevenlabs_client.get_voices()

                        if voices:
                            # Create a list of voice IDs and names for display
                            voice_options = []
                            self.voice_display_to_id = {}  # Map display names to voice IDs

                            for voice in voices:
                                display_name = f"{voice['name']} ({voice['category']})"
                                voice_id = voice['voice_id']
                                voice_options.append(display_name)
                                self.voice_display_to_id[display_name] = voice_id

                            # Update the voice combo with ElevenLabs voices
                            self.voice_combo.configure(values=voice_options)
                            self.voice_combo.current(0)
                            self.log_message(f"Loaded {len(voices)} ElevenLabs voices")
                        else:
                            # No voices found, show a message
                            self.voice_combo.configure(values=["No ElevenLabs voices found"])
                            self.voice_combo.current(0)
                            messagebox.showwarning("ElevenLabs Voices", "No voices found. Please check your ElevenLabs API key.")
                    except Exception as e:
                        # Error getting ElevenLabs voices
                        self.voice_combo.configure(values=["Error loading ElevenLabs voices"])
                        self.voice_combo.current(0)
                        error_message = f"Error loading ElevenLabs voices: {str(e)}"
                        self.log_message(error_message)
                        messagebox.showerror("ElevenLabs Error", error_message)
        else:
            # Fallback if locale filter frame doesn't exist
            if model == "OpenAI":
                self.voice_combo.configure(values=[
                    "alloy", "echo", "fable", "onyx", "nova", "shimmer"
                ])
                self.voice_combo.current(0)
            elif model == "Voicely":  # Voicely TTS
                # Popular English voices from Voicely TTS
                self.voice_combo.configure(values=[
                    # US English - popular voices
                    "en-US-AriaNeural",    # Microsoft Aria (Female)
                    "en-US-JennyNeural",   # Microsoft Jenny (Female)
                    "en-US-GuyNeural",     # Microsoft Guy (Male)
                    "en-US-AndrewNeural",  # Microsoft Andrew (Male)

                    # UK English
                    "en-GB-SoniaNeural",   # Microsoft Sonia (Female)
                    "en-GB-RyanNeural",    # Microsoft Ryan (Male)

                    # Other popular English voices
                    "en-AU-NatashaNeural", # Australian English (Female)
                    "en-CA-ClaraNeural",   # Canadian English (Female)
                    "en-IN-NeerjaNeural",  # Indian English (Female)
                    "en-US-MichelleNeural", # US English (Female)
                    "en-US-BrianNeural",   # US English (Male)
                    "en-US-EmmaNeural",    # US English (Female)
                    "en-US-EricNeural",    # US English (Male)
                ])
                self.voice_combo.current(0)
            elif model == "ElevenLabs":
                # Try to get ElevenLabs voices
                try:
                    from elevenlabs_client import elevenlabs_client

                    # Show loading message in the dropdown
                    self.voice_combo.configure(values=["Loading ElevenLabs voices..."])
                    self.voice_combo.current(0)
                    self.root.update()  # Force UI update to show loading message

                    # Check if API key is available
                    if not elevenlabs_client.api_key:
                        # ElevenLabs API key not set in environment
                        self.voice_combo.configure(values=["ElevenLabs API key not set"])
                        self.voice_combo.current(0)
                        messagebox.showwarning("ElevenLabs API Key", "ElevenLabs API key not set. Please add your API key to the .env file.")
                        return

                    # Get voices from ElevenLabs (this will use direct API call if needed)
                    voices = elevenlabs_client.get_voices()

                    if voices:
                        # Create a list of voice IDs and names for display
                        voice_options = []
                        self.voice_display_to_id = {}  # Map display names to voice IDs

                        for voice in voices:
                            display_name = f"{voice['name']} ({voice['category']})"
                            voice_id = voice['voice_id']
                            voice_options.append(display_name)
                            self.voice_display_to_id[display_name] = voice_id

                        # Update the voice combo with ElevenLabs voices
                        self.voice_combo.configure(values=voice_options)
                        self.voice_combo.current(0)
                        self.log_message(f"Loaded {len(voices)} ElevenLabs voices")
                    else:
                        # No voices found, show a message
                        self.voice_combo.configure(values=["No ElevenLabs voices found"])
                        self.voice_combo.current(0)
                        messagebox.showwarning("ElevenLabs Voices", "No voices found. Please check your ElevenLabs API key.")
                except Exception as e:
                    # Error getting ElevenLabs voices
                    self.voice_combo.configure(values=["Error loading ElevenLabs voices"])
                    self.voice_combo.current(0)
                    error_message = f"Error loading ElevenLabs voices: {str(e)}"
                    self.log_message(error_message)
                    messagebox.showerror("ElevenLabs Error", error_message)
            else:
                self.voice_combo.current(0)

    def update_status(self, message):
        """Update the status message"""
        self.status_label.config(text=message)
        # Also log the message to console
        self.log_message(message)
        self.root.update()

    def update_progress(self, value):
        """Update the progress bar"""
        self.progress_var.set(value)
        self.root.update()

    def generate_video(self):
        # Check if other processes are running
        if self.is_processing:
            messagebox.showinfo("Process Running", "Another process is already running.")
            return

        # Get values from UI
        story_type = self.story_combo.get()
        image_style = self.style_combo.get()
        custom_script_enabled = self.use_custom_script_var.get()
        custom_script = self.script_editor.get("1.0", tk.END) if custom_script_enabled else None
        custom_title = self.custom_title_entry.get() if custom_script_enabled else None

        # Validate custom script if enabled
        if custom_script_enabled:
            if not custom_script or len(custom_script.strip()) < 10:
                messagebox.showerror("Error", "Please enter a valid script with at least 10 characters.")
                return

        # Get TTS and image model values
        tts_model = self.tts_model_combo.get()
        image_model = self.image_model_combo.get()

        # Get the selected voice display name and convert it to the actual voice ID
        voice_display = self.voice_combo.get()
        if hasattr(self, 'voice_display_to_id') and voice_display in self.voice_display_to_id:
            voice_name = self.voice_display_to_id[voice_display]
        else:
            # Fallback to the display name if mapping not found
            voice_name = voice_display

        # Get AI Provider (LLM) value
        llm_provider = self.shared_llm_provider_var.get()

        # Caption styling
        font_name = self.font_combo.get()
        font_color = self.font_color
        font_size = self.font_size
        outline_color = self.outline_color
        outline_size = self.outline_size
        caption_position = self.caption_position_var.get()

        # Background music
        bg_music_selection = self.bg_music_combo.get()
        bg_music_path = None
        if bg_music_selection and bg_music_selection != "None":
            # Get the music file path directly
            music_dir = os.path.join(os.path.dirname(script_dir), "music")
            bg_music_path = os.path.join(music_dir, f"{bg_music_selection}.mp3")

            # Verify the file exists
            if not os.path.exists(bg_music_path):
                print(f"Warning: Music file not found: {bg_music_path}")
                bg_music_path = None

        # Get background music volume
        bg_music_volume = self.bg_music_volume_slider.get()

        # Get end pause duration
        end_pause_duration = self.get_end_pause_duration()

        # Get caption words as int from the dropdown
        caption_words = int(self.caption_words_var.get())
        # Update the instance variable for consistency
        self.caption_words = caption_words

        # Get word highlighting options
        highlight_words = self.highlight_words_var.get()
        highlight_color = self.highlight_color
        highlight_style = self.highlight_style_var.get()
        highlight_bg_color = self.highlight_bg_color
        highlight_bg_opacity = self.highlight_bg_opacity_slider.get()

        # Get video quality and orientation
        video_quality = self.video_quality_var.get()
        orientation = self.orientation_var.get()

        # Reset progress bar
        self.progress_var.set(0)

        # Check if batch generation is enabled
        is_batch = self.batch_enabled_var.get()

        # If batch generation is enabled, get the quantity and check for custom titles
        if is_batch:
            try:
                quantity = int(self.batch_quantity_var.get())
                if quantity < 1 or quantity > 50:
                    messagebox.showerror("Error", "Batch quantity must be between 1 and 50")
                    return

                # Initialize batch variables
                self.batch_current = 1
                self.batch_total = quantity

                # Check if custom titles from file is enabled
                self.batch_titles = None
                self.batch_scripts = None

                if self.batch_custom_titles_var.get():
                    titles_file = self.titles_file_var.get()
                    if not titles_file or not os.path.exists(titles_file):
                        messagebox.showerror("Error", "Please select a valid titles file")
                        return

                    try:
                        with open(titles_file, 'r', encoding='utf-8') as f:
                            self.batch_titles = [line.strip() for line in f.readlines() if line.strip()]

                        if len(self.batch_titles) < quantity:
                            response = messagebox.askquestion(
                                "Title Count Mismatch",
                                f"The file contains only {len(self.batch_titles)} titles, but you've set {quantity} videos to generate.\n\n"
                                f"Do you want to continue? (Missing titles will be AI-generated)",
                                icon='warning'
                            )
                            if response != 'yes':
                                return
                    except Exception as e:
                        messagebox.showerror("Error Reading File", f"Could not read the titles file: {str(e)}")
                        return

                # Check if custom scripts from folder is enabled
                elif self.batch_custom_scripts_var.get():
                    scripts_folder = self.scripts_folder_var.get()
                    if not scripts_folder or not os.path.exists(scripts_folder) or not os.path.isdir(scripts_folder):
                        messagebox.showerror("Error", "Please select a valid folder with script files")
                        return

                    try:
                        # Find all .txt files in the folder
                        script_files = []
                        for file in os.listdir(scripts_folder):
                            if file.lower().endswith('.txt'):
                                script_files.append(file)

                        if not script_files:
                            messagebox.showerror("Error", "No script files (.txt) found in the selected folder")
                            return

                        # Store script file paths and titles (filenames without extension)
                        self.batch_scripts = []
                        for file in script_files:
                            file_path = os.path.join(scripts_folder, file)
                            # Use filename without extension as title
                            title = os.path.splitext(file)[0]

                            # Read script content
                            with open(file_path, 'r', encoding='utf-8') as f:
                                script_content = f.read().strip()

                            if not script_content:
                                self.log_message(f"Warning: Empty script file: {file}")
                                continue

                            self.batch_scripts.append({
                                'title': title,
                                'script': script_content,
                                'file': file
                            })

                        if not self.batch_scripts:
                            messagebox.showerror("Error", "No valid script files found in the selected folder")
                            return

                        # Update quantity to match the number of script files
                        quantity = len(self.batch_scripts)
                        self.batch_total = quantity
                        self.batch_quantity_var.set(str(quantity))

                        # Force custom script mode since we're using custom scripts
                        self.use_custom_script_var.set(True)
                        self.toggle_custom_script()

                    except Exception as e:
                        messagebox.showerror("Error Reading Scripts", f"Could not read script files: {str(e)}")
                        return

                self.update_status(f"Starting batch generation: video 1 of {quantity}")
            except ValueError:
                messagebox.showerror("Error", "Invalid batch quantity")
                return
        else:
            self.update_status("Generating video...")
            self.batch_titles = None

        # Set processing flag and disable generate button
        self.is_processing = True
        self.generate_button.config(state=tk.DISABLED)

        # Enable pause and stop buttons
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)

        # Handle custom titles or scripts from batch for the first video
        ai_custom_title = None

        # Check if we're using custom scripts from folder
        if self.batch_scripts and self.batch_enabled_var.get() and self.batch_current <= len(self.batch_scripts):
            # Get the script and title for the current batch index (first video)
            batch_script = self.batch_scripts[self.batch_current - 1]

            # Use the script content and title from the file
            custom_script = batch_script['script']
            custom_title = batch_script['title']

            # Force custom script mode
            custom_script_enabled = True
            self.use_custom_script_var.set(True)
            self.toggle_custom_script()

            # Set the script in the editor for visibility
            self.script_editor.delete(1.0, tk.END)
            self.script_editor.insert(tk.END, custom_script)

            # Set the title in the entry for visibility
            self.custom_title_entry.delete(0, tk.END)
            self.custom_title_entry.insert(0, custom_title)

            self.update_status(f"Using script from file: {batch_script['file']}")

        # Check if we're using custom titles from file
        elif self.batch_titles and self.batch_enabled_var.get() and self.batch_current <= len(self.batch_titles):
            # Get the title for the current batch index
            batch_title = self.batch_titles[self.batch_current - 1]

            if custom_script_enabled:
                # Use as custom title for custom script
                custom_title = batch_title
                self.update_status(f"Using custom title from file: {batch_title}")
            else:
                # Use as AI custom title
                ai_custom_title = batch_title
                self.update_status(f"Using AI title from file: {batch_title}")
        elif self.use_custom_title_var.get() and not custom_script_enabled:
            # Use the UI custom title if no batch titles or scripts
            ai_custom_title = self.ai_title_entry.get().strip()

        # Create and start the video generation thread
        self.generator_thread = VideoGeneratorThread(
            story_type=story_type,
            image_style=image_style,
            tts_model=tts_model,
            image_model=image_model,
            voice_name=voice_name,
            font_name=font_name,
            font_color=font_color,
            font_size=int(font_size),
            outline_color=outline_color,
            outline_size=int(outline_size),
            caption_position=caption_position,
            caption_words=caption_words,
            callback=self.update_status,
            update_progress=self.update_progress,
            highlight_words=highlight_words,
            highlight_color=highlight_color,
            custom_script=custom_script,
            custom_title=custom_title,
            bg_music_path=bg_music_path,
            bg_music_volume=bg_music_volume,
            llm_provider=llm_provider,
            ai_custom_title=ai_custom_title,
            end_pause_duration=end_pause_duration,
            orientation=orientation,
            video_quality=video_quality,
            highlight_style=highlight_style,
            highlight_bg_color=highlight_bg_color,
            highlight_bg_opacity=highlight_bg_opacity
        )
        self.generator_thread.start()

        # Start checking thread status based on batch or single mode
        if is_batch:
            self.root.after(500, self.check_batch_thread)
        else:
            self.root.after(500, self.check_thread)

    def pause_generation(self):
        """Pause the video generation process"""
        if self.generator_thread and self.generator_thread.is_alive():
            if not self.generator_thread.paused:
                # Pause the thread
                self.generator_thread.pause()
                self.pause_button.config(text="RESUME")
                self.update_status("Video generation paused")
            else:
                # Resume the thread
                self.generator_thread.resume()
                self.pause_button.config(text="PAUSE")
                self.update_status("Video generation resumed")

    def stop_generation(self):
        """Stop the video generation process"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Request the thread to stop
            self.update_status("Stopping video generation...")
            self.generator_thread.stop()

            # Disable pause button
            self.pause_button.config(state=tk.DISABLED)

            # Wait for the thread to finish in check_thread

    def check_thread(self):
        """Check if the video generation thread has completed"""
        if self.generator_thread and not self.generator_thread.is_alive():
            result = self.generator_thread.result
            if result["success"]:
                # Ensure progress bar is at 100%
                self.progress_var.set(100)
                self.update_status("Video generation completed!")
                self.most_recent_video = result["video_path"]
                self.recent_video_path.config(text=self.most_recent_video)
                self.open_video_button.config(state=tk.NORMAL)
                self.open_folder_button.config(state=tk.NORMAL)
                self.refresh_history()

                # Add video details
                created_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.recent_video_info.config(
                    text=f"Created: {created_time}\nType: {self.story_combo.get()}\nStyle: {self.style_combo.get()}"
                )
            else:
                # Reset progress bar on error
                self.progress_var.set(0)
                self.update_status(result["message"])

            # Reset processing flag and enable button
            self.is_processing = False
            self.generate_button.config(state=tk.NORMAL)
            # Disable control buttons
            self.pause_button.config(state=tk.DISABLED, text="PAUSE")
            self.stop_button.config(state=tk.DISABLED)
            return

        # Check again after 100ms
        self.root.after(100, self.check_thread)

    def check_batch_thread(self):
        """Check the status of the generator thread for batch processing"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Thread is still running, check again later
            self.root.after(500, self.check_batch_thread)
        else:
            # Get the result of the current video
            result = self.generator_thread.result
            if result["success"]:
                self.most_recent_video = result["video_path"]
                self.recent_video_path.config(text=self.most_recent_video)
                self.open_video_button.config(state=tk.NORMAL)
                self.open_folder_button.config(state=tk.NORMAL)

                # Update history list
                self.refresh_history()

                # Check if we have more videos to generate
                if self.batch_current < self.batch_total:
                    self.batch_current += 1
                    self.progress_var.set(0)
                    self.update_status(f"Starting batch generation: video {self.batch_current} of {self.batch_total}")

                    # Get current values from UI again (in case they were changed)
                    story_type = self.story_combo.get()
                    image_style = self.style_combo.get()
                    custom_script_enabled = self.use_custom_script_var.get()
                    custom_script = self.script_editor.get("1.0", tk.END) if custom_script_enabled else None

                    # Handle custom titles from batch file if enabled
                    custom_title = None
                    ai_custom_title = None

                    if self.batch_titles and self.batch_current <= len(self.batch_titles):
                        # Get the title for the current batch index
                        batch_title = self.batch_titles[self.batch_current - 1]

                        if custom_script_enabled:
                            # Use as custom title for custom script
                            custom_title = batch_title
                            self.update_status(f"Using custom title from file: {batch_title}")
                        else:
                            # Use as AI custom title
                            ai_custom_title = batch_title
                            self.update_status(f"Using AI title from file: {batch_title}")
                    else:
                        # Use the UI values if no batch titles or index out of range
                        custom_title = self.custom_title_entry.get() if custom_script_enabled else None
                        ai_custom_title = self.ai_title_entry.get() if self.use_custom_title_var.get() and not custom_script_enabled else None

                    # Get TTS and image model values
                    tts_model = self.tts_model_combo.get()
                    image_model = self.image_model_combo.get()
                    voice_name = self.voice_combo.get()

                    # Get LLM provider
                    llm_provider = self.shared_llm_provider_var.get()

                    # Caption styling
                    font_name = self.font_combo.get()
                    font_color = self.font_color
                    font_size = self.font_size
                    outline_color = self.outline_color
                    outline_size = self.outline_size
                    caption_position = self.caption_position_var.get()

                    # Initialize bg_music_path to None by default
                    bg_music_path = None

                    # Background music
                    bg_music_selection = self.bg_music_combo.get()
                    if bg_music_selection and bg_music_selection != "None":
                        # Get the music file path directly
                        music_dir = os.path.join(os.path.dirname(script_dir), "music")
                        bg_music_path = os.path.join(music_dir, f"{bg_music_selection}.mp3")

                        # Verify the file exists
                        if not os.path.exists(bg_music_path):
                            print(f"Warning: Music file not found: {bg_music_path}")
                            bg_music_path = None

                    # Get background music volume
                    bg_music_volume = self.bg_music_volume_slider.get()

                    # Get end pause duration
                    end_pause_duration = self.get_end_pause_duration()

                    # Get caption words as int from the dropdown
                    caption_words = int(self.caption_words_var.get())

                    # Get word highlighting options
                    highlight_words = self.highlight_words_var.get()
                    highlight_color = self.highlight_color
                    highlight_style = self.highlight_style_var.get()
                    highlight_bg_color = self.highlight_bg_color
                    highlight_bg_opacity = self.highlight_bg_opacity_slider.get()

                    # Get video quality and orientation
                    video_quality = self.video_quality_var.get()
                    orientation = self.orientation_var.get()

                    # Create and start a new generator thread for the next video
                    self.generator_thread = VideoGeneratorThread(
                        story_type=story_type,
                        image_style=image_style,
                        tts_model=tts_model,
                        image_model=image_model,
                        voice_name=voice_name,
                        font_name=font_name,
                        font_color=font_color,
                        font_size=int(font_size),
                        outline_color=outline_color,
                        outline_size=int(outline_size),
                        caption_position=caption_position,
                        caption_words=caption_words,
                        callback=self.update_status,
                        update_progress=self.update_progress,
                        highlight_words=highlight_words,
                        highlight_color=highlight_color,
                        custom_script=custom_script,
                        custom_title=custom_title,
                        bg_music_path=bg_music_path,
                        bg_music_volume=bg_music_volume,
                        llm_provider=llm_provider,
                        ai_custom_title=ai_custom_title,
                        end_pause_duration=end_pause_duration,
                        orientation=orientation,
                        video_quality=video_quality,
                        highlight_style=highlight_style,
                        highlight_bg_color=highlight_bg_color,
                        highlight_bg_opacity=highlight_bg_opacity
                    )
                    self.generator_thread.start()

                    # Check again
                    self.root.after(500, self.check_batch_thread)
                else:
                    # All videos in the batch have been generated
                    self.update_status(f"Batch generation completed! Generated {self.batch_total} videos.")
                    self.is_processing = False
                    self.generate_button.config(state=tk.NORMAL)
                    # Disable control buttons
                    self.pause_button.config(state=tk.DISABLED, text="PAUSE")
                    self.stop_button.config(state=tk.DISABLED)

                    # Play a completion sound on Windows
                    if platform.system() == "Windows":
                        try:
                            import winsound
                            winsound.MessageBeep(winsound.MB_ICONINFORMATION)
                        except:
                            pass
            else:
                # Error in the current video generation
                self.update_status(f"Error in video {self.batch_current}: {result['message']}")

                # Ask if user wants to continue with the batch
                continue_batch = messagebox.askyesno(
                    "Batch Error",
                    f"Error in video {self.batch_current}: {result['message']}\n\nDo you want to continue with the remaining videos?"
                )

                if continue_batch and self.batch_current < self.batch_total:
                    # Continue with next video
                    self.batch_current += 1
                    self.progress_var.set(0)
                    self.update_status(f"Starting batch generation: video {self.batch_current} of {self.batch_total}")

                    # Create and start a new generator thread
                    # (Same code as the success branch for creating a new thread)
                    story_type = self.story_combo.get()
                    image_style = self.style_combo.get()
                    custom_script_enabled = self.use_custom_script_var.get()
                    custom_script = self.script_editor.get("1.0", tk.END) if custom_script_enabled else None

                    # Handle custom titles from batch file if enabled
                    custom_title = None
                    ai_custom_title = None

                    if self.batch_titles and self.batch_current <= len(self.batch_titles):
                        # Get the title for the current batch index
                        batch_title = self.batch_titles[self.batch_current - 1]

                        if custom_script_enabled:
                            # Use as custom title for custom script
                            custom_title = batch_title
                            self.update_status(f"Using custom title from file: {batch_title}")
                        else:
                            # Use as AI custom title
                            ai_custom_title = batch_title
                            self.update_status(f"Using AI title from file: {batch_title}")
                    else:
                        # Use the UI values if no batch titles or index out of range
                        custom_title = self.custom_title_entry.get() if custom_script_enabled else None
                        ai_custom_title = self.ai_title_entry.get() if self.use_custom_title_var.get() and not custom_script_enabled else None

                    # We've already set ai_custom_title above, no need to set it again

                    # Get TTS and image model values
                    tts_model = self.tts_model_combo.get()
                    image_model = self.image_model_combo.get()
                    voice_name = self.voice_combo.get()

                    # Get LLM provider
                    llm_provider = self.shared_llm_provider_var.get()

                    # Caption styling
                    font_name = self.font_combo.get()
                    font_color = self.font_color
                    font_size = self.font_size
                    outline_color = self.outline_color
                    outline_size = self.outline_size
                    caption_position = self.caption_position_var.get()

                    # Initialize bg_music_path to None by default
                    bg_music_path = None

                    # Background music
                    bg_music_selection = self.bg_music_combo.get()
                    if bg_music_selection and bg_music_selection != "None":
                        # Get the music file path directly
                        music_dir = os.path.join(os.path.dirname(script_dir), "music")
                        bg_music_path = os.path.join(music_dir, f"{bg_music_selection}.mp3")

                        # Verify the file exists
                        if not os.path.exists(bg_music_path):
                            print(f"Warning: Music file not found: {bg_music_path}")
                            bg_music_path = None

                    # Get background music volume
                    bg_music_volume = self.bg_music_volume_slider.get()

                    # Get end pause duration
                    end_pause_duration = self.get_end_pause_duration()

                    # Get caption words as int from the dropdown
                    caption_words = int(self.caption_words_var.get())

                    # Get word highlighting options
                    highlight_words = self.highlight_words_var.get()
                    highlight_color = self.highlight_color
                    highlight_style = self.highlight_style_var.get()
                    highlight_bg_color = self.highlight_bg_color
                    highlight_bg_opacity = self.highlight_bg_opacity_slider.get()

                    # Get video quality and orientation
                    video_quality = self.video_quality_var.get()
                    orientation = self.orientation_var.get()

                    # Create and start a new generator thread for the next video
                    self.generator_thread = VideoGeneratorThread(
                        story_type=story_type,
                        image_style=image_style,
                        tts_model=tts_model,
                        image_model=image_model,
                        voice_name=voice_name,
                        font_name=font_name,
                        font_color=font_color,
                        font_size=int(font_size),
                        outline_color=outline_color,
                        outline_size=int(outline_size),
                        caption_position=caption_position,
                        caption_words=caption_words,
                        callback=self.update_status,
                        update_progress=self.update_progress,
                        highlight_words=highlight_words,
                        highlight_color=highlight_color,
                        custom_script=custom_script,
                        custom_title=custom_title,
                        bg_music_path=bg_music_path,
                        bg_music_volume=bg_music_volume,
                        llm_provider=llm_provider,
                        ai_custom_title=ai_custom_title,
                        end_pause_duration=end_pause_duration,
                        orientation=orientation,
                        video_quality=video_quality,
                        highlight_style=highlight_style,
                        highlight_bg_color=highlight_bg_color,
                        highlight_bg_opacity=highlight_bg_opacity
                    )
                    self.generator_thread.start()

                    # Check again
                    self.root.after(500, self.check_batch_thread)
                else:
                    # Stop batch generation
                    self.update_status(f"Batch generation stopped at video {self.batch_current} of {self.batch_total}.")
                    self.is_processing = False
                    self.generate_button.config(state=tk.NORMAL)

    def open_recent_video(self):
        """Open the most recently generated video"""
        if self.most_recent_video and os.path.exists(self.most_recent_video):
            if platform.system() == "Windows":
                os.startfile(self.most_recent_video)
            elif platform.system() == "Darwin":  # macOS
                subprocess.Popen(["open", self.most_recent_video])
            else:  # Linux
                subprocess.Popen(["xdg-open", self.most_recent_video])
        else:
            messagebox.showerror("Error", "Video file not found")

    def open_selected_video(self):
        """Open the selected video from the history list"""
        selected_indices = self.history_list.curselection()
        if not selected_indices:
            messagebox.showinfo("Info", "Please select a video from the list")
            return

        selected_item = self.history_list.get(selected_indices[0])
        if ":" in selected_item:  # Make sure we can extract a path
            video_path = selected_item.split(":", 1)[1].strip()
            if os.path.exists(video_path):
                if platform.system() == "Windows":
                    os.startfile(video_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.Popen(["open", video_path])
                else:  # Linux
                    subprocess.Popen(["xdg-open", video_path])
            else:
                messagebox.showerror("Error", "Video file not found")

    def open_selected_folder(self):
        """Open the folder containing the selected video"""
        selected_indices = self.history_list.curselection()
        if not selected_indices:
            messagebox.showinfo("Info", "Please select a video from the list")
            return

        selected_item = self.history_list.get(selected_indices[0])
        if ":" in selected_item:  # Make sure we can extract a path
            video_path = selected_item.split(":", 1)[1].strip()
            if os.path.exists(video_path):
                folder_path = os.path.dirname(video_path)
                if platform.system() == "Windows":
                    os.startfile(folder_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.Popen(["open", folder_path])
                else:  # Linux
                    subprocess.Popen(["xdg-open", folder_path])
            else:
                messagebox.showerror("Error", "Video file not found")

    def open_env_file(self):
        """Open the .env file for editing"""
        env_path = os.path.join(os.path.dirname(script_dir), ".env")

        # Create the file if it doesn't exist
        if not os.path.exists(env_path):
            with open(env_path, "w") as f:
                f.write("# API Keys for Faceless Video Generator\n")
                f.write("OPENAI_API_KEY=your_openai_key_here\n")
                f.write("OPENAI_BASE_URL=https://api.openai.com/v1\n")
                f.write("GROQ_API_KEY=your_groq_key_here\n")
                f.write("ELEVENLABS_API_KEY=your_elevenlabs_key_here\n")
                f.write("REPLICATE_API_KEY=your_replicate_key_here\n")
                f.write("FAL_API_KEY=your_fal_key_here\n")
                f.write("TOGETHER_API_KEY=your_together_key_here\n")

        # Open the file
        if platform.system() == "Windows":
            os.startfile(env_path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.Popen(["open", env_path])
        else:  # Linux
            subprocess.Popen(["xdg-open", env_path])

    def check_elevenlabs_api(self):
        """Check the ElevenLabs API status and show the results"""
        self.log_message("Checking ElevenLabs API status...")

        # Run the check_elevenlabs.py script
        try:
            import sys
            import subprocess

            # Get the path to the check_elevenlabs.py script
            script_path = os.path.join(script_dir, "check_elevenlabs.py")

            # Run the script using the same Python interpreter
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True
            )

            # Log the output
            self.log_message(result.stdout)

            # Show a message box with the results
            if result.returncode == 0:
                messagebox.showinfo("ElevenLabs API Status", "ElevenLabs API is working correctly.\n\n" + result.stdout)
            else:
                messagebox.showerror("ElevenLabs API Error", "There was an error with the ElevenLabs API.\n\n" + result.stdout)

        except Exception as e:
            error_message = f"Error checking ElevenLabs API: {str(e)}"
            self.log_message(error_message)
            messagebox.showerror("Error", error_message)



    def reload_config(self):
        """Reload configuration from disk"""
        global config
        config = load_config()

        # Update the config display
        config_text = json.dumps(config, indent=2)
        for page_id, page in self.pages.items():
            if page_id == "settings":
                for widget in page.winfo_children():
                    if isinstance(widget, tk.Canvas):
                        for item in widget.find_all():
                            if "window" in widget.itemconfigure(item):
                                frame = widget.itemcget(item, "window")
                                if frame:
                                    for card in frame.winfo_children():
                                        if isinstance(card, CardFrame):
                                            for grid_child in card.winfo_children():
                                                for child in grid_child.winfo_children():
                                                    if isinstance(child, tk.Text):
                                                        child.configure(state='normal')
                                                        child.delete(1.0, tk.END)
                                                        child.insert(tk.END, config_text)
                                                        child.configure(state='disabled')

        messagebox.showinfo("Success", "Configuration reloaded successfully")

    def refresh_history(self):
        """Refresh the list of generated videos"""
        # Clear current list
        self.history_list.delete(0, tk.END)

        # Scan data directory for videos
        data_dir = os.path.join(os.path.dirname(script_dir), "data")
        if not os.path.exists(data_dir):
            return

        video_files = []
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith("_subtitle.mp4"):  # Prefer subtitled versions
                    full_path = os.path.join(root, file)
                    # Get relative path for display
                    rel_path = os.path.relpath(full_path, os.path.dirname(data_dir))
                    created_time = os.path.getctime(full_path)
                    video_files.append((full_path, rel_path, created_time))
                elif file.endswith(".mp4") and not file.endswith("_subtitle.mp4"):
                    # Check if a subtitled version exists
                    subtitle_path = os.path.join(root, file.replace(".mp4", "_subtitle.mp4"))
                    if os.path.exists(subtitle_path):
                        continue  # Skip, as we'll include the subtitled version

                    full_path = os.path.join(root, file)
                    # Get relative path for display
                    rel_path = os.path.relpath(full_path, os.path.dirname(data_dir))
                    created_time = os.path.getctime(full_path)
                    video_files.append((full_path, rel_path, created_time))

        # Sort by creation time (newest first)
        video_files.sort(key=lambda x: x[2], reverse=True)

        # Add to listbox
        for full_path, rel_path, created_time in video_files:
            created_str = datetime.datetime.fromtimestamp(created_time).strftime('%Y-%m-%d %H:%M:%S')
            self.history_list.insert(tk.END, f"{created_str}: {full_path}")

        # Update UI state based on if we found videos
        if video_files:
            self.open_selected_button.config(state=tk.NORMAL)
            self.open_folder_selected_button.config(state=tk.NORMAL)
        else:
            self.open_selected_button.config(state=tk.DISABLED)
            self.open_folder_selected_button.config(state=tk.DISABLED)

    def toggle_custom_title(self):
        """Toggle the state of the custom title entry for AI-generated scripts"""
        if self.use_custom_title_var.get():
            # Enable custom title field
            self.ai_title_entry.config(state=tk.NORMAL)

            # Disable custom script option as they're mutually exclusive
            if self.use_custom_script_var.get():
                self.use_custom_script_var.set(False)
                self.toggle_custom_script()
        else:
            # Disable custom title field
            self.ai_title_entry.config(state=tk.DISABLED)

    def toggle_custom_script(self):
        """Toggle the state of the custom script editor based on checkbox state"""
        if self.use_custom_script_var.get():
            # Enable custom script fields
            self.script_editor.config(state=tk.NORMAL)
            self.custom_title_entry.config(state=tk.NORMAL)

            # Disable story type selection as it's not used with custom script
            self.story_combo.config(state=tk.DISABLED)

            # Disable custom title for AI option as they're mutually exclusive
            if self.use_custom_title_var.get():
                self.use_custom_title_var.set(False)
                self.toggle_custom_title()
        else:
            # Disable custom script fields
            self.script_editor.config(state=tk.DISABLED)
            self.custom_title_entry.config(state=tk.DISABLED)

            # Re-enable story type selection
            self.story_combo.config(state="readonly")

    def load_sample_script(self):
        """Load a sample script into the text editor"""
        # Enable the custom script option
        self.use_custom_script_var.set(True)
        self.toggle_custom_script()

        # Sample script with multiple scenes separated by blank lines
        sample_script = """I want to share the story of the incredible monarch butterfly migration.

These delicate insects fly up to 3,000 miles from Canada and the United States to central Mexico every year.

What makes this journey remarkable is that no single butterfly completes the entire round trip. It takes multiple generations to make the full migration cycle.

The butterflies that return to the starting point are the great-great-grandchildren of those that began the journey.

Scientists still don't fully understand how they navigate with such precision to the same locations year after year.

This magnificent migration is one of nature's most extraordinary phenomena, showing how even small creatures can accomplish amazing feats."""

        # Set a custom title
        self.custom_title_entry.delete(0, tk.END)
        self.custom_title_entry.insert(0, "The Monarch Butterfly Migration")

        # Clear and insert the sample script
        self.script_editor.delete(1.0, tk.END)
        self.script_editor.insert(1.0, sample_script)

    def toggle_highlight_color(self):
        """Toggle the state of the highlight color picker based on checkbox state"""
        if self.highlight_words_var.get():
            # Enable highlight color picker
            self.highlight_color_picker.set_state(tk.NORMAL)

            # Enable highlight style controls
            self.highlight_style_group.set_state(tk.NORMAL)
            self.highlight_bg_color_picker.set_state(tk.NORMAL)
            self.highlight_bg_opacity_slider.set_state(tk.NORMAL)
        else:
            # Disable highlight color picker
            self.highlight_color_picker.set_state(tk.DISABLED)

            # Disable highlight style controls
            self.highlight_style_group.set_state(tk.DISABLED)
            self.highlight_bg_color_picker.set_state(tk.DISABLED)
            self.highlight_bg_opacity_slider.set_state(tk.DISABLED)

    def filter_voices(self, event=None):
        """Filter voices based on selected locale"""
        selected_display = self.locale_filter_var.get()

        try:
            # Convert display name to locale code
            selected_locale = self.country_to_locale.get(selected_display, "All")

            # Load voices from JSON file
            with open(os.path.join(script_dir, "english_edge_voices.json"), "r") as f:
                english_voices = json.load(f)

            # Filter voices by locale if needed
            if selected_locale != "All":
                filtered_voices = [v for v in english_voices if v["Locale"] == selected_locale]
            else:
                filtered_voices = english_voices

            # Create a list of voice names for the dropdown
            voice_options = []
            for voice in filtered_voices:
                short_name = voice["ShortName"]

                # Extract just the voice name without the locale prefix
                voice_name = short_name.split('-')[-1]  # Gets the part after the last hyphen (e.g., "AriaNeural" from "en-US-AriaNeural")

                # Use only the voice name as the display name
                display_name = voice_name

                # Store a mapping of display name to actual voice ID for later use
                self.voice_display_to_id = getattr(self, 'voice_display_to_id', {})
                self.voice_display_to_id[display_name] = short_name

                voice_options.append(display_name)

            # Sort voices
            voice_options.sort()

            # Update the combobox
            current_voice = self.voice_combo.get()
            self.voice_combo.configure(values=voice_options)

            # Try to keep the current selection if it's still in the list
            if current_voice in voice_options:
                self.voice_combo.set(current_voice)
            else:
                # Default to first voice in the list
                if voice_options:
                    self.voice_combo.current(0)

        except Exception as e:
            print(f"Error filtering voices: {str(e)}")

    def refresh_voice_list(self):
        """Refresh the voice list by running the update_edge_voices.py script"""
        try:
            # Show a message that we're refreshing
            self.log_message("Refreshing voice list from Edge TTS...")

            # Run the update_edge_voices.py script
            import subprocess
            import sys

            # Get the path to the update_edge_voices.py script
            script_path = os.path.join(script_dir, "update_edge_voices.py")

            # Run the script using the same Python interpreter
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                self.log_message("Voice list refreshed successfully!")
                # Reload the voice list
                self.filter_voices()
            else:
                self.log_message(f"Error refreshing voice list: {result.stderr}")

        except Exception as e:
            self.log_message(f"Error refreshing voice list: {str(e)}")
            messagebox.showerror("Error", f"Failed to refresh voice list: {str(e)}")

    def refresh_resources(self):
        """Refresh fonts and music resources without restarting the application"""
        try:
            self.log_message("Refreshing resources (fonts and music)...")

            # Refresh fonts
            available_fonts = get_available_fonts()
            if not available_fonts:
                available_fonts = ["TitanOne", "Ranchers", "RampartOne", "PermanentMarker", "OpenSans", "NotoSans",
                                 "Montserrat", "LuckiestGuy", "Knewave", "Jua", "Creepster", "Caveat",
                                 "Bungee", "BebasNeue", "Bangers", "BakbakOne"]
                self.log_message("No fonts found in font directory, using default font list")
            else:
                self.log_message(f"Found {len(available_fonts)} fonts in the font directory")

            # Update font dropdown
            current_font = self.font_combo.get()
            self.font_combo.configure(values=available_fonts)

            # Try to keep the previously selected font if it still exists
            if current_font in available_fonts:
                self.font_combo.set(current_font)
            else:
                # Set default font (TitanOne if available, otherwise first font in list)
                if "TitanOne" in available_fonts:
                    self.font_combo.set("TitanOne")
                else:
                    self.font_combo.current(0)

            # Refresh music files
            music_dir = os.path.join(os.path.dirname(script_dir), "music")
            music_options = ["None"]

            # Create music directory if it doesn't exist
            os.makedirs(music_dir, exist_ok=True)

            # Scan for MP3 files in the music directory
            for file in os.listdir(music_dir):
                if file.lower().endswith(".mp3"):
                    # Use the filename without extension as the display name
                    music_name = os.path.splitext(file)[0]
                    music_options.append(music_name)

            # Update music dropdown
            current_music = self.bg_music_combo.get()
            self.bg_music_combo.configure(values=music_options)

            # Try to keep the previously selected music if it still exists
            if current_music in music_options:
                self.bg_music_combo.set(current_music)
            else:
                self.bg_music_combo.current(0)  # Default to None

            self.log_message(f"Found {len(music_options)-1} music files in the music directory")
            self.log_message("Resources refreshed successfully!")

            # Show success message
            messagebox.showinfo("Success", "Fonts and music refreshed successfully!")

        except Exception as e:
            self.log_message(f"Error refreshing resources: {str(e)}")
            messagebox.showerror("Error", f"Failed to refresh resources: {str(e)}")

    def preview_selected_voice(self):
        """Preview the currently selected voice with a sample text"""
        voice = self.voice_combo.get()
        if not voice:
            messagebox.showinfo("Voice Preview", "Please select a voice first.")
            return

        # Get the TTS model
        tts_model = self.tts_model_combo.get()

        # Get the actual voice ID if we're using a display name
        voice_id = voice
        try:
            if hasattr(self, 'voice_display_to_id') and voice in self.voice_display_to_id:
                voice_id = self.voice_display_to_id[voice]
            elif tts_model == "Voicely" and not voice.startswith("en-"):
                # For Edge TTS (Voicely), ensure we have the full voice ID with locale prefix
                voice_id = f"en-US-{voice}"  # Default to US English if no mapping exists
        except Exception as e:
            print(f"Error getting voice ID: {str(e)}")

        # Show a message to the user
        self.log_message(f"Playing preview for voice: {voice}")

        # Generate and play the preview
        threading.Thread(
            target=lambda: voice_preview.generate_and_play_preview(
                voice_id, tts_model, None, True  # Force new sample
            ),
            daemon=True
        ).start()

    def toggle_batch_generation(self):
        """Toggle the state of the batch generation checkbox"""
        if self.batch_enabled_var.get():
            # Enable batch generation fields
            self.batch_quantity_spinbox.config(state="normal")
            self.batch_custom_titles_check.config(state="normal")
            self.batch_custom_scripts_check.config(state="normal")

            # Update custom titles file controls based on checkbox state
            self.toggle_batch_custom_titles()
            self.toggle_batch_custom_scripts()
        else:
            # Disable batch generation fields
            self.batch_quantity_spinbox.config(state="disabled")
            self.batch_custom_titles_check.config(state="disabled")
            self.batch_custom_scripts_check.config(state="disabled")
            self.titles_file_entry.config(state="disabled")
            self.browse_titles_button.config(state="disabled")
            self.scripts_folder_entry.config(state="disabled")
            self.browse_scripts_folder_button.config(state="disabled")

    def toggle_batch_custom_titles(self):
        """Toggle the state of the custom titles file controls"""
        if self.batch_custom_titles_var.get() and self.batch_enabled_var.get():
            # Enable custom titles file controls
            self.titles_file_entry.config(state="normal")
            self.browse_titles_button.config(state="normal")
            self.sample_titles_button.config(state="normal")

            # Disable custom scripts option as they're mutually exclusive
            if self.batch_custom_scripts_var.get():
                self.batch_custom_scripts_var.set(False)
                self.toggle_batch_custom_scripts()
        else:
            # Disable custom titles file controls
            self.titles_file_entry.config(state="disabled")
            self.browse_titles_button.config(state="disabled")
            self.sample_titles_button.config(state="disabled")

    def toggle_batch_custom_scripts(self):
        """Toggle the state of the custom scripts folder controls"""
        if self.batch_custom_scripts_var.get() and self.batch_enabled_var.get():
            # Enable custom scripts folder controls
            self.scripts_folder_entry.config(state="normal")
            self.browse_scripts_folder_button.config(state="normal")
            self.sample_scripts_button.config(state="normal")

            # Disable custom titles option as they're mutually exclusive
            if self.batch_custom_titles_var.get():
                self.batch_custom_titles_var.set(False)
                self.toggle_batch_custom_titles()

            # Enable custom script in the main UI since we'll be using custom scripts
            self.use_custom_script_var.set(True)
            self.toggle_custom_script()
        else:
            # Disable custom scripts folder controls
            self.scripts_folder_entry.config(state="disabled")
            self.browse_scripts_folder_button.config(state="disabled")
            self.sample_scripts_button.config(state="disabled")

            # If we're disabling custom scripts and no other batch option is enabled,
            # reset the custom script option in the main UI
            if not self.batch_custom_titles_var.get() and self.use_custom_script_var.get():
                self.use_custom_script_var.set(False)
                self.toggle_custom_script()

    def load_sample_titles(self):
        """Load the sample titles file"""
        # Get the path to the sample titles file
        sample_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "sample_titles.txt")

        if os.path.exists(sample_file):
            self.titles_file_var.set(sample_file)

            # Preview the titles and check if they match the batch quantity
            try:
                with open(sample_file, 'r', encoding='utf-8') as f:
                    titles = [line.strip() for line in f.readlines() if line.strip()]

                batch_quantity = int(self.batch_quantity_var.get())

                if len(titles) < batch_quantity:
                    messagebox.showwarning(
                        "Title Count Mismatch",
                        f"The sample file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Either add more titles to the file or reduce the batch quantity."
                    )
                elif len(titles) > batch_quantity:
                    messagebox.showinfo(
                        "Extra Titles",
                        f"The sample file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Only the first {batch_quantity} titles will be used."
                    )

                # Show a preview of the titles
                preview = "\n".join(titles[:5])
                if len(titles) > 5:
                    preview += f"\n...\n(and {len(titles) - 5} more)"

                messagebox.showinfo(
                    "Sample Titles Loaded",
                    f"Sample titles file loaded with {len(titles)} titles.\n\nPreview:\n{preview}"
                )
            except Exception as e:
                messagebox.showerror("Error Reading File", f"Could not read the sample titles file: {str(e)}")
        else:
            messagebox.showerror(
                "Sample File Not Found",
                "The sample titles file was not found. Please use the Browse button to select a custom titles file."
            )

    def browse_titles_file(self):
        """Open a file dialog to select a text file with custom titles"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="Select Custom Titles File",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )

        if file_path:
            self.titles_file_var.set(file_path)

            # Preview the titles and check if they match the batch quantity
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    titles = [line.strip() for line in f.readlines() if line.strip()]

                batch_quantity = int(self.batch_quantity_var.get())

                if len(titles) < batch_quantity:
                    messagebox.showwarning(
                        "Title Count Mismatch",
                        f"The file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Either add more titles to the file or reduce the batch quantity."
                    )
                elif len(titles) > batch_quantity:
                    messagebox.showinfo(
                        "Extra Titles",
                        f"The file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Only the first {batch_quantity} titles will be used."
                    )
            except Exception as e:
                messagebox.showerror("Error Reading File", f"Could not read the titles file: {str(e)}")

    def browse_scripts_folder(self):
        """Open a folder dialog to select a folder with custom script files"""
        from tkinter import filedialog

        folder_path = filedialog.askdirectory(
            title="Select Folder with Custom Script Files"
        )

        if folder_path:
            self.scripts_folder_var.set(folder_path)

            # Check for script files in the folder
            script_files = []
            try:
                for file in os.listdir(folder_path):
                    if file.lower().endswith('.txt'):
                        script_files.append(file)

                if not script_files:
                    messagebox.showwarning(
                        "No Script Files Found",
                        f"No .txt files were found in the selected folder.\n\n"
                        f"Please select a folder containing text files with your custom scripts."
                    )
                    return

                # Update batch quantity based on number of script files
                self.batch_quantity_var.set(str(len(script_files)))

                # Show a preview of the script files
                preview_files = script_files[:5]
                preview_text = "\n".join(preview_files)
                if len(script_files) > 5:
                    preview_text += f"\n...\n(and {len(script_files) - 5} more)"

                messagebox.showinfo(
                    "Script Files Found",
                    f"Found {len(script_files)} script files in the folder.\n\n"
                    f"The batch quantity has been set to {len(script_files)}.\n\n"
                    f"Files:\n{preview_text}"
                )
            except Exception as e:
                messagebox.showerror("Error Reading Folder", f"Could not read the folder: {str(e)}")

    def create_sample_scripts_folder(self):
        """Create a sample folder with script files for demonstration"""
        import tempfile
        import os

        # Create a temporary directory for sample scripts
        temp_dir = tempfile.mkdtemp(prefix="sample_scripts_")

        # Sample script 1
        with open(os.path.join(temp_dir, "Monarch Butterfly Migration.txt"), "w", encoding='utf-8') as f:
            f.write("The monarch butterfly migration is one of nature's most incredible phenomena.\n\n")
            f.write("These delicate insects travel up to 3,000 miles from Canada and the United States to central Mexico every year.\n\n")
            f.write("What makes this journey remarkable is that no single butterfly completes the entire round trip. It takes multiple generations to make the full migration cycle.\n\n")
            f.write("The butterflies that return to the starting point are the great-great-grandchildren of those that began the journey.\n\n")
            f.write("Scientists still don't fully understand how they navigate with such precision to the same locations year after year.")

        # Sample script 2
        with open(os.path.join(temp_dir, "Deep Sea Exploration.txt"), "w", encoding='utf-8') as f:
            f.write("The deep sea remains one of Earth's last great frontiers.\n\n")
            f.write("More than 80% of our oceans remain unmapped, unobserved, and unexplored.\n\n")
            f.write("The deepest part of the ocean, the Mariana Trench, reaches depths of nearly 11,000 meters - deeper than Mount Everest is tall.\n\n")
            f.write("At these extreme depths, the pressure is more than 1,000 times that at sea level, enough to crush a submarine.\n\n")
            f.write("Despite these harsh conditions, life thrives in the deep sea, with new species being discovered on every expedition.")

        # Sample script 3
        with open(os.path.join(temp_dir, "Northern Lights.txt"), "w", encoding='utf-8') as f:
            f.write("The Northern Lights, or Aurora Borealis, are one of Earth's most magnificent natural displays.\n\n")
            f.write("These dancing lights occur when charged particles from the sun collide with gases in Earth's atmosphere.\n\n")
            f.write("The different colors we see are caused by different types of gas particles. Oxygen produces green and red lights, while nitrogen creates blue and purple hues.\n\n")
            f.write("The best places to view the Northern Lights include Alaska, Canada, Iceland, Norway, and Finland.\n\n")
            f.write("Indigenous peoples have many legends about the lights, with some believing they are spirits of ancestors dancing in the sky.")

        # Set the folder path in the entry
        self.scripts_folder_var.set(temp_dir)

        # Update batch quantity based on number of script files
        self.batch_quantity_var.set("3")

        # Show a message
        messagebox.showinfo(
            "Sample Scripts Created",
            f"Sample script files have been created in a temporary folder:\n{temp_dir}\n\n"
            f"This folder contains 3 script files that will be used for batch generation.\n\n"
            f"The folder will be deleted when you close the application."
        )

    def on_ai_provider_change(self, event=None):
        """Handle changing the AI provider from the main UI"""
        selected_provider = self.shared_llm_provider_var.get()

        # Try to switch to the selected provider
        success, message = switch_ai_provider(selected_provider)

        if success:
            self.ai_provider_status.config(text=f"Status: {selected_provider} Ready", foreground=ModernUI.COLORS['success'])
            self.log_message(f"Switched to {selected_provider} AI provider")

            # Also update the script generator status if it exists
            if hasattr(self, 'script_ai_provider_status'):
                self.script_ai_provider_status.config(text=f"Status: {selected_provider} Ready", foreground=ModernUI.COLORS['success'])
        else:
            # Get the current provider after the failed switch attempt
            current_provider = ai_client.get_current_provider().lower()
            provider_name = "OpenAI" if current_provider == "openai" else "Groq"

            # Update UI to reflect the actual provider being used
            self.ai_provider_status.config(text=f"Status: Using {provider_name}", foreground=ModernUI.COLORS['warning'])

            # Set the radio button to match the actual provider
            self.shared_llm_provider_var.set(provider_name)

            # Also update the script generator status if it exists
            if hasattr(self, 'script_ai_provider_status'):
                self.script_ai_provider_status.config(text=f"Status: Using {provider_name}", foreground=ModernUI.COLORS['warning'])

            # Show error message
            error_msg = f"{message}\n\nFalling back to {provider_name} provider."
            self.log_message(f"Failed to switch to {selected_provider}: {message}. Using {provider_name} instead.")
            messagebox.showerror("AI Provider Error", error_msg)

def console_main():
    """Run the application in console mode"""
    # 1. pick story type, image style and voice name
    story_type = pick_story_type()
    image_style = pick_image_style()
    voice_name = pick_voice_name()

    # Default video settings
    video_quality = "720p"
    orientation = "portrait"

    # Ask for video quality and orientation
    print("\nChoose video quality:")
    print("1. 720p (default)")
    print("2. 1080p")
    print("3. 2K")
    print("4. 4K")
    quality_choice = input("Enter choice (1-4) or press Enter for default: ")
    if quality_choice == "2":
        video_quality = "1080p"
    elif quality_choice == "3":
        video_quality = "2K"
    elif quality_choice == "4":
        video_quality = "4K"

    print("\nChoose video orientation:")
    print("1. Portrait (9:16) (default)")
    print("2. Landscape (16:9)")
    orientation_choice = input("Enter choice (1-2) or press Enter for default: ")
    if orientation_choice == "2":
        orientation = "landscape"

    # 2. generate story and storyboard
    storyboard_project = generate_story(client, story_type)
    storyboard_project["image_style"] = image_style

    # 3. generate images and create video
    storyboard_project = generate_storyboard(
        client,
        storyboard_project,
        image_generator_func=replicate_flux_api,
        video_quality=video_quality,
        orientation=orientation
    )

    if storyboard_project.get("storyboards"):
        print("\nCreating video from images...")
        story_dir = storyboard_project["story_dir"]
        audio_dir = os.path.join(story_dir, "audio")
        video_path = os.path.join(story_dir, "story_video.mp4")
        process_video(
            client,
            storyboard_project,
            video_path,
            audio_dir,
            voice_name,
            orientation=orientation,
            video_quality=video_quality
        )
        print(f"Video created: {video_path}")
    else:
        print("No storyboard data available. Cannot create video.")


def gui_main():
    """Run the application in GUI mode"""
    try:
        # For Windows, try to set app ID for proper taskbar icon
        if platform.system() == "Windows":
            try:
                import ctypes
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(APP_NAME)
            except:
                pass

        root = ThemedTk(theme="arc")  # Using a modern theme

        # Set initial size and position for login
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.geometry(f"1200x800+{(screen_width - 1200) // 2}+{(screen_height - 800) // 2}")

        # Show login dialog first
        if not show_login_dialog(root):
            root.destroy()
            sys.exit(0)  # Exit if login fails

        # Create the application after successful login
        app = MainApp(root)

        # Start the main loop
        root.mainloop()

    except ImportError:
        # Fallback to regular Tkinter if ttkthemes is not available
        root = tk.Tk()

        # Set initial size and position
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.geometry(f"1200x800+{(screen_width - 1200) // 2}+{(screen_height - 800) // 2}")

        # Show login dialog first
        if not show_login_dialog(root):
            root.destroy()
            sys.exit(0)  # Exit if login fails

        # Create the application after successful login
        app = MainApp(root)

        # Start the main loop
        root.mainloop()

    except Exception as e:
        # Show error dialog for any other issues
        messagebox.showerror("Error", f"An error occurred: {str(e)}")
        raise


if __name__ == "__main__":
    # Parse command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        console_main()  # Run in console mode
    else:
        gui_main()  # Run in GUI mode by default