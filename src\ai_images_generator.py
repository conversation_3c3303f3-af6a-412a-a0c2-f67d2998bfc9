"""
AI Images Generator module for generating AI images.
Supports single image generation and bulk image generation from text prompts.
"""
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import datetime
from PIL import Image, ImageTk
import io
from api import replicate_flux_api, fal_flux_api, together_flux_api, runware_flux_api
from utils import load_config

class AIImagesGenerator:
    """
    AI Images Generator UI and functionality.
    Allows users to generate single images or bulk images from text prompts.
    """

    def __init__(self, parent):
        """
        Initialize the AI Images Generator UI.

        Args:
            parent: The parent frame
        """
        self.parent = parent
        self.config = load_config()

        # Create main container
        self.main_container = ttk.Frame(parent)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create title
        title_label = ttk.Label(
            self.main_container,
            text="AI Images Generator",
            font=("Segoe UI", 18, "bold")
        )
        title_label.pack(anchor="w", pady=(0, 20))

        # Create tabs for single and bulk generation
        self.tab_control = ttk.Notebook(self.main_container)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Single image generation tab
        self.single_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.single_tab, text="Single Image", sticky="nsew")

        # Bulk image generation tab
        self.bulk_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.bulk_tab, text="Bulk Images", sticky="nsew")

        # Setup single image tab
        self._setup_single_image_tab()

        # Setup bulk image tab
        self._setup_bulk_image_tab()

        # Create output directory if it doesn't exist
        self.output_dir = os.path.join(os.getcwd(), "generated_images")
        os.makedirs(self.output_dir, exist_ok=True)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(
            self.main_container,
            textvariable=self.status_var,
            font=("Segoe UI", 10)
        )
        self.status_bar.pack(anchor="w", pady=(10, 0))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.main_container,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

    def _setup_single_image_tab(self):
        """Setup the UI for single image generation"""
        # Left panel for inputs
        left_panel = ttk.Frame(self.single_tab)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Prompt input
        prompt_label = ttk.Label(
            left_panel,
            text="Image Prompt:",
            font=("Segoe UI", 12)
        )
        prompt_label.pack(anchor="w", pady=(0, 5))

        self.prompt_text = tk.Text(
            left_panel,
            height=10,
            width=40,
            wrap=tk.WORD,
            font=("Segoe UI", 11)
        )
        self.prompt_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Options frame
        options_frame = ttk.LabelFrame(left_panel, text="Options")
        options_frame.pack(fill=tk.X, pady=(0, 10))

        # Orientation selection
        orientation_frame = ttk.Frame(options_frame)
        orientation_frame.pack(fill=tk.X, padx=10, pady=5)

        orientation_label = ttk.Label(
            orientation_frame,
            text="Orientation:",
            width=15
        )
        orientation_label.pack(side=tk.LEFT)

        self.orientation_var = tk.StringVar(value="portrait")
        orientation_combo = ttk.Combobox(
            orientation_frame,
            textvariable=self.orientation_var,
            values=["portrait", "landscape"],
            state="readonly",
            width=20
        )
        orientation_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # AI Provider selection
        provider_frame = ttk.Frame(options_frame)
        provider_frame.pack(fill=tk.X, padx=10, pady=5)

        provider_label = ttk.Label(
            provider_frame,
            text="AI Provider:",
            width=15
        )
        provider_label.pack(side=tk.LEFT)

        self.provider_var = tk.StringVar(value="Replicate Flux")
        provider_combo = ttk.Combobox(
            provider_frame,
            textvariable=self.provider_var,
            values=["Replicate Flux", "FAL AI Flux", "Together AI Flux", "Runware Flux Dev", "Runware Flex Schenele"],
            state="readonly",
            width=20
        )
        provider_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Generate button
        self.generate_button = ttk.Button(
            left_panel,
            text="Generate Image",
            command=self.generate_single_image
        )
        self.generate_button.pack(fill=tk.X, pady=(10, 0))

        # Right panel for image display
        right_panel = ttk.LabelFrame(self.single_tab, text="Generated Image")
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Image display
        self.image_display = ttk.Label(right_panel)
        self.image_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Save button (initially disabled)
        self.save_button = ttk.Button(
            right_panel,
            text="Save Image",
            command=self.save_single_image,
            state="disabled"
        )
        self.save_button.pack(fill=tk.X, padx=10, pady=(0, 10))

    def _setup_bulk_image_tab(self):
        """Setup the UI for bulk image generation"""
        # Top panel for inputs
        top_panel = ttk.Frame(self.bulk_tab)
        top_panel.pack(fill=tk.X, pady=(0, 10))

        # File selection
        file_frame = ttk.Frame(top_panel)
        file_frame.pack(fill=tk.X, pady=5)

        file_label = ttk.Label(
            file_frame,
            text="Prompts File:",
            width=15
        )
        file_label.pack(side=tk.LEFT)

        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(
            file_frame,
            textvariable=self.file_path_var,
            width=40
        )
        file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        browse_button = ttk.Button(
            file_frame,
            text="Browse",
            command=self.browse_file
        )
        browse_button.pack(side=tk.LEFT)

        # Options frame
        options_frame = ttk.LabelFrame(top_panel, text="Options")
        options_frame.pack(fill=tk.X, pady=(5, 0))

        # Orientation selection
        orientation_frame = ttk.Frame(options_frame)
        orientation_frame.pack(fill=tk.X, padx=10, pady=5)

        orientation_label = ttk.Label(
            orientation_frame,
            text="Orientation:",
            width=15
        )
        orientation_label.pack(side=tk.LEFT)

        self.bulk_orientation_var = tk.StringVar(value="portrait")
        orientation_combo = ttk.Combobox(
            orientation_frame,
            textvariable=self.bulk_orientation_var,
            values=["portrait", "landscape"],
            state="readonly",
            width=20
        )
        orientation_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # AI Provider selection
        provider_frame = ttk.Frame(options_frame)
        provider_frame.pack(fill=tk.X, padx=10, pady=5)

        provider_label = ttk.Label(
            provider_frame,
            text="AI Provider:",
            width=15
        )
        provider_label.pack(side=tk.LEFT)

        self.bulk_provider_var = tk.StringVar(value="Replicate Flux")
        provider_combo = ttk.Combobox(
            provider_frame,
            textvariable=self.bulk_provider_var,
            values=["Replicate Flux", "FAL AI Flux", "Together AI Flux", "Runware Flux Dev", "Runware Flex Schenele"],
            state="readonly",
            width=20
        )
        provider_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Generate button
        self.bulk_generate_button = ttk.Button(
            top_panel,
            text="Generate Bulk Images",
            command=self.generate_bulk_images
        )
        self.bulk_generate_button.pack(fill=tk.X, pady=(10, 0))

        # Bottom panel for results
        self.results_frame = ttk.LabelFrame(self.bulk_tab, text="Results")
        self.results_frame.pack(fill=tk.BOTH, expand=True)

        # Results text
        self.results_text = tk.Text(
            self.results_frame,
            height=10,
            width=40,
            wrap=tk.WORD,
            font=("Segoe UI", 10)
        )
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.results_text, command=self.results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_text.config(yscrollcommand=scrollbar.set)

    def browse_file(self):
        """Open file dialog to select a text file with prompts"""
        file_path = filedialog.askopenfilename(
            title="Select Prompts File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)

    def generate_single_image(self):
        """Generate a single image based on the prompt"""
        prompt = self.prompt_text.get("1.0", tk.END).strip()
        if not prompt:
            messagebox.showerror("Error", "Please enter a prompt")
            return

        orientation = self.orientation_var.get()
        provider = self.provider_var.get()

        # Disable generate button
        self.generate_button.config(state="disabled")
        self.status_var.set("Generating image...")
        self.progress_var.set(10)

        # Start generation in a separate thread
        threading.Thread(
            target=self._generate_single_image_thread,
            args=(prompt, orientation, provider),
            daemon=True
        ).start()

    def _generate_single_image_thread(self, prompt, orientation, provider):
        """Thread function for generating a single image"""
        try:
            # Select image generation function based on provider
            if provider == "Replicate Flux":
                image_generator_func = replicate_flux_api
            elif provider == "FAL AI Flux":
                image_generator_func = fal_flux_api
            elif provider == "Together AI Flux":
                image_generator_func = together_flux_api
            elif provider == "Runware Flux Dev":
                # Use runware_flux_api with flux_dev model
                image_generator_func = lambda prompt, orientation=orientation: runware_flux_api(
                    prompt, orientation=orientation, model_type="flux_dev"
                )
            elif provider == "Runware Flex Schenele":
                # Use runware_flux_api with flex_schenele model
                image_generator_func = lambda prompt, orientation=orientation: runware_flux_api(
                    prompt, orientation=orientation, model_type="flex_schenele"
                )
            else:
                image_generator_func = replicate_flux_api  # Default

            self.progress_var.set(30)

            # Generate image
            image_bytes = image_generator_func(prompt, orientation=orientation)

            if not image_bytes:
                raise Exception("Failed to generate image")

            self.progress_var.set(70)

            # Store the image bytes for later saving
            self.current_image_bytes = image_bytes

            # Display the image
            self._display_image(image_bytes)

            self.status_var.set("Image generated successfully")
            self.progress_var.set(100)

            # Enable save button
            self.save_button.config(state="normal")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate image: {str(e)}")
            self.status_var.set("Error generating image")
        finally:
            # Re-enable generate button
            self.generate_button.config(state="normal")

    def _display_image(self, image_bytes):
        """Display the generated image"""
        try:
            # Open image from bytes
            image = Image.open(io.BytesIO(image_bytes))

            # Resize to fit display area (max 400x400)
            display_width = 400
            display_height = 400

            # Calculate aspect ratio
            width, height = image.size
            aspect_ratio = width / height

            if aspect_ratio > 1:  # Landscape
                new_width = display_width
                new_height = int(display_width / aspect_ratio)
            else:  # Portrait
                new_height = display_height
                new_width = int(display_height * aspect_ratio)

            # Resize image
            image = image.resize((new_width, new_height), Image.LANCZOS)

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Update label
            self.image_display.config(image=photo)
            self.image_display.image = photo  # Keep a reference

        except Exception as e:
            messagebox.showerror("Error", f"Failed to display image: {str(e)}")

    def save_single_image(self):
        """Save the generated image"""
        if not hasattr(self, 'current_image_bytes'):
            messagebox.showerror("Error", "No image to save")
            return

        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            title="Save Image",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")],
            initialdir=self.output_dir
        )

        if not file_path:
            return

        try:
            # Save image
            with open(file_path, "wb") as f:
                f.write(self.current_image_bytes)

            self.status_var.set(f"Image saved to {file_path}")
            messagebox.showinfo("Success", f"Image saved to {file_path}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save image: {str(e)}")

    def generate_bulk_images(self):
        """Generate multiple images from a text file"""
        file_path = self.file_path_var.get()
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("Error", "Please select a valid prompts file")
            return

        orientation = self.bulk_orientation_var.get()
        provider = self.bulk_provider_var.get()

        # Disable generate button
        self.bulk_generate_button.config(state="disabled")
        self.status_var.set("Reading prompts file...")
        self.progress_var.set(5)

        # Clear results
        self.results_text.delete("1.0", tk.END)

        # Start generation in a separate thread
        threading.Thread(
            target=self._generate_bulk_images_thread,
            args=(file_path, orientation, provider),
            daemon=True
        ).start()

    def _generate_bulk_images_thread(self, file_path, orientation, provider):
        """Thread function for generating bulk images"""
        try:
            # Read prompts from file
            with open(file_path, "r", encoding="utf-8") as f:
                prompts = [line.strip() for line in f.readlines() if line.strip()]

            if not prompts:
                raise Exception("No prompts found in the file")

            self.results_text.insert(tk.END, f"Found {len(prompts)} prompts\n")
            self.progress_var.set(10)

            # Select image generation function based on provider
            if provider == "Replicate Flux":
                image_generator_func = replicate_flux_api
            elif provider == "FAL AI Flux":
                image_generator_func = fal_flux_api
            elif provider == "Together AI Flux":
                image_generator_func = together_flux_api
            elif provider == "Runware Flux Dev":
                # Use runware_flux_api with flux_dev model
                image_generator_func = lambda prompt, orientation=orientation: runware_flux_api(
                    prompt, orientation=orientation, model_type="flux_dev"
                )
            elif provider == "Runware Flex Schenele":
                # Use runware_flux_api with flex_schenele model
                image_generator_func = lambda prompt, orientation=orientation: runware_flux_api(
                    prompt, orientation=orientation, model_type="flex_schenele"
                )
            else:
                image_generator_func = replicate_flux_api  # Default

            # Create output directory with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(self.output_dir, f"bulk_{timestamp}")
            os.makedirs(output_dir, exist_ok=True)

            self.results_text.insert(tk.END, f"Output directory: {output_dir}\n\n")

            # Generate images
            success_count = 0
            for i, prompt in enumerate(prompts):
                # Update status
                self.status_var.set(f"Generating image {i+1}/{len(prompts)}")
                self.progress_var.set(10 + (i / len(prompts)) * 90)

                self.results_text.insert(tk.END, f"Prompt {i+1}: {prompt[:50]}...\n")
                self.results_text.see(tk.END)

                try:
                    # Generate image
                    image_bytes = image_generator_func(prompt, orientation=orientation)

                    if not image_bytes:
                        self.results_text.insert(tk.END, "  Failed: No image generated\n")
                        continue

                    # Save image
                    image_path = os.path.join(output_dir, f"image_{i+1:03d}.png")
                    with open(image_path, "wb") as f:
                        f.write(image_bytes)

                    self.results_text.insert(tk.END, f"  Success: Saved to {os.path.basename(image_path)}\n")
                    success_count += 1

                except Exception as e:
                    self.results_text.insert(tk.END, f"  Failed: {str(e)}\n")

                self.results_text.see(tk.END)

            # Update status
            self.status_var.set(f"Generated {success_count}/{len(prompts)} images")
            self.progress_var.set(100)

            self.results_text.insert(tk.END, f"\nCompleted: Generated {success_count}/{len(prompts)} images\n")
            self.results_text.insert(tk.END, f"Images saved to: {output_dir}\n")
            self.results_text.see(tk.END)

            # Show completion message
            messagebox.showinfo("Complete", f"Generated {success_count}/{len(prompts)} images\nSaved to: {output_dir}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate images: {str(e)}")
            self.status_var.set("Error generating images")
        finally:
            # Re-enable generate button
            self.bulk_generate_button.config(state="normal")
