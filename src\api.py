import requests
import replicate
import os
import hashlib
from typing import Optional
import time
from utils import load_config
import fal_client
import uuid
import base64
from dotenv import load_dotenv

# Simple in-memory cache for generated images
_image_cache = {}
_MAX_CACHE_SIZE = 20  # Maximum number of images to keep in cache

# Load environment variables
load_dotenv()

def submit_fal_request(prompt: str, config: dict, orientation: str = "portrait", video_quality: str = "720p") -> Optional[str]:
    try:
        # FAL client expects the key in FAL_KEY environment variable
        api_key = os.getenv("FAL_KEY")

        # If FAL_KEY is not set, try FAL_API_KEY as fallback for backward compatibility
        if not api_key:
            api_key = os.getenv("FAL_API_KEY")
            if api_key:
                print("WARNING: Using FAL_API_KEY is deprecated. Please use FAL_KEY instead.")

        if not api_key:
            raise ValueError("FAL_KEY not found in environment variables")

        # Set the API key for FAL client
        os.environ['FAL_KEY'] = api_key

        # Add a negative prompt to prevent text in the generated images
        negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

        # Get dimensions based on video quality and orientation
        all_config = load_config()
        video_dimensions = all_config["video_resolutions"].get(video_quality, all_config["video_resolutions"]["720p"])
        dimensions = video_dimensions.get(orientation, video_dimensions["portrait"])
        width, height = dimensions["width"], dimensions["height"]

        # Calculate aspect ratio
        aspect_ratio = width / height

        # Determine the appropriate image_size parameter based on aspect ratio
        # FAL AI expects specific string values or a properly formatted object
        if abs(aspect_ratio - 9/16) < 0.1:  # Portrait (9:16)
            image_size = "portrait_16_9"
            print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
        elif abs(aspect_ratio - 16/9) < 0.1:  # Landscape (16:9)
            image_size = "landscape_16_9"
            print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
        elif abs(aspect_ratio - 4/3) < 0.1:  # 4:3 aspect ratio
            if width > height:
                image_size = "landscape_4_3"
            else:
                image_size = "portrait_4_3"
            print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
        elif abs(aspect_ratio - 1) < 0.1:  # Square
            image_size = "square_hd"
            print(f"Using FAL AI preset: {image_size} for dimensions {width}x{height}")
        else:
            # For custom aspect ratios, use the width/height object format
            # Ensure dimensions are within FAL AI's limits
            max_dimension = 1024  # FAL AI typically has limits on dimensions

            # Scale dimensions to fit within limits while maintaining aspect ratio
            if width > height:
                if width > max_dimension:
                    scale = max_dimension / width
                    width = max_dimension
                    height = int(height * scale)
            else:
                if height > max_dimension:
                    scale = max_dimension / height
                    height = max_dimension
                    width = int(width * scale)

            # Use custom dimensions
            image_size = {
                "width": width,
                "height": height
            }
            print(f"Using custom dimensions for FAL AI: {width}x{height}")

        try:
            print(f"Submitting request to FAL AI with prompt: {prompt[:50]}...")
            handler = fal_client.submit(
                config["model"],
                arguments={
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "image_size": image_size,
                    "num_images": config["num_images"],
                    "num_inference_steps": config["num_inference_steps"],
                    "enable_safety_checker": config["enable_safety_checker"],
                },
            )

            print(f"Request submitted, waiting for result...")
            result = handler.get()
            if result and isinstance(result, dict) and "images" in result:
                images = result["images"]
                if isinstance(images, list) and images:
                    image_url = images[0].get("url")
                    if image_url:
                        print(f"Image URL received: {image_url[:50]}...")
                        # Ensure URL has proper protocol
                        if not image_url.startswith(('http://', 'https://')):
                            print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                            image_url = f"https://{image_url}"
                        return image_url

            print("No valid image URL found in FAL AI response")
            return None

        except Exception as e:
            print(f"Error in FAL AI request: {str(e)}")
            if "422" in str(e) or "400" in str(e):
                print("Error 422/400: This usually means the parameters are invalid.")
                print("Trying with predefined image size...")

                # Try again with a predefined image size based on orientation
                try:
                    # Use a predefined image size based on orientation
                    if width > height:
                        fallback_image_size = "landscape_16_9"
                    else:
                        fallback_image_size = "portrait_16_9"

                    print(f"Retrying with predefined image size: {fallback_image_size}")

                    handler = fal_client.submit(
                        config["model"],
                        arguments={
                            "prompt": prompt,
                            "negative_prompt": negative_prompt,
                            "image_size": fallback_image_size,
                            "num_images": config["num_images"],
                            "num_inference_steps": config["num_inference_steps"],
                            "enable_safety_checker": config["enable_safety_checker"],
                        },
                    )

                    result = handler.get()
                    if result and isinstance(result, dict) and "images" in result:
                        images = result["images"]
                        if isinstance(images, list) and images:
                            image_url = images[0].get("url")
                            if image_url:
                                print(f"Retry successful with predefined size: {fallback_image_size}")
                                # Ensure URL has proper protocol
                                if not image_url.startswith(('http://', 'https://')):
                                    print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                                    image_url = f"https://{image_url}"
                                return image_url
                except Exception as retry_error:
                    print(f"Retry with predefined size failed: {str(retry_error)}")

                    # Last resort: try without specifying image_size at all
                    try:
                        print("Trying without image_size parameter...")
                        handler = fal_client.submit(
                            config["model"],
                            arguments={
                                "prompt": prompt,
                                "negative_prompt": negative_prompt,
                                "num_images": config["num_images"],
                                "num_inference_steps": config["num_inference_steps"],
                                "enable_safety_checker": config["enable_safety_checker"],
                            },
                        )

                        result = handler.get()
                        if result and isinstance(result, dict) and "images" in result:
                            images = result["images"]
                            if isinstance(images, list) and images:
                                image_url = images[0].get("url")
                                if image_url:
                                    print("Fallback without image_size succeeded, but aspect ratio may be incorrect")
                                    # Ensure URL has proper protocol
                                    if not image_url.startswith(('http://', 'https://')):
                                        print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                                        image_url = f"https://{image_url}"
                                    return image_url
                    except Exception as last_error:
                        print(f"Final fallback also failed: {str(last_error)}")

            return None
    except Exception as e:
        print(f"Error in FAL AI API request: {e}")
        return None


def fal_flux_api(prompt: str, max_retries: int = 3, orientation: str = "portrait", video_quality: str = "720p") -> Optional[bytes]:
    """
    Generate an image using FAL AI's Flux model.

    This function can use either the FalAIImageService class or the direct submit_fal_request function.
    The service-based approach is preferred for better consistency and error handling.

    Args:
        prompt: The text prompt for image generation
        max_retries: Number of retry attempts if generation fails
        orientation: Image orientation (portrait or landscape)
        video_quality: Video quality setting (720p, 1080p, etc.)

    Returns:
        Image data as bytes or None if generation fails
    """
    # Log video quality and orientation
    print(f"Generating image with FAL AI, orientation: {orientation}, video quality: {video_quality}")

    # Try both approaches for maximum reliability
    # First try the service-based approach
    try:
        # Import the image service
        from services.image_service import get_image_service

        # Get dimensions based on video quality and orientation
        config = load_config()
        video_dimensions = config["video_resolutions"].get(video_quality, config["video_resolutions"]["720p"])
        dimensions = video_dimensions.get(orientation, video_dimensions["portrait"])
        width, height = dimensions["width"], dimensions["height"]

        # Get the FAL AI service
        fal_service = get_image_service("fal")

        # Try to generate the image with retries
        for attempt in range(max_retries):
            try:
                print(f"Using FalAIImageService (attempt {attempt+1}/{max_retries})")
                image_data = fal_service.generate_image(
                    prompt=prompt,
                    width=width,
                    height=height,
                    steps=4
                )

                if image_data:
                    return image_data
                else:
                    raise ValueError("No image data returned from FAL AI service")
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"Error in FAL AI service request (attempt {attempt + 1}/{max_retries}): {e}")
                    print("Retrying...")
                    time.sleep(1)  # Wait for 1 second before retrying
                else:
                    print(f"Error in FAL AI service after {max_retries} attempts: {e}")
                    print("Trying direct API approach as fallback...")

                    # If service-based approach fails, try the direct API approach
                    try:
                        config = load_config()
                        fal_config = config["fal_flux_api"]

                        # Call the submit_fal_request function with video_quality parameter
                        image_url = submit_fal_request(
                            prompt=prompt,
                            config=fal_config,
                            orientation=orientation,
                            video_quality=video_quality
                        )

                        if image_url:
                            # Ensure URL has proper protocol
                            if not image_url.startswith(('http://', 'https://')):
                                print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                                image_url = f"https://{image_url}"
                            response = requests.get(image_url)
                            response.raise_for_status()
                            return response.content
                    except Exception as direct_error:
                        print(f"Direct API approach also failed: {direct_error}")
    except Exception as outer_error:
        print(f"Error setting up FAL AI service: {outer_error}")
        print("Falling back to direct API approach...")

        # If service setup fails, try the direct API approach
        try:
            config = load_config()
            fal_config = config["fal_flux_api"]

            # Call the submit_fal_request function with video_quality parameter
            image_url = submit_fal_request(
                prompt=prompt,
                config=fal_config,
                orientation=orientation,
                video_quality=video_quality
            )

            if image_url:
                # Ensure URL has proper protocol
                if not image_url.startswith(('http://', 'https://')):
                    print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                    image_url = f"https://{image_url}"
                response = requests.get(image_url)
                response.raise_for_status()
                return response.content
        except Exception as direct_error:
            print(f"Direct API approach failed: {direct_error}")

    return None


def replicate_flux_api(prompt: str, max_retries: int = 3, orientation: str = "portrait", video_quality: str = "720p") -> Optional[bytes]:
    config = load_config()
    replicate_config = config["replicate_flux_api"]

    # Set API key from environment
    api_key = os.getenv("REPLICATE_API_KEY")
    if not api_key:
        raise ValueError("REPLICATE_API_KEY not found in environment variables")

    # Set the API key for Replicate
    os.environ["REPLICATE_API_TOKEN"] = api_key

    # Add a negative prompt to prevent text in the generated images
    negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

    # Get aspect ratio based on orientation - this is the key to making it work!
    aspect_ratio = replicate_config["aspect_ratio"].get(orientation, "9:16")

    # Log video quality and orientation
    print(f"Generating image with Replicate, orientation: {orientation}, video quality: {video_quality}")
    print(f"Using aspect ratio: {aspect_ratio}")

    # Create payload with aspect_ratio instead of width/height
    # This is how it was working in v5.5
    payload = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "aspect_ratio": aspect_ratio,  # Use aspect_ratio parameter directly
        "num_inference_steps": replicate_config["num_inference_steps"],
        "disable_safety_checker": replicate_config["disable_safety_checker"],
        "guidance": replicate_config["guidance"],
        "output_quality": replicate_config["output_quality"],
    }

    for attempt in range(max_retries):
        try:
            print(f"Calling Replicate API with model: {replicate_config['model']}")
            print(f"Payload: {payload}")

            image_urls = replicate.run(
                replicate_config["model"], input=payload
            )

            print(f"Replicate API response type: {type(image_urls)}")

            if image_urls and isinstance(image_urls, list) and len(image_urls) > 0:
                image_url = image_urls[0]
                print(f"Image URL received: {image_url[:50]}...")
                # Ensure URL has proper protocol
                if not image_url.startswith(('http://', 'https://')):
                    print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                    image_url = f"https://{image_url}"
                response = requests.get(image_url)
                response.raise_for_status()
                return response.content
            else:
                print(f"Unexpected response from Replicate API: {image_urls}")
                raise ValueError("No valid image URL returned from Replicate API")
        except Exception as e:
            error_message = str(e)
            print(f"Error in Replicate API call (attempt {attempt + 1}/{max_retries}): {error_message}")

            if attempt < max_retries - 1:
                # Try with simplified parameters on the next attempt
                if attempt == 0:
                    print("Simplifying parameters for next attempt...")
                    # Get dimensions based on orientation
                    config = load_config()
                    video_dimensions = config["video_resolutions"].get(video_quality, config["video_resolutions"]["720p"])
                    dimensions = video_dimensions.get(orientation, video_dimensions["portrait"])
                    width, height = dimensions["width"], dimensions["height"]

                    # Remove all parameters except the essential ones
                    payload = {
                        "prompt": prompt,
                        "negative_prompt": negative_prompt,
                        "width": width,
                        "height": height
                    }

                # Implement exponential backoff
                wait_time = 2 ** attempt  # 1, 2, 4, 8, etc. seconds
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                print(f"All {max_retries} attempts failed for Replicate API")

                # Try one last time with a different model as a last resort
                try:
                    print("Trying with alternative model as last resort...")
                    alt_model = "stability-ai/sdxl"  # Alternative model

                    # Determine if this is portrait or landscape based on orientation parameter
                    is_portrait_mode = orientation == "portrait"

                    # Ensure we're using the correct aspect ratio for the alternative model
                    # SDXL works better with specific dimensions
                    if is_portrait_mode:  # Portrait
                        # Standard portrait dimensions for SDXL
                        alt_width = 832
                        alt_height = 1216  # Approximately 9:16 ratio
                    else:  # Landscape
                        # Standard landscape dimensions for SDXL
                        alt_width = 1216
                        alt_height = 832  # Approximately 16:9 ratio

                    print(f"Using alternative model with dimensions: {alt_width}x{alt_height}, aspect ratio: {alt_width/alt_height:.2f}")

                    # Simplified payload for the alternative model
                    alt_payload = {
                        "prompt": prompt,
                        "negative_prompt": negative_prompt,
                        "width": alt_width,
                        "height": alt_height
                    }

                    image_urls = replicate.run(alt_model, input=alt_payload)

                    if image_urls and isinstance(image_urls, list) and len(image_urls) > 0:
                        image_url = image_urls[0]
                        print(f"Alternative model successful, image URL: {image_url[:50]}...")
                        response = requests.get(image_url)
                        response.raise_for_status()
                        return response.content
                except Exception as alt_error:
                    print(f"Alternative model also failed: {str(alt_error)}")

    print("All Replicate API attempts failed")
    return None


# Global variable to cache the Together client
_together_client = None

def get_together_client(api_key):
    """Get or create a cached Together client instance"""
    global _together_client
    if _together_client is None:
        try:
            from together import Together
            print(f"Initializing Together client with API key: {api_key[:5]}...{api_key[-5:]}")
            _together_client = Together(api_key=api_key)
        except Exception as e:
            print(f"Error initializing Together client: {e}")
            _together_client = None
    return _together_client

def runware_flux_api(prompt: str, max_retries: int = 3, orientation: str = "portrait", video_quality: str = "720p", model_type: str = "flux_dev") -> Optional[bytes]:
    """
    Generate an image using Runware.ai's models with the official SDK.

    Args:
        prompt: The text prompt for image generation
        max_retries: Number of retry attempts if generation fails
        orientation: Image orientation (portrait or landscape)
        video_quality: Video quality setting (720p, 1080p, etc.)
        model_type: The Runware.ai model to use (flux_dev or flex_schenele)

    Returns:
        Image data as bytes or None if generation fails
    """
    global _image_cache

    config = load_config()
    runware_config = config["runware_flux_api"]

    # Get dimensions based on orientation - ALWAYS use Runware.ai dimensions from config
    dimensions = runware_config["dimensions"].get(orientation, {})
    width = dimensions.get("width", 640)
    height = dimensions.get("height", 1152)

    # Log the dimensions from config
    print(f"Using Runware.ai dimensions from config: {width}x{height}")

    # IMPORTANT: Do NOT override dimensions with video_quality dimensions
    # This is the key difference from other image services
    if video_quality:
        print(f"NOTE: Ignoring video_quality dimensions for Runware.ai and using config dimensions: {width}x{height}")

    # Log video quality, orientation, and model
    model_id = runware_config["models"][model_type]["model_id"]
    model_name = runware_config["models"][model_type]["name"]
    print(f"Generating image with Runware.ai {model_name}, orientation: {orientation}, video quality: {video_quality}")

    # Create a cache key based on the prompt and configuration
    cache_key = hashlib.md5(f"{prompt}_{model_id}_{width}_{height}_{runware_config['steps']}_{orientation}_{video_quality}".encode()).hexdigest()

    # Check if we have this image in cache
    if cache_key in _image_cache:
        print(f"Using cached image for prompt: {prompt[:50]}...")
        return _image_cache[cache_key]

    # Get API key from environment variables
    api_key = os.getenv("RUNWARE_API_KEY")
    if not api_key:
        print("RUNWARE_API_KEY not found in environment variables")
        print("ERROR: Cannot use Runware.ai without an API key. Please set RUNWARE_API_KEY environment variable.")
        # Raise an exception instead of falling back to Replicate
        raise ValueError("RUNWARE_API_KEY not found in environment variables. Cannot use Runware.ai without an API key.")

    # Get negative prompt from config
    negative_prompt = runware_config.get("negative_prompt", "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp")

    for attempt in range(max_retries):
        try:
            print(f"Calling Runware.ai API with model: {model_id}")

            # Skip SDK and use direct API call
            print("Using direct API call for Runware.ai")
            return _runware_direct_api_call(
                prompt, api_key, model_id, width, height,
                runware_config, negative_prompt, cache_key,
                max_retries, attempt, orientation, video_quality
            )

        except Exception as e:
            error_message = str(e)
            if attempt < max_retries - 1:
                print(f"Error in Runware.ai API request (attempt {attempt + 1}/{max_retries}): {error_message}")
                # Implement exponential backoff: wait longer with each retry
                wait_time = 2 ** attempt  # 1, 2, 4, 8, etc. seconds
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                print(f"Error in Runware.ai API request after {max_retries} attempts: {error_message}")
                # Don't fall back to Replicate, raise an exception instead
                print("ERROR: Failed to generate image with Runware.ai after multiple attempts")
                raise Exception(f"Failed to generate image with Runware.ai after {max_retries} attempts: {error_message}")

    return None


def _runware_direct_api_call(prompt, api_key, model_id, width, height, runware_config, negative_prompt, cache_key, max_retries, attempt, orientation, video_quality):
    """
    Fallback function to make direct API calls to Runware.ai if the SDK is not available.
    """
    try:
        # API endpoint
        api_endpoint = "https://api.runware.ai/v1/inference"

        # Generate a random UUID for the task
        task_uuid = str(uuid.uuid4())

        # Determine the correct steps value based on the model_id
        if "101" in model_id:  # flux_dev model
            steps = 28  # Use 28 steps for Flux Dev model
            print(f"Using 28 steps for Flux Dev model (model_id: {model_id})")
        elif "102" in model_id:  # flex_schenele model
            steps = 4  # Use 4 steps for Flex Schenele model
            print(f"Using 4 steps for Flex Schenele model (model_id: {model_id})")
        else:
            # Use the default steps from config
            steps = runware_config["steps"]
            print(f"Using default steps ({steps}) for model_id: {model_id}")

        # Prepare the request payload according to Runware.ai API format
        payload = [{
            "taskType": "imageInference",
            "taskUUID": task_uuid,
            "positivePrompt": prompt,
            "negativePrompt": negative_prompt,
            "model": model_id,
            "width": width,
            "height": height,
            "steps": steps,  # Use the model-specific steps value
            "CFGScale": runware_config["guidance_scale"],
            "numberResults": runware_config["num_images"],
            "outputType": "base64Data",
            "outputFormat": runware_config.get("output_format", "PNG")
        }]

        print(f"Calling Runware.ai API with payload: {payload}")

        # Set up headers with API key
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # Make the API request
        response = requests.post(
            api_endpoint,
            headers=headers,
            json=payload
        )

        # Check if the request was successful
        response.raise_for_status()

        # Parse the response
        result = response.json()
        print(f"Runware.ai API response: {result}")

        # The response format is different from what we expected
        # According to the documentation, it should be in the format:
        # { "data": [ { "taskType": "imageInference", "taskUUID": "...", "imageBase64Data": "..." } ] }

        if "data" in result and len(result["data"]) > 0:
            # Get the task data
            task_data = result["data"][0]

            # Check if we have the image data
            if "imageBase64Data" in task_data:
                # Get the base64 encoded image
                image_b64 = task_data["imageBase64Data"]

                if image_b64:
                    # Decode the base64 image
                    image_data = base64.b64decode(image_b64)

                    # Add to cache
                    _image_cache[cache_key] = image_data

                    # Manage cache size
                    if len(_image_cache) > _MAX_CACHE_SIZE:
                        # Remove oldest entry
                        oldest_key = next(iter(_image_cache))
                        _image_cache.pop(oldest_key)

                    return image_data
                else:
                    print("No base64 image data found in response")
            else:
                print(f"No imageBase64Data found in response: {task_data}")
        else:
            print(f"Unexpected response format: {result}")

    except Exception as e:
        error_message = str(e)
        if attempt < max_retries - 1:
            print(f"Error in direct Runware.ai API request: {error_message}")
            # Don't fall back to Replicate, raise an exception instead
            print("ERROR: Failed to generate image with Runware.ai direct API call")
            raise Exception(f"Failed to generate image with Runware.ai direct API call: {error_message}")

    return None


def together_flux_api(prompt: str, max_retries: int = 3, orientation: str = "portrait", video_quality: str = "720p") -> Optional[bytes]:
    global _image_cache

    config = load_config()
    together_config = config["together_flux_api"]

    # Get dimensions based on orientation
    dimensions = together_config["dimensions"].get(orientation, {})
    width = dimensions.get("width", 720)
    height = dimensions.get("height", 1280)

    # Log video quality and orientation
    print(f"Generating image with Together AI, orientation: {orientation}, video quality: {video_quality}")

    # Create a cache key based on the prompt and configuration
    cache_key = hashlib.md5(f"{prompt}_{together_config['model']}_{width}_{height}_{together_config['steps']}_{orientation}_{video_quality}".encode()).hexdigest()

    # Check if we have this image in cache
    if cache_key in _image_cache:
        print(f"Using cached image for prompt: {prompt[:50]}...")
        return _image_cache[cache_key]

    # Get API key from environment variables
    api_key = os.getenv("TOGETHER_API_KEY")
    if not api_key:
        raise ValueError("TOGETHER_API_KEY not found in environment variables")

    try:
        from together import Together
    except ImportError:
        print("Together AI library not installed. Please install with: pip install together")
        print("Falling back to Replicate Flux API.")
        # Use replicate_flux_api as a fallback
        return replicate_flux_api(prompt, max_retries, orientation, video_quality)

    # Ensure width and height are multiples of 16 as required by Together AI
    # Round to nearest multiple of 16 (round down)
    width = (width // 16) * 16
    height = (height // 16) * 16

    print(f"Using dimensions: {width}x{height} (adjusted to be multiples of 16)")

    # Get the cached client or create a new one
    client = get_together_client(api_key)
    if client is None:
        print("Failed to initialize Together client, falling back to Replicate")
        return replicate_flux_api(prompt, max_retries, orientation, video_quality)

    for attempt in range(max_retries):
        try:
            print(f"Calling Together images.generate with prompt: {prompt[:50]}...")
            # Add a negative prompt to prevent text in the generated images
            negative_prompt = "text, words, letters, writing, captions, labels, watermark, signature, logo, title, subtitle, timestamp"

            # Use the correct API method for image generation
            response = client.images.generate(
                model=together_config["model"],
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                n=1,
                steps=together_config["steps"],
                timeout=60  # Add a timeout of 60 seconds
            )

            print(f"Response received: {type(response)}")

            if hasattr(response, 'data') and len(response.data) > 0:
                image_data = response.data[0]
                if hasattr(image_data, 'b64_json') and image_data.b64_json:
                    print("Using b64_json data")
                    image_bytes = base64.b64decode(image_data.b64_json)
                    # Store in cache for future use
                    _image_cache[cache_key] = image_bytes

                    # Limit cache size to prevent memory issues
                    if len(_image_cache) > _MAX_CACHE_SIZE:
                        # Remove oldest entry
                        oldest_key = next(iter(_image_cache))
                        del _image_cache[oldest_key]
                        print(f"Cache full, removed oldest entry. Cache size: {len(_image_cache)}")

                    return image_bytes
                elif hasattr(image_data, 'url') and image_data.url:
                    image_url = image_data.url
                    print(f"Using image URL: {image_url[:50]}...")
                    # Ensure URL has proper protocol
                    if not image_url.startswith(('http://', 'https://')):
                        print(f"URL missing protocol, adding https://: {image_url[:50]}...")
                        image_url = f"https://{image_url}"
                    img_response = requests.get(image_url)
                    img_response.raise_for_status()
                    image_bytes = img_response.content
                    # Store in cache for future use
                    _image_cache[cache_key] = image_bytes

                    # Limit cache size to prevent memory issues
                    if len(_image_cache) > _MAX_CACHE_SIZE:
                        # Remove oldest entry
                        oldest_key = next(iter(_image_cache))
                        del _image_cache[oldest_key]
                        print(f"Cache full, removed oldest entry. Cache size: {len(_image_cache)}")

                    return image_bytes

            # If we get here, we didn't find usable data in the response
            raise ValueError(f"No image data found in the response: {response}")

        except Exception as e:
            error_message = str(e)
            if attempt < max_retries - 1:
                print(f"Error in Together AI API request (attempt {attempt + 1}/{max_retries}): {error_message}")
                # Implement exponential backoff: wait longer with each retry
                wait_time = 2 ** attempt  # 1, 2, 4, 8, etc. seconds
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                print(f"Error in Together AI API request after {max_retries} attempts: {error_message}")
                # If all retries with Together API fail, fall back to Replicate
                print("Falling back to Replicate API after Together API failures")
                return replicate_flux_api(prompt, max_retries=2, orientation=orientation, video_quality=video_quality)
    return None

