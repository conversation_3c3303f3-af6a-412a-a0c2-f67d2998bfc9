import os, sys, shutil, subprocess 
 
def encrypt_file(input_path, output_path): 
    try: 
        print(f"Encrypting {input_path} with PyArmor full security...") 
        # Handle Windows paths correctly by normalizing them 
        input_path = os.path.normpath(input_path) 
        output_path = os.path.normpath(output_path) 
        # Ensure output directory exists 
        os.makedirs(os.path.dirname(output_path), exist_ok=True) 
        # Create a temporary directory for PyArmor output 
        temp_dir = f"temp_pyarmor_{os.path.basename(input_path).replace('.', '_')}" 
        if os.path.exists(temp_dir): 
            shutil.rmtree(temp_dir) 
        os.makedirs(temp_dir, exist_ok=True) 
        # Copy the input file to the temp directory 
        temp_file = os.path.join(temp_dir, os.path.basename(input_path)) 
        shutil.copy2(input_path, temp_file) 
        # Run PyArmor with full security mode 
        cmd = [ 
            sys.executable, 
            "-m", "pyarmor", "obfuscate", 
            "--exact",  # Only obfuscate the specified file 
            "--advanced", "2",  # Use advanced mode level 2 (highest security) 
            "--bootstrap", "3",  # Use bootstrap 3 for better protection 
            "--restrict", "0",  # No restrict mode 
            "--obf-code", "2",  # Obfuscate code objects (highest level) 
            "--obf-mod", "2",  # Obfuscate module (highest level) 
            "--wrap-mode", "1",  # Use wrap mode 1 for better protection 
            "--protection", "check-keyserver",  # Add keyserver protection 
            temp_file 
        ] 
        result = subprocess.run(cmd, capture_output=True, text=True) 
        if result.returncode  
            print(f"PyArmor error: {result.stderr}") 
            return False 
        # Copy the obfuscated file to the output path 
        dist_dir = os.path.join(temp_dir, "dist") 
        obfuscated_file = os.path.join(dist_dir, os.path.basename(input_path)) 
        if os.path.exists(obfuscated_file): 
            shutil.copy2(obfuscated_file, output_path) 
            # Clean up temporary files 
            shutil.rmtree(temp_dir) 
            return True 
        else: 
            print(f"Error: PyArmor did not generate the expected output file") 
            return False 
    except Exception as e: 
        print(f"Error in PyArmor encryption: {e}") 
        return False 
