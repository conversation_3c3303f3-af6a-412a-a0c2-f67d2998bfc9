"""
Utility script to check ElevenLabs API status and list available voices.
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add the parent directory to the path so we can import from src
script_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(script_dir)
sys.path.append(parent_dir)
sys.path.append(script_dir)  # Also add the current directory

# Define paths
dotenv_path = os.path.join(parent_dir, ".env")

def load_env_with_fallback():
    """Load environment variables with fallback locations"""
    # Try loading from root .env first
    load_dotenv(dotenv_path)
    
    # If not found in root, try src folder
    src_env = os.path.join(script_dir, ".env")
    if not os.getenv("ELEVENLABS_API_KEY") and os.path.exists(src_env):
        load_dotenv(src_env)
        
    # Try loading from OS environment as last resort
    api_key = os.getenv("ELEVENLABS_API_KEY")
    
    if not api_key:
        # Try to read directly from .env file if env var not set
        if os.path.exists(dotenv_path):
            try:
                with open(dotenv_path, 'r') as f:
                    for line in f:
                        if line.startswith('ELEVENLABS_API_KEY='):
                            api_key = line.strip().split('=', 1)[1].strip('"').strip("'")
                            os.environ["ELEVENLABS_API_KEY"] = api_key
                            break
            except Exception as e:
                print(f"Error reading .env file: {e}")
                
    return api_key

def check_elevenlabs_api():
    """Check if ElevenLabs API key is set and working"""
    try:
        # Load environment with enhanced fallback
        api_key = load_env_with_fallback()
        
        if not api_key:
            print("ElevenLabs API key not found!")
            print("\nTo fix this issue:")
            print("1. Make sure you have a .env file in either:")
            print(f"   - Root directory: {dotenv_path}")
            print(f"   - Src directory: {os.path.join(script_dir, '.env')}")
            print("2. Add your ElevenLabs API key: ELEVENLABS_API_KEY=your_key_here")
            print("3. Make sure there are no quotes or spaces around the key")
            print("4. Save the file and restart the application")
            print("\nYou can get an API key by signing up at https://elevenlabs.io/")
            return False, "API key not found"

        # Display masked key for verification
        masked_key = f"{api_key[:4]}...{api_key[-4:]}" if len(api_key) > 8 else "***"
        print(f"Found API key: {masked_key}")

        # Import the ElevenLabs client
        from elevenlabs_client import elevenlabs_client

        # Check if the client instance is properly initialized
        if not elevenlabs_client.api_key:
            print("Error: API key not properly loaded into ElevenLabs client")
            return False, "API key not loaded into client"

        # Check if client is available and validate API key
        if not elevenlabs_client.is_available:
            error_msg = getattr(elevenlabs_client, 'initialization_error', None) or "API key validation failed"
            print(f"ElevenLabs API not available: {error_msg}")
            print("\nPossible issues:")
            print("1. The API key may be invalid")
            print("2. Network connectivity problems")
            print("3. Rate limiting or account issues")
            print("\nPlease verify your API key at https://elevenlabs.io/")
            return False, error_msg

        # Try to get voices to verify API key is working
        voices = elevenlabs_client.get_voices()

        # Log the raw voice data for debugging
        print("\nDebug: Raw voice data received:", voices if voices else "No data")

        # Even if no voices are found, the key might be valid for a new account
        if not voices:
            print("ElevenLabs API key is valid but no voices were found.")
            print("This is normal if:")
            print("1. You have a new account without any voices")
            print("2. You haven't added any voices to your account")
            print("\nTo add voices to your account:")
            print("1. Visit https://elevenlabs.io/speech-synthesis")
            print("2. Click 'Add Voice' to create or clone voices")
            print("3. Refresh the application to see your new voices")
            return True, "API key valid (no voices found)"

        # API key is working and voices found
        print(f"ElevenLabs API key is valid. Found {len(voices)} voices.")

        # Print voice details
        print("\nAvailable voices:")
        for voice in voices:
            name = voice.get('name', 'Unknown')
            category = voice.get('category', 'unknown')
            voice_id = voice.get('voice_id', 'N/A')
            print(f"- {name} ({category}) [ID: {voice_id}]")

        return True, f"Found {len(voices)} voices"

    except Exception as e:
        print(f"Error checking ElevenLabs API: {str(e)}")
        import traceback
        print("\nDetailed error information:")
        print(traceback.format_exc())
        return False, f"Error: {str(e)}"

if __name__ == "__main__":
    # Check ElevenLabs API status
    status, message = check_elevenlabs_api()

    # Print status
    print(f"\nElevenLabs API Status: {'OK' if status else 'ERROR'}")
    print(f"Message: {message}")

    # Exit with appropriate code
    sys.exit(0 if status else 1)
